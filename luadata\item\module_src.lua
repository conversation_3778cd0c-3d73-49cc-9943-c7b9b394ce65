-- ./excel/item/module_src.xlsx
return {

    [1] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_1",
        go_inwar = 0,
        id = 1,
        name = "활약상점",
        op = "(활약상점 캐릭터 아이템)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [2] = {
        arg = "",
        blockkey = "shop",
        config = "shop_2_1",
        go_inwar = 0,
        id = 2,
        name = "골드상점",
        op = "(골드 룬 상점 파트너 아이템)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [3] = {
        arg = "",
        blockkey = "shop",
        config = "shop_3_1",
        go_inwar = 0,
        id = 3,
        name = "스킨상점",
        op = "(스킨상점 캐릭터 아이템)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [4] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_1",
        go_inwar = 0,
        id = 4,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [5] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2",
        go_inwar = 0,
        id = 5,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [6] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3",
        go_inwar = 0,
        id = 6,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [7] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4",
        go_inwar = 0,
        id = 7,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [9] = {
        arg = "",
        blockkey = "shop",
        config = "shop_5_1",
        go_inwar = 0,
        id = 9,
        name = "충전",
        op = "(RMB 충전)",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [11] = {
        arg = "",
        blockkey = "lilian",
        config = "daily_cultivate",
        go_inwar = 1,
        id = 11,
        name = "일일 수행",
        op = "일일 수행",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [12] = {
        arg = "",
        blockkey = "task",
        config = "task_cur",
        go_inwar = 0,
        id = 12,
        name = "서브퀘스트",
        op = "서브퀘스트",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [13] = {
        arg = "",
        blockkey = "task",
        config = "task_cur",
        go_inwar = 0,
        id = 13,
        name = "메인퀘스트",
        op = "메인퀘스트",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [14] = {
        arg = "",
        blockkey = "schedule",
        config = "schedule",
        go_inwar = 0,
        id = 14,
        name = "활약보상",
        op = "일일 활약",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [21] = {
        arg = "",
        blockkey = "org",
        config = "org",
        go_inwar = 0,
        id = 21,
        name = "길 드",
        op = "길드 기능",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [22] = {
        arg = "",
        blockkey = "trapmine",
        config = "anlei",
        go_inwar = 1,
        id = 22,
        name = "탐색",
        op = "팝업 탐색 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [23] = {
        arg = "",
        blockkey = "minglei",
        config = "",
        go_inwar = 1,
        id = 23,
        name = "야옹이 다과회",
        op = "글자 팝업 ",
        switch_type = 0,
        tips = "매일 정각에 단혼애,풍죽림,월견도,팔문촌,광석의 도시에 귀빈들이 출현합니다 ~",
        type = 3,
    },

    [24] = {
        arg = "",
        blockkey = "trapmine",
        config = "anlei",
        go_inwar = 1,
        id = 24,
        name = "정예몹",
        op = "팝업 탐색 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [25] = {
        arg = "",
        blockkey = "trapmine",
        config = "anlei",
        go_inwar = 1,
        id = 25,
        name = "보물사냥꾼",
        op = "팝업 탐색 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [31] = {
        arg = "",
        blockkey = "house",
        config = "house",
        go_inwar = 1,
        id = 31,
        name = "저 택",
        op = "저택 기능",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [32] = {
        arg = "",
        blockkey = "",
        config = "partner_equip_compose",
        go_inwar = 0,
        id = 32,
        name = "룬 정련",
        op = "룬 합성 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [33] = {
        arg = "",
        blockkey = "shop",
        config = "exchange_coin",
        go_inwar = 0,
        id = 33,
        name = "골드교환",
        op = "수정으로 골드를 구매하는 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [41] = {
        arg = "",
        blockkey = "pata",
        config = "pata",
        go_inwar = 1,
        id = 41,
        name = "감옥",
        op = "감옥 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [42] = {
        arg = "",
        blockkey = "worldboss",
        config = "world_boss",
        go_inwar = 1,
        id = 42,
        name = "봉인된 곳",
        op = "보스 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [43] = {
        arg = "",
        blockkey = "question",
        config = "quesion_answer",
        go_inwar = 1,
        id = 43,
        name = "꼴지의역습",
        op = "꼴지의역습 활성(활성화 못하면 글자가 팝업으로 표시:이벤트 미오픈)",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [44] = {
        arg = "",
        blockkey = "treasure",
        config = "",
        go_inwar = 1,
        id = 44,
        name = "성상도",
        op = "보물 지도",
        switch_type = 0,
        tips = '활약도 및 원정 포인트 교환으로 "성상도”를 획득할 수 있다',
        type = 3,
    },

    [45] = {
        arg = "",
        blockkey = "equipfuben",
        config = "equip_fuben",
        go_inwar = 1,
        id = 45,
        name = "장비던전",
        op = "장비던전",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [46] = {
        arg = "",
        blockkey = "pefuben",
        config = "pefuben",
        go_inwar = 1,
        id = 46,
        name = "룬던전",
        op = "룬던전",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [47] = {
        arg = "",
        blockkey = "arenagame",
        config = "arena",
        go_inwar = 1,
        id = 47,
        name = "티어대련장",
        op = "티어대련장",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [48] = {
        arg = "",
        blockkey = "endless_pve",
        config = "endless_pve",
        go_inwar = 1,
        id = 48,
        name = "월견 던전",
        op = "월견 던전",
        switch_type = 0,
        tips = "활약도 보상으로 “경화수월”던전을 오픈할 수 있다",
        type = 1,
    },

    [49] = {
        arg = "",
        blockkey = "terrawars",
        config = "terrawars",
        go_inwar = 1,
        id = 49,
        name = "거점전",
        op = "거점전",
        switch_type = 0,
        tips = "길드 가입 시 거점전에 참가할 수 있다",
        type = 1,
    },

    [51] = {
        arg = "",
        blockkey = "",
        config = "gemmerge_1",
        go_inwar = 0,
        id = 51,
        name = "보석합성",
        op = "보석합성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [52] = {
        arg = "",
        blockkey = "",
        config = "gemmerge_2",
        go_inwar = 0,
        id = 52,
        name = "보석합성",
        op = "보석합성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [53] = {
        arg = "",
        blockkey = "",
        config = "gemmerge_3",
        go_inwar = 0,
        id = 53,
        name = "보석합성",
        op = "보석합성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [54] = {
        arg = "",
        blockkey = "",
        config = "gemmerge_4",
        go_inwar = 0,
        id = 54,
        name = "보석합성",
        op = "보석합성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [55] = {
        arg = "",
        blockkey = "",
        config = "gemmerge_5",
        go_inwar = 0,
        id = 55,
        name = "보석합성",
        op = "보석합성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [56] = {
        arg = "",
        blockkey = "",
        config = "gemmerge_6",
        go_inwar = 0,
        id = 56,
        name = "보석합성",
        op = "보석합성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [57] = {
        arg = "",
        blockkey = "",
        config = "forge_strength",
        go_inwar = 0,
        id = 57,
        name = "장비 돌파",
        op = "장비 돌파 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [61] = {
        arg = "",
        blockkey = "draw_card",
        config = "card",
        go_inwar = 0,
        id = 61,
        name = "파트너모집",
        op = "파트너모집",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [100] = {
        arg = "",
        blockkey = "",
        config = "",
        go_inwar = 0,
        id = 100,
        name = "충 전",
        op = "충전 이벤트",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [101] = {
        arg = "",
        blockkey = "loginreward",
        config = "loginreward",
        go_inwar = 0,
        id = 101,
        name = "로그인 선물",
        op = "로그인 선물",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [102] = {
        arg = "",
        blockkey = "",
        config = "",
        go_inwar = 0,
        id = 102,
        name = "출석보상",
        op = "출석보상",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [151] = {
        arg = "",
        blockkey = "mapbook",
        config = "map_book_partner_equip",
        go_inwar = 1,
        id = 151,
        name = "도감 룬",
        op = "파트너 룬 도감",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [152] = {
        arg = "",
        blockkey = "travel",
        config = "travel",
        go_inwar = 1,
        id = 152,
        name = "원 정",
        op = "파트너 원정",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [153] = {
        arg = "",
        blockkey = "daily_task",
        config = "daily_task",
        go_inwar = 1,
        id = 153,
        name = "일일퀘스트",
        op = "일일퀘스트 태그",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [154] = {
        arg = "",
        blockkey = "org_talk",
        config = "org_talk",
        go_inwar = 1,
        id = 154,
        name = "의사당",
        op = "길드 의사당",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [155] = {
        arg = "5008",
        blockkey = "yjfuben",
        config = "yjfuben",
        go_inwar = 1,
        id = 155,
        name = "악몽사냥",
        op = "몽염 던전 ",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 2,
    },

    [156] = {
        arg = "",
        blockkey = "fieldboss",
        config = "fieldboss",
        go_inwar = 1,
        id = 156,
        name = "인형토벌",
        op = "야외 보스 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [157] = {
        arg = "",
        blockkey = "",
        config = "school_skill",
        go_inwar = 0,
        id = 157,
        name = "스킬",
        op = "스킬 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [158] = {
        arg = "",
        blockkey = "",
        config = "partner_upgrade",
        go_inwar = 0,
        id = 158,
        name = "파트너 레벨업",
        op = "파트너 레벨업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [159] = {
        arg = "",
        blockkey = "org",
        config = "org_build",
        go_inwar = 1,
        id = 159,
        name = "길드 건설",
        op = "길드 건설 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [160] = {
        arg = "",
        blockkey = "org",
        config = "org_redbag",
        go_inwar = 1,
        id = 160,
        name = "길드보너스",
        op = "길드보너스 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [161] = {
        arg = "",
        blockkey = "org",
        config = "org_wish",
        go_inwar = 1,
        id = 161,
        name = "길드소원",
        op = "길드소원 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [162] = {
        arg = "",
        blockkey = "org",
        config = "org_fuben",
        go_inwar = 1,
        id = 162,
        name = "길드현상",
        op = "길드현상 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [163] = {
        arg = "",
        blockkey = "mapbook",
        config = "map_book_partner_book",
        go_inwar = 1,
        id = 163,
        name = "파트너 전기",
        op = "도감 파트너 전기 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [164] = {
        arg = "",
        blockkey = "",
        config = "forge_fuwen",
        go_inwar = 0,
        id = 164,
        name = "장비쉬령",
        op = "쉬령 장비 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [165] = {
        arg = "",
        blockkey = "",
        config = "forge_composite",
        go_inwar = 0,
        id = 165,
        name = "장비단조",
        op = "장비단조 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [166] = {
        arg = "5006",
        blockkey = "pefuben",
        config = "pefuben",
        go_inwar = 1,
        id = 166,
        name = "룬던전\n$time1~3층",
        op = "이공유배 ",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 2,
    },

    [167] = {
        arg = "",
        blockkey = "trapmine",
        config = "anlei",
        go_inwar = 1,
        id = 167,
        name = "탐색\n야외탐색으로 해당 룬을 획득할 수 있다",
        op = "팝업 탐색 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [168] = {
        arg = "",
        blockkey = "travel",
        config = "travel_exchange",
        go_inwar = 0,
        id = 168,
        name = "원정교환",
        op = "원정 포인트 상점 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [169] = {
        arg = "",
        blockkey = "LimitReward",
        config = "limitreward_limitdraw",
        go_inwar = 0,
        id = 169,
        name = "행운룰렛",
        op = "행운룰렛 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [170] = {
        arg = "",
        blockkey = "LimitReward",
        config = "limitreward_costscore",
        go_inwar = 0,
        id = 170,
        name = "소비포인트",
        op = "소비포인트 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [171] = {
        arg = "",
        blockkey = "welfare",
        config = "welfare_yueka",
        go_inwar = 0,
        id = 171,
        name = "월정액",
        op = "월정액 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [172] = {
        arg = "",
        blockkey = "welfare",
        config = "welfare_czjj",
        go_inwar = 0,
        id = 172,
        name = "성장기금",
        op = "성장기금 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [173] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "chapterfuben",
        go_inwar = 1,
        id = 173,
        name = "전투",
        op = "전투 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [174] = {
        arg = "",
        blockkey = "teampvp",
        config = "teampvp",
        go_inwar = 1,
        id = 174,
        name = "협동대련장",
        op = "협동대련장 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [175] = {
        arg = "",
        blockkey = "equalarena",
        config = "equalarena",
        go_inwar = 1,
        id = 175,
        name = "공평대련장",
        op = "공평대련장 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [176] = {
        arg = "",
        blockkey = "achieve",
        config = "achieve",
        go_inwar = 0,
        id = 176,
        name = "업적",
        op = "업적 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [177] = {
        arg = "",
        blockkey = "MonsterAtk",
        config = "monsteratkcity",
        go_inwar = 1,
        id = 177,
        name = "제국침공",
        op = "몬스터 공성 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [178] = {
        arg = "5019",
        blockkey = "",
        config = "convoy",
        go_inwar = 1,
        id = 178,
        name = "제국기사단",
        op = "길을 찾아 제국기사단에 가다",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 2,
    },

    [179] = {
        arg = "5001",
        blockkey = "",
        config = "newshimen",
        go_inwar = 1,
        id = 179,
        name = "순찰잡무",
        op = "길을 찾아 잡무 순찰을 하다",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 2,
    },

    [180] = {
        arg = "",
        blockkey = "OnlineGift",
        config = "online_gift",
        go_inwar = 0,
        id = 180,
        name = "로그인 선물",
        op = "접속 보상 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [181] = {
        arg = "",
        blockkey = "org",
        config = "org_fuli",
        go_inwar = 1,
        id = 181,
        name = "길드축복",
        op = "길드축복 인터페이스 팝업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [182] = {
        arg = "",
        blockkey = "",
        config = "org_fuli",
        go_inwar = 1,
        id = 182,
        name = "퀴즈",
        op = "팝업 단어 고정",
        switch_type = 0,
        tips = "매일 정오 12시에 꼴지의 역습과 모범생 어디가 플레이할 수 있다 ",
        type = 3,
    },

    [183] = {
        arg = "",
        blockkey = "soulbox",
        config = "soulbox",
        go_inwar = 1,
        id = 183,
        name = "영혼 보물 상자",
        op = "정각과 반시간마다 제두의 각 장소에서 영혼 보물 상자가 출현한다",
        switch_type = 0,
        tips = "정각과 반시간마다 제두의 각 장소에서 영혼 보물 상자가 출현한다",
        type = 1,
    },

    [184] = {
        arg = "",
        blockkey = "",
        config = "partner_huntsoul",
        go_inwar = 0,
        id = 184,
        name = "환령",
        op = "사냥 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [185] = {
        arg = "",
        blockkey = "",
        config = "partner_stone_compose",
        go_inwar = 0,
        id = 185,
        name = "룬석합성",
        op = "룬석합성 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [186] = {
        arg = "",
        blockkey = "",
        config = "org_war",
        go_inwar = 1,
        id = 186,
        name = "길드전",
        op = "길드전 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [187] = {
        arg = "",
        blockkey = "",
        config = "arena_club",
        go_inwar = 1,
        id = 187,
        name = "무관도전",
        op = "무관도전 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [188] = {
        arg = "",
        blockkey = "",
        config = "marry",
        go_inwar = 1,
        id = 188,
        name = "연인시스템",
        op = "연인시스템 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [189] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_1",
        go_inwar = 1,
        id = 189,
        name = "전투 제1장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [190] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_2",
        go_inwar = 1,
        id = 190,
        name = "전투 제2장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [191] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_3",
        go_inwar = 1,
        id = 191,
        name = "전투 제3장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [192] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_4",
        go_inwar = 1,
        id = 192,
        name = "전투 제4장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [193] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_5",
        go_inwar = 1,
        id = 193,
        name = "전투 제5장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [194] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_6",
        go_inwar = 1,
        id = 194,
        name = "전투 제6장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [195] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_7",
        go_inwar = 1,
        id = 195,
        name = "전투 제7장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [196] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_8",
        go_inwar = 1,
        id = 196,
        name = "전투 제8장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [197] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_9",
        go_inwar = 1,
        id = 197,
        name = "전투 제9장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [198] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_10",
        go_inwar = 1,
        id = 198,
        name = "전투 제10장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [199] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_11",
        go_inwar = 1,
        id = 199,
        name = "전투 제11장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [200] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_12",
        go_inwar = 1,
        id = 200,
        name = "전투 제12장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [201] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_13",
        go_inwar = 1,
        id = 201,
        name = "전투 제13장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [202] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_14",
        go_inwar = 1,
        id = 202,
        name = "전투 제14장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [203] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_15",
        go_inwar = 1,
        id = 203,
        name = "전투 제15장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [204] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_16",
        go_inwar = 1,
        id = 204,
        name = "전투 제16장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [205] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_17",
        go_inwar = 1,
        id = 205,
        name = "전투 제17장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [206] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_18",
        go_inwar = 1,
        id = 206,
        name = "전투 제18장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [207] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_19",
        go_inwar = 1,
        id = 207,
        name = "전투 제19장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [208] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_20",
        go_inwar = 1,
        id = 208,
        name = "전투 제20장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [209] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_21",
        go_inwar = 1,
        id = 209,
        name = "전투 제21장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [210] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_22",
        go_inwar = 1,
        id = 210,
        name = "전투 제22장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [211] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_23",
        go_inwar = 1,
        id = 211,
        name = "전투 제23장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [212] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_24",
        go_inwar = 1,
        id = 212,
        name = "전투 제24장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [213] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_25",
        go_inwar = 1,
        id = 213,
        name = "전투 제25장",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [214] = {
        arg = "",
        blockkey = "",
        config = "partner_upstar",
        go_inwar = 0,
        id = 214,
        name = "파트너 승성",
        op = "파트너 레벨업",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [215] = {
        arg = "",
        blockkey = "",
        config = "partner_upskill",
        go_inwar = 0,
        id = 215,
        name = "파트너 스킬강화",
        op = "파트너 스킬강화",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [216] = {
        arg = "",
        blockkey = "",
        config = "partner_awake",
        go_inwar = 0,
        id = 216,
        name = "파트너 각성",
        op = "파트너 각성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [217] = {
        arg = "",
        blockkey = "",
        config = "partner_equip_upgrade",
        go_inwar = 0,
        id = 217,
        name = "룬강화",
        op = "룬강화",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [218] = {
        arg = "",
        blockkey = "",
        config = "partner_equip_upstar",
        go_inwar = 0,
        id = 218,
        name = "룬승성",
        op = "룬승성",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [219] = {
        arg = "",
        blockkey = "",
        config = "partner_equip_upstone",
        go_inwar = 0,
        id = 219,
        name = "룬석",
        op = "룬석",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [220] = {
        arg = "",
        blockkey = "",
        config = "travel_item",
        go_inwar = 0,
        id = 220,
        name = "원정아이템",
        op = "원정아이템",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [300] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_1_2",
        go_inwar = 1,
        id = 300,
        name = "전투1-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [301] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_1_4",
        go_inwar = 1,
        id = 301,
        name = "전투1-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [302] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_1_6",
        go_inwar = 1,
        id = 302,
        name = "전투1-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [303] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_1_8",
        go_inwar = 1,
        id = 303,
        name = "전투1-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [304] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_2_2",
        go_inwar = 1,
        id = 304,
        name = "전투2-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [305] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_2_4",
        go_inwar = 1,
        id = 305,
        name = "전투2-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [306] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_2_6",
        go_inwar = 1,
        id = 306,
        name = "전투2-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [307] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_2_8",
        go_inwar = 1,
        id = 307,
        name = "전투2-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [308] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_3_2",
        go_inwar = 1,
        id = 308,
        name = "전투3-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [309] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_3_4",
        go_inwar = 1,
        id = 309,
        name = "전투3-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [310] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_3_6",
        go_inwar = 1,
        id = 310,
        name = "전투3-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [311] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_3_8",
        go_inwar = 1,
        id = 311,
        name = "전투3-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [312] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_4_2",
        go_inwar = 1,
        id = 312,
        name = "전투4-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [313] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_4_4",
        go_inwar = 1,
        id = 313,
        name = "전투4-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [314] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_4_6",
        go_inwar = 1,
        id = 314,
        name = "전투4-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [315] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_4_8",
        go_inwar = 1,
        id = 315,
        name = "전투4-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [316] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_5_2",
        go_inwar = 1,
        id = 316,
        name = "전투5-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [317] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_5_4",
        go_inwar = 1,
        id = 317,
        name = "전투5-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [318] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_5_6",
        go_inwar = 1,
        id = 318,
        name = "전투5-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [319] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_5_8",
        go_inwar = 1,
        id = 319,
        name = "전투5-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [320] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_6_2",
        go_inwar = 1,
        id = 320,
        name = "전투6-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [321] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_6_4",
        go_inwar = 1,
        id = 321,
        name = "전투6-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [322] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_6_6",
        go_inwar = 1,
        id = 322,
        name = "전투6-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [323] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_6_8",
        go_inwar = 1,
        id = 323,
        name = "전투6-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [324] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_7_2",
        go_inwar = 1,
        id = 324,
        name = "전투7-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [325] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_7_4",
        go_inwar = 1,
        id = 325,
        name = "전투7-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [326] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_7_6",
        go_inwar = 1,
        id = 326,
        name = "전투7-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [327] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_7_8",
        go_inwar = 1,
        id = 327,
        name = "전투7-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [328] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_8_2",
        go_inwar = 1,
        id = 328,
        name = "전투8-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [329] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_8_4",
        go_inwar = 1,
        id = 329,
        name = "전투8-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [330] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_8_6",
        go_inwar = 1,
        id = 330,
        name = "전투8-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [331] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_8_8",
        go_inwar = 1,
        id = 331,
        name = "전투8-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [332] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_9_2",
        go_inwar = 1,
        id = 332,
        name = "전투9-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [333] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_9_4",
        go_inwar = 1,
        id = 333,
        name = "전투9-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [334] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_9_6",
        go_inwar = 1,
        id = 334,
        name = "전투9-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [335] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_9_8",
        go_inwar = 1,
        id = 335,
        name = "전투9-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [336] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_10_2",
        go_inwar = 1,
        id = 336,
        name = "전투10-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [337] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_10_4",
        go_inwar = 1,
        id = 337,
        name = "전투10-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [338] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_10_6",
        go_inwar = 1,
        id = 338,
        name = "전투10-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [339] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_10_8",
        go_inwar = 1,
        id = 339,
        name = "전투10-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [340] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_11_2",
        go_inwar = 1,
        id = 340,
        name = "전투11-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [341] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_11_4",
        go_inwar = 1,
        id = 341,
        name = "전투11-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [342] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_11_6",
        go_inwar = 1,
        id = 342,
        name = "전투11-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [343] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_11_8",
        go_inwar = 1,
        id = 343,
        name = "전투11-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [344] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_12_2",
        go_inwar = 1,
        id = 344,
        name = "전투12-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [345] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_12_4",
        go_inwar = 1,
        id = 345,
        name = "전투12-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [346] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_12_6",
        go_inwar = 1,
        id = 346,
        name = "전투12-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [347] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_12_8",
        go_inwar = 1,
        id = 347,
        name = "전투12-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [348] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_13_2",
        go_inwar = 1,
        id = 348,
        name = "전투13-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [349] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_13_4",
        go_inwar = 1,
        id = 349,
        name = "전투13-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [350] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_13_6",
        go_inwar = 1,
        id = 350,
        name = "전투13-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [351] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_13_8",
        go_inwar = 1,
        id = 351,
        name = "전투13-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [352] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_14_2",
        go_inwar = 1,
        id = 352,
        name = "전투14-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [353] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_14_4",
        go_inwar = 1,
        id = 353,
        name = "전투14-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [354] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_14_6",
        go_inwar = 1,
        id = 354,
        name = "전투14-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [355] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_14_8",
        go_inwar = 1,
        id = 355,
        name = "전투14-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [356] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_15_2",
        go_inwar = 1,
        id = 356,
        name = "전투15-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [357] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_15_4",
        go_inwar = 1,
        id = 357,
        name = "전투15-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [358] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_15_6",
        go_inwar = 1,
        id = 358,
        name = "전투15-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [359] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_15_8",
        go_inwar = 1,
        id = 359,
        name = "전투15-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [360] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_16_2",
        go_inwar = 1,
        id = 360,
        name = "전투16-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [361] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_16_4",
        go_inwar = 1,
        id = 361,
        name = "전투16-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [362] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_16_6",
        go_inwar = 1,
        id = 362,
        name = "전투16-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [363] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_16_8",
        go_inwar = 1,
        id = 363,
        name = "전투16-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [364] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_17_2",
        go_inwar = 1,
        id = 364,
        name = "전투17-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [365] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_17_4",
        go_inwar = 1,
        id = 365,
        name = "전투17-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [366] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_17_6",
        go_inwar = 1,
        id = 366,
        name = "전투17-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [367] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_17_8",
        go_inwar = 1,
        id = 367,
        name = "전투17-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [368] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_18_2",
        go_inwar = 1,
        id = 368,
        name = "전투18-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [369] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_18_4",
        go_inwar = 1,
        id = 369,
        name = "전투18-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [370] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_18_6",
        go_inwar = 1,
        id = 370,
        name = "전투18-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [371] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_18_8",
        go_inwar = 1,
        id = 371,
        name = "전투18-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [372] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_19_2",
        go_inwar = 1,
        id = 372,
        name = "전투19-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [373] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_19_4",
        go_inwar = 1,
        id = 373,
        name = "전투19-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [374] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_19_6",
        go_inwar = 1,
        id = 374,
        name = "전투19-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [375] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_19_8",
        go_inwar = 1,
        id = 375,
        name = "전투19-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [376] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_20_2",
        go_inwar = 1,
        id = 376,
        name = "전투20-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [377] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_20_4",
        go_inwar = 1,
        id = 377,
        name = "전투20-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [378] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_20_6",
        go_inwar = 1,
        id = 378,
        name = "전투20-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [379] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_20_8",
        go_inwar = 1,
        id = 379,
        name = "전투20-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [380] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_21_2",
        go_inwar = 1,
        id = 380,
        name = "전투21-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [381] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_21_4",
        go_inwar = 1,
        id = 381,
        name = "전투21-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [382] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_21_6",
        go_inwar = 1,
        id = 382,
        name = "전투21-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [383] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_21_8",
        go_inwar = 1,
        id = 383,
        name = "전투21-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [384] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_22_2",
        go_inwar = 1,
        id = 384,
        name = "전투22-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [385] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_22_4",
        go_inwar = 1,
        id = 385,
        name = "전투22-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [386] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_22_6",
        go_inwar = 1,
        id = 386,
        name = "전투22-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [387] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_22_8",
        go_inwar = 1,
        id = 387,
        name = "전투22-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [388] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_23_2",
        go_inwar = 1,
        id = 388,
        name = "전투23-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [389] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_23_4",
        go_inwar = 1,
        id = 389,
        name = "전투23-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [390] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_23_6",
        go_inwar = 1,
        id = 390,
        name = "전투23-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [391] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_23_8",
        go_inwar = 1,
        id = 391,
        name = "전투23-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [392] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_24_2",
        go_inwar = 1,
        id = 392,
        name = "전투24-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [393] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_24_4",
        go_inwar = 1,
        id = 393,
        name = "전투24-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [394] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_24_6",
        go_inwar = 1,
        id = 394,
        name = "전투24-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [395] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_24_8",
        go_inwar = 1,
        id = 395,
        name = "전투24-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [396] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_25_2",
        go_inwar = 1,
        id = 396,
        name = "전투25-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [397] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_25_4",
        go_inwar = 1,
        id = 397,
        name = "전투25-4",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [398] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_25_6",
        go_inwar = 1,
        id = 398,
        name = "전투25-6",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [399] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterfuben_25_8",
        go_inwar = 1,
        id = 399,
        name = "전투25-8",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [400] = {
        arg = "",
        blockkey = "",
        config = "partner_chip",
        go_inwar = 0,
        id = 400,
        name = "파트너 조각 사용",
        op = "파트너 조각 사용",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [401] = {
        arg = "",
        blockkey = "",
        config = "total_pay",
        go_inwar = 0,
        id = 401,
        name = "누적 충전",
        op = "누적 충전",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [402] = {
        arg = "",
        blockkey = "",
        config = "partner_equip_base",
        go_inwar = 0,
        id = 402,
        name = "파트너 룬",
        op = "파트너 룬",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [403] = {
        arg = "",
        blockkey = "",
        config = "partner_soul",
        go_inwar = 0,
        id = 403,
        name = "파트너 어령",
        op = "파트너 어령",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [404] = {
        arg = "",
        blockkey = "",
        config = "partner_skin",
        go_inwar = 0,
        id = 404,
        name = "파트너 스킨",
        op = "파트너 스킨",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [405] = {
        arg = "",
        blockkey = "",
        config = "partner_main_base",
        go_inwar = 0,
        id = 405,
        name = "파트너 인터페이스",
        op = "파트너 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [406] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_1_208005",
        go_inwar = 0,
        id = 406,
        name = "활약상점",
        op = "(활약상점 캐릭터 아이템)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [407] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_1_208006",
        go_inwar = 0,
        id = 407,
        name = "활약상점",
        op = "(활약상점 캐릭터 아이템)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [408] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_1_208007",
        go_inwar = 0,
        id = 408,
        name = "활약상점",
        op = "(활약상점 캐릭터 아이템)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [409] = {
        arg = "",
        blockkey = "shop",
        config = "shop_2_1_203001",
        go_inwar = 0,
        id = 409,
        name = "골드상점",
        op = "(골드 룬 상점 파트너 아이템)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [410] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_1_212003",
        go_inwar = 0,
        id = 410,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [411] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_1_212004",
        go_inwar = 0,
        id = 411,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [412] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_1_212027",
        go_inwar = 0,
        id = 412,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [413] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205001",
        go_inwar = 0,
        id = 413,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [414] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205002",
        go_inwar = 0,
        id = 414,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [415] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205003",
        go_inwar = 0,
        id = 415,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [416] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205004",
        go_inwar = 0,
        id = 416,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [417] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205005",
        go_inwar = 0,
        id = 417,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [418] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205006",
        go_inwar = 0,
        id = 418,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [419] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205007",
        go_inwar = 0,
        id = 419,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [420] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205008",
        go_inwar = 0,
        id = 420,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [421] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205009",
        go_inwar = 0,
        id = 421,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [422] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205010",
        go_inwar = 0,
        id = 422,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [423] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205011",
        go_inwar = 0,
        id = 423,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [424] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205012",
        go_inwar = 0,
        id = 424,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [425] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205013",
        go_inwar = 0,
        id = 425,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [426] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205014",
        go_inwar = 0,
        id = 426,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [427] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_3_205015",
        go_inwar = 0,
        id = 427,
        name = "명예상점",
        op = "(명예상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [428] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207001",
        go_inwar = 0,
        id = 428,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [429] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207002",
        go_inwar = 0,
        id = 429,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [430] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207003",
        go_inwar = 0,
        id = 430,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [431] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207004",
        go_inwar = 0,
        id = 431,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [432] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207005",
        go_inwar = 0,
        id = 432,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [433] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207006",
        go_inwar = 0,
        id = 433,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [434] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207007",
        go_inwar = 0,
        id = 434,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [435] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207008",
        go_inwar = 0,
        id = 435,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [436] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207009",
        go_inwar = 0,
        id = 436,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [437] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207010",
        go_inwar = 0,
        id = 437,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [438] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207011",
        go_inwar = 0,
        id = 438,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [439] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207012",
        go_inwar = 0,
        id = 439,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [440] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207013",
        go_inwar = 0,
        id = 440,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [441] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_4_207014",
        go_inwar = 0,
        id = 441,
        name = "공헌상점",
        op = "길드상점",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [442] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_1_212027",
        go_inwar = 0,
        id = 442,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [443] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204026",
        go_inwar = 0,
        id = 443,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [444] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204027",
        go_inwar = 0,
        id = 444,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [445] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204028",
        go_inwar = 0,
        id = 445,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [446] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204029",
        go_inwar = 0,
        id = 446,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [447] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204030",
        go_inwar = 0,
        id = 447,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [448] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201003",
        go_inwar = 0,
        id = 448,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [449] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201004",
        go_inwar = 0,
        id = 449,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [450] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2",
        go_inwar = 0,
        id = 450,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [451] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2",
        go_inwar = 0,
        id = 451,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [452] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3",
        go_inwar = 0,
        id = 452,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [453] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3",
        go_inwar = 0,
        id = 453,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [454] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3",
        go_inwar = 0,
        id = 454,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [455] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202001",
        go_inwar = 0,
        id = 455,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [456] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201005",
        go_inwar = 0,
        id = 456,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [457] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201006",
        go_inwar = 0,
        id = 457,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [458] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201007",
        go_inwar = 0,
        id = 458,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [459] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201008",
        go_inwar = 0,
        id = 459,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [460] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201009",
        go_inwar = 0,
        id = 460,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [461] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_2_201010",
        go_inwar = 0,
        id = 461,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [462] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202002",
        go_inwar = 0,
        id = 462,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [463] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202003",
        go_inwar = 0,
        id = 463,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [464] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202004",
        go_inwar = 0,
        id = 464,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [465] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202005",
        go_inwar = 0,
        id = 465,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [466] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202006",
        go_inwar = 0,
        id = 466,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [467] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202007",
        go_inwar = 0,
        id = 467,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [468] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202008",
        go_inwar = 0,
        id = 468,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [469] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202009",
        go_inwar = 0,
        id = 469,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [470] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202010",
        go_inwar = 0,
        id = 470,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [471] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202011",
        go_inwar = 0,
        id = 471,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [472] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202012",
        go_inwar = 0,
        id = 472,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [473] = {
        arg = "",
        blockkey = "shop",
        config = "shop_4_3_202013",
        go_inwar = 0,
        id = 473,
        name = "수정상점",
        op = "(수정패키지)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20302] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204001",
        go_inwar = 0,
        id = 20302,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20303] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204002",
        go_inwar = 0,
        id = 20303,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20311] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204003",
        go_inwar = 0,
        id = 20311,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20312] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204004",
        go_inwar = 0,
        id = 20312,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20314] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204005",
        go_inwar = 0,
        id = 20314,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20316] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204006",
        go_inwar = 0,
        id = 20316,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20402] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204007",
        go_inwar = 0,
        id = 20402,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20407] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204008",
        go_inwar = 0,
        id = 20407,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20503] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204009",
        go_inwar = 0,
        id = 20503,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20504] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204010",
        go_inwar = 0,
        id = 20504,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20505] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204011",
        go_inwar = 0,
        id = 20505,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20506] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204012",
        go_inwar = 0,
        id = 20506,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20508] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204013",
        go_inwar = 0,
        id = 20508,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20509] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204014",
        go_inwar = 0,
        id = 20509,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20313] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204015",
        go_inwar = 0,
        id = 20313,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20409] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204016",
        go_inwar = 0,
        id = 20409,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20514] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204017",
        go_inwar = 0,
        id = 20514,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20315] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204018",
        go_inwar = 0,
        id = 20315,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20512] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204019",
        go_inwar = 0,
        id = 20512,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20405] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204020",
        go_inwar = 0,
        id = 20405,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20418] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204021",
        go_inwar = 0,
        id = 20418,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20510] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204022",
        go_inwar = 0,
        id = 20510,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20511] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204023",
        go_inwar = 0,
        id = 20511,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20305] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204024",
        go_inwar = 0,
        id = 20305,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20306] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204025",
        go_inwar = 0,
        id = 20306,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20417] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204032",
        go_inwar = 0,
        id = 20417,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20403] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204033",
        go_inwar = 0,
        id = 20403,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20513] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204034",
        go_inwar = 0,
        id = 20513,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20501] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204035",
        go_inwar = 0,
        id = 20501,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20507] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204036",
        go_inwar = 0,
        id = 20507,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20502] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204037",
        go_inwar = 0,
        id = 20502,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20416] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204038",
        go_inwar = 0,
        id = 20416,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20415] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204039",
        go_inwar = 0,
        id = 20415,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20414] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204040",
        go_inwar = 0,
        id = 20414,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20413] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204041",
        go_inwar = 0,
        id = 20413,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20412] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204042",
        go_inwar = 0,
        id = 20412,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20410] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204043",
        go_inwar = 0,
        id = 20410,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20404] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204044",
        go_inwar = 0,
        id = 20404,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20401] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204045",
        go_inwar = 0,
        id = 20401,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20308] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204046",
        go_inwar = 0,
        id = 20308,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [20301] = {
        arg = "",
        blockkey = "shop",
        config = "shop_1_2_204047",
        go_inwar = 0,
        id = 20301,
        name = "훈장상점",
        op = "(훈장상점)",
        switch_type = 1,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [30001] = {
        arg = "",
        blockkey = "",
        config = "partner_target_stone_compose",
        go_inwar = 0,
        id = 30001,
        name = "룬석합성",
        op = "룬석합성 지정",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [30002] = {
        arg = "",
        blockkey = "",
        config = "partner_awakeitem",
        go_inwar = 0,
        id = 30002,
        name = "재료합성",
        op = "각성 재료합성 지정",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [30003] = {
        arg = "",
        blockkey = "",
        config = "composite_in_bag",
        go_inwar = 0,
        id = 30003,
        name = "운모합성",
        op = "업그레이드 재료합성 지정",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [30004] = {
        arg = "",
        blockkey = "",
        config = "target_gem_compose",
        go_inwar = 0,
        id = 30004,
        name = "보석합성",
        op = "캐릭터 보석합성 지정",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [500] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_1_1",
        go_inwar = 1,
        id = 500,
        name = "정예1-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [501] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_1_2",
        go_inwar = 1,
        id = 501,
        name = "정예1-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [502] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_1_3",
        go_inwar = 1,
        id = 502,
        name = "정예1-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [503] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_2_1",
        go_inwar = 1,
        id = 503,
        name = "정예2-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [504] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_2_2",
        go_inwar = 1,
        id = 504,
        name = "정예2-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [505] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_2_3",
        go_inwar = 1,
        id = 505,
        name = "정예2-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [506] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_3_1",
        go_inwar = 1,
        id = 506,
        name = "정예3-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [507] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_3_2",
        go_inwar = 1,
        id = 507,
        name = "정예3-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [508] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_3_3",
        go_inwar = 1,
        id = 508,
        name = "정예3-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [509] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_4_1",
        go_inwar = 1,
        id = 509,
        name = "정예4-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [510] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_4_2",
        go_inwar = 1,
        id = 510,
        name = "정예4-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [511] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_4_3",
        go_inwar = 1,
        id = 511,
        name = "정예4-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [512] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_5_1",
        go_inwar = 1,
        id = 512,
        name = "정예5-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [513] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_5_2",
        go_inwar = 1,
        id = 513,
        name = "정예5-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [514] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_5_3",
        go_inwar = 1,
        id = 514,
        name = "정예5-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [515] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_6_1",
        go_inwar = 1,
        id = 515,
        name = "정예6-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [516] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_6_2",
        go_inwar = 1,
        id = 516,
        name = "정예6-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [517] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_6_3",
        go_inwar = 1,
        id = 517,
        name = "정예6-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [518] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_7_1",
        go_inwar = 1,
        id = 518,
        name = "정예7-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [519] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_7_2",
        go_inwar = 1,
        id = 519,
        name = "정예7-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [520] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_7_3",
        go_inwar = 1,
        id = 520,
        name = "정예7-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [521] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_8_1",
        go_inwar = 1,
        id = 521,
        name = "정예8-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [522] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_8_2",
        go_inwar = 1,
        id = 522,
        name = "정예8-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [523] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_8_3",
        go_inwar = 1,
        id = 523,
        name = "정예8-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [524] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_9_1",
        go_inwar = 1,
        id = 524,
        name = "정예9-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [525] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_9_2",
        go_inwar = 1,
        id = 525,
        name = "정예9-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [526] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_9_3",
        go_inwar = 1,
        id = 526,
        name = "정예9-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [527] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_10_1",
        go_inwar = 1,
        id = 527,
        name = "정예10-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [528] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_10_2",
        go_inwar = 1,
        id = 528,
        name = "정예10-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [529] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_10_3",
        go_inwar = 1,
        id = 529,
        name = "정예10-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [530] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_11_1",
        go_inwar = 1,
        id = 530,
        name = "정예11-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [531] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_11_2",
        go_inwar = 1,
        id = 531,
        name = "정예11-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [532] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_11_3",
        go_inwar = 1,
        id = 532,
        name = "정예11-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [533] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_12_1",
        go_inwar = 1,
        id = 533,
        name = "정예12-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [534] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_12_2",
        go_inwar = 1,
        id = 534,
        name = "정예12-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [535] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_12_3",
        go_inwar = 1,
        id = 535,
        name = "정예12-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [536] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_13_1",
        go_inwar = 1,
        id = 536,
        name = "정예13-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [537] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_13_2",
        go_inwar = 1,
        id = 537,
        name = "정예13-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [538] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_13_3",
        go_inwar = 1,
        id = 538,
        name = "정예13-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [539] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_14_1",
        go_inwar = 1,
        id = 539,
        name = "정예14-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [540] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_14_2",
        go_inwar = 1,
        id = 540,
        name = "정예14-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [541] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_14_3",
        go_inwar = 1,
        id = 541,
        name = "정예14-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [542] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_15_1",
        go_inwar = 1,
        id = 542,
        name = "정예15-1",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [543] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_15_2",
        go_inwar = 1,
        id = 543,
        name = "정예15-2",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [544] = {
        arg = "",
        blockkey = "chapterfuben",
        config = "targetchapterhardfuben_15_3",
        go_inwar = 1,
        id = 544,
        name = "정예15-3",
        op = "전투 인터페이스",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [10305] = {
        arg = "",
        blockkey = "draw_card",
        config = "targetcard_305",
        go_inwar = 0,
        id = 10305,
        name = "파트너모집",
        op = "백수 모집",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [10306] = {
        arg = "",
        blockkey = "draw_card",
        config = "targetcard_306",
        go_inwar = 0,
        id = 10306,
        name = "파트너모집",
        op = "백수 모집",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [10315] = {
        arg = "",
        blockkey = "draw_card",
        config = "targetcard_315",
        go_inwar = 0,
        id = 10315,
        name = "파트너모집",
        op = "백수 모집",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [10405] = {
        arg = "",
        blockkey = "draw_card",
        config = "targetcard_405",
        go_inwar = 0,
        id = 10405,
        name = "파트너모집",
        op = "백수 모집",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [10510] = {
        arg = "",
        blockkey = "draw_card",
        config = "targetcard_510",
        go_inwar = 0,
        id = 10510,
        name = "파트너모집",
        op = "백수 모집",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

    [10512] = {
        arg = "",
        blockkey = "draw_card",
        config = "targetcard_512",
        go_inwar = 0,
        id = 10512,
        name = "파트너모집",
        op = "백수 모집",
        switch_type = 0,
        tips = "현재 해당 인터페이스를 열 수 없습니다~",
        type = 1,
    },

}
