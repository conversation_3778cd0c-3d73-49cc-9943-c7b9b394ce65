-- ./excel/task/shimen/task.xlsx
return {

    [60001] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5033,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:22.5:1.5"},
        description = "촌민을 도와 작은 일 처리하기",
        goalDesc = {"달맞이섬에 버섯을 캐러 가다", "백모 에게 버섯 을 주다"},
        id = 60001,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40001", "E5033:60001", "E5035:60001"},
        submitNpcId = 5035,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60002] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:21:5"},
        description = "장난기가 많은 야옹이는 항상 작은 동물 잡기를 좋아합니다.",
        goalDesc = {"야옹이가 나비를 잡도록 돕기"},
        id = 60002,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5034:60002"},
        submitNpcId = 5034,
        submitRewardStr = {"R2001"},
        tasktype = 13,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60003] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PATROL206000:10001"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"순찰을 위해 명계로 이동"},
        id = 60003,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40018", "E40018:60003"},
        submitNpcId = 40018,
        submitRewardStr = {"R2001"},
        tasktype = 14,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60004] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:24.5:17"},
        description = "야옹이 다과회가 곧 열릴 예정이니 야옹이가 손님을 초대하도록 도와주세요.",
        goalDesc = {"집양을 초대하기 위해 풍죽림으로 이동"},
        id = 60004,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5034:60004", "E5025:60004"},
        submitNpcId = 5025,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60005] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40002,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:10.4:19.3"},
        description = "통수부에서 주관하는 신병특훈은 신병의 자질 향상을 목적으로 합니다.",
        goalDesc = {"추무를 찾아 신병 특훈을 완료하기"},
        id = 60005,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40002", "E40002:60005", "E5020:60005"},
        submitNpcId = 5020,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60006] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5035,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE210400:25:6"},
        description = "백 할머니의 건어물은 널리 알려져 있어 일부 범죄자들이 탐낼 수밖에 없습니다.",
        goalDesc = {"생선 도둑을 체포하기"},
        id = 60006,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40003", "E40003:60006", "E5035:60006"},
        submitNpcId = 40003,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60007] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5036,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE210400:6:5"},
        description = "시무진에 있든 팔문촌에 있든 암탉은 건드리지 마십시오.",
        goalDesc = {"사라진 닭을 찾아 이철단에게 전하기"},
        id = 60007,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE40004:210400:6:5", "NC40004", "E40004:60007", "E5036:60007"},
        submitNpcId = 5036,
        submitRewardStr = {"R2001"},
        tasktype = 10,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60011] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5029,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:16.8:6.6"},
        description = "녹리와 영월은 죽마고우로 둘도 없는 한 쌍의 요정입니다.",
        goalDesc = {"유명진에서 설연을 채집하고", "영월에게 전하기"},
        id = 60011,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40005", "E5029:60011", "E5030:60011"},
        submitNpcId = 5030,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60012] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5011,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:15.5:2"},
        description = "제두의 패비용은 소녀의 마음도 있습니다.",
        goalDesc = {"풍죽림에서 대나무를 채칩하고", "비용에게 전하기"},
        id = 60012,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40006", "E5011:60012", "E5010:60012"},
        submitNpcId = 5010,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60013] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5033,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:15:17"},
        description = "촌민을 도와 작은 일 처리하기",
        goalDesc = {"월견도에서 버섯을 채집하고", "원대부에게 전하기"},
        id = 60013,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40011", "E5033:60013", "E5038:60013"},
        submitNpcId = 5038,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60014] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5036,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:2.7:18"},
        description = "촌민을 도와 작은 일 처리하기",
        goalDesc = {"광석장에서 광석을 채집하고", "함어비에게 전하기"},
        id = 60014,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40012", "E5036:60014", "E5037:60014"},
        submitNpcId = 5037,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60015] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5005,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:20:18"},
        description = "성당의 신도들을 도와 성당의 호감을 얻기",
        goalDesc = {"유명진에서 설연을 채집하고", "리사에게 전하기"},
        id = 60015,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40013", "E5005:60015", "E5004:60015"},
        submitNpcId = 5004,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60016] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:4.2:4.1"},
        description = "광석의 도시에는 자원이 부족하고 약물은 전부 상인의 운송에 의존해야 합니다.",
        goalDesc = {"단혼애에서 광석을 채집하고", "단에게 전하기"},
        id = 60016,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40014", "E5018:60016", "E5015:60016"},
        submitNpcId = 5015,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60017] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5021,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:24:15"},
        description = "광석은 단혼애 촌민의 유일한 수입원입니다",
        goalDesc = {"풍죽림에서 대나무를 채칩히고", "연에게 전하기"},
        id = 60017,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40015", "E5021:60017", "E5039:60017"},
        submitNpcId = 5039,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60018] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5026,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE206000:19.3:20"},
        description = "풍죽림의 요정 형제는 사람들의 머리를 아프게 합니다",
        goalDesc = {"명계에서 피안화를 채집하고", "청익에게 전하기"},
        id = 60018,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40016", "E5026:60018", "E5027:60018"},
        submitNpcId = 5027,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60019] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5010,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE206000:27.5:6.3"},
        description = "제두의 패비용과 그의 수하 몇 명",
        goalDesc = {"명계에서 피안화를 채", "주예에게 전하기"},
        id = 60019,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40017", "E5010:60019", "E5011:60019"},
        submitNpcId = 5011,
        submitRewardStr = {"R2001"},
        tasktype = 6,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60021] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5004,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:15:15"},
        description = "식물에 애착이 있는 순결한 성당 수녀.",
        goalDesc = {"장홍화 수확을 위해 유명진으로 이동"},
        id = 60021,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5004:60021"},
        submitNpcId = 5004,
        submitRewardStr = {"R2001"},
        tasktype = 13,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60022] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5015,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:12.5:10.5"},
        description = "많은 환자를 치료한 의술이 고명한 의사.",
        goalDesc = {"부활초 수확을 위해 단혼애로 이동"},
        id = 60022,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5015:60022"},
        submitNpcId = 5015,
        submitRewardStr = {"R2001"},
        tasktype = 13,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60023] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5030,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:3.6:20"},
        description = "요정 영월,장난꾸러기 영월~",
        goalDesc = {"영월이 나비를 잡도록 돕기"},
        id = 60023,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5030:60023"},
        submitNpcId = 5030,
        submitRewardStr = {"R2001"},
        tasktype = 13,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60024] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:43:26.3"},
        description = "수생은 단순한 상인이 아닙니다.",
        goalDesc = {"수생이 나비를 잡도록 돕기"},
        id = 60024,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:60024"},
        submitNpcId = 5018,
        submitRewardStr = {"R2001"},
        tasktype = 13,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60031] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PATROL204000:10002"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"순찰을 위해 월견도로 이동"},
        id = 60031,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40018", "E40018:60031"},
        submitNpcId = 40018,
        submitRewardStr = {"R2001"},
        tasktype = 14,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60032] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PATROL210400:10003"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"순찰을 위해 팔문촌으로 이동"},
        id = 60032,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40018", "E40018:60032"},
        submitNpcId = 40018,
        submitRewardStr = {"R2001"},
        tasktype = 14,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60033] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PATROL202000:10004"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"순찰을 위해 풍죽림으로 이동"},
        id = 60033,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40018", "E40018:60033"},
        submitNpcId = 40018,
        submitRewardStr = {"R2001"},
        tasktype = 14,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60034] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PATROL201000:10005"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"순찰을 위해 단혼애로 이동"},
        id = 60034,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40018", "E40018:60034"},
        submitNpcId = 40018,
        submitRewardStr = {"R2001"},
        tasktype = 14,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60035] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PATROL200000:10006"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"순찰을 위해 광석의 도시로 이동"},
        id = 60035,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40018", "E40018:60035"},
        submitNpcId = 40018,
        submitRewardStr = {"R2001"},
        tasktype = 14,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60036] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PATROL205000:10007"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"순찰을 위해 유명진으로 이동"},
        id = 60036,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40018", "E40018:60036"},
        submitNpcId = 40018,
        submitRewardStr = {"R2001"},
        tasktype = 14,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60041] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5003,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:24.5:20"},
        description = "통수부에 의해 할당된 퀘스트는 신중하게 완료해야 합니다.",
        goalDesc = {"봉 전환을 위해 광석의 도시로 이동"},
        id = 60041,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5003:60041", "E5016:60041"},
        submitNpcId = 5016,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60042] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5026,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE210400:3.4:10.2"},
        description = "팔문촌의 괴구지,베테랑 coser 한 명",
        goalDesc = {"묘옥수에게 옷을 주기"},
        id = 60042,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5026:60042", "E5033:60042"},
        submitNpcId = 5033,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60043] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5020,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:24.5:17"},
        description = "통수부의 기밀 정보,즉시 전달해야 함",
        goalDesc = {"교염에게 정보 전달"},
        id = 60043,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5020:60043", "E5003:60043"},
        submitNpcId = 5003,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60044] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5017,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:27.5:9.5"},
        description = "성당은 신도들을 끌어들이기 위해 전도에 의존한다",
        goalDesc = {"촌민에게 복음 전달"},
        id = 60044,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5017:60044", "E5022:60044"},
        submitNpcId = 5022,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60045] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5025,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:5:13.5"},
        description = "월견도의 요족은 풍죽림과 밀접한 관계가 있습니다.",
        goalDesc = {"영월에게 메시지 전달"},
        id = 60045,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5025:60045", "E5030:60045"},
        submitNpcId = 5030,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60046] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5048,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE206000:21.6:13.2"},
        description = "신비한 물종 게시판,심지어 생물인지도 모름",
        goalDesc = {"명계의 발녀를 찾기"},
        id = 60046,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5048:60046", "E5062:60046"},
        submitNpcId = 5062,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60047] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40019,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:19.7:13.6"},
        description = "망촌유령이 생성한 요괴는 천성적으로 원한을 가지고 태어",
        goalDesc = {"유명진의 북명을 찾기"},
        id = 60047,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40019", "E40019:60047", "E5061:60047"},
        submitNpcId = 5061,
        submitRewardStr = {"R2001"},
        tasktype = 1,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60051] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40002,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:18.7:16"},
        description = "통수부에서 주관하는 신병특훈은 신병의 자질 향상을 목적으로 합니다.",
        goalDesc = {"판다 할아버지를 찾아 특훈 완료"},
        id = 60051,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40002", "E40002:60051", "E5024:60051"},
        submitNpcId = 5024,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60052] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40002,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:7:11.3"},
        description = "통수부에서 주관하는 신병특훈은 신병의 자질 향상을 목적으로 합니다.",
        goalDesc = {"형명을 찾아 특훈 완료"},
        id = 60052,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40002", "E40002:60052", "E5012:60052"},
        submitNpcId = 5012,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60053] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40002,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:17:18"},
        description = "통수부에서 주관하는 신병특훈은 신병의 자질 향상을 목적으로 합니다.",
        goalDesc = {"간 의사를 찾아 특훈 완료"},
        id = 60053,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40002", "E40002:60053", "E5015:60053"},
        submitNpcId = 5015,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60054] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 40002,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE214000:7.5:12.5"},
        description = "통수부에서 주관하는 신병특훈은 신병의 자질 향상을 목적으로 합니다.",
        goalDesc = {"야옹이를 찾아 특훈 완료"},
        id = 60054,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC40002", "E40002:60054", "E5034:60054"},
        submitNpcId = 5034,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60061] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5001,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:24:7"},
        description = "제두의 감옥은 항상 삼엄한 경비가 있었지만 여전히 탈출구가 있습니다.",
        goalDesc = {"도주범 체포를 위해 유명진으로 이동"},
        id = 60061,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5001:60061", "NC40007", "E40007:60061"},
        submitNpcId = 40007,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60062] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE1010000:45.5:10.5"},
        description = "야옹이의 찐빵은 널리 알려져 있어 일부 범죄자들이 탐낼 수밖에 없습니다.",
        goalDesc = {"찐빵 도둑을 찾기"},
        id = 60062,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5034:60062", "NC40008", "E40008:60062"},
        submitNpcId = 40008,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60063] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5015,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE2000000:19.7:2.5"},
        description = "실종된 무도대회 참가자들은 어떤 종류의 약물에 의해 자극을 받은 것으로 보입니다.",
        goalDesc = {"잃어버린 곰패 찾기"},
        id = 60063,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5015:60063", "NC40020", "E40020:60063"},
        submitNpcId = 40020,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60064] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5022,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE2010000:25:9.5"},
        description = "미친 요족들이 단혼애의 주민들을 반복적으로 공격합니다.",
        goalDesc = {"미친 요족 쫓아내기"},
        id = 60064,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5022:60064", "NC40021", "E40021:60064"},
        submitNpcId = 40021,
        submitRewardStr = {"R2001"},
        tasktype = 4,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60071] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5007,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:22:19"},
        description = "전국에 많은 지점을 두고 있는 제두의 술집 화 사장은 고양이를 좋아합니다.",
        goalDesc = {"화 사장님의 고양이를 찾아", "화 사장님에게 전달하기"},
        id = 60071,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE40009:202000:22:19", "NC40009", "E40009:60071", "E5007:60071"},
        submitNpcId = 5007,
        submitRewardStr = {"R2001"},
        tasktype = 10,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

    [60072] = {
        AcceptCallPlot = 0,
        acceptConditionStr = {},
        acceptDialogConfig = {},
        acceptNpcId = 5028,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:27:5.5"},
        description = "신비한 풍죽림의 관리자인 그녀는 자신의 필요를 충족시키기 위해 최선을 다해야 합니다.",
        goalDesc = {"도하의 고양이를 찾아", "도하에게 전달하기"},
        id = 60072,
        missiondone = {},
        name = "순찰 잡무",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE40010:201000:27:5.5", "NC40010", "E40010:60072", "E5028:60072"},
        submitNpcId = 5028,
        submitRewardStr = {"R2001"},
        tasktype = 10,
        teamwork = 1,
        tips = 0,
        type = 4,
    },

}
