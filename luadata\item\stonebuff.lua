-- ./excel/item/equipattr.xlsx
return {

    [11201001] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201001,
        level = 1,
        name = "신격·생",
    },

    [11201002] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201002,
        level = 2,
        name = "신격·생",
    },

    [11201003] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201003,
        level = 3,
        name = "신격·생",
    },

    [11201004] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201004,
        level = 4,
        name = "신격·생",
    },

    [11201005] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201005,
        level = 5,
        name = "신격·생",
    },

    [11201006] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201006,
        level = 6,
        name = "신격·생",
    },

    [11201007] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201007,
        level = 7,
        name = "신격·생",
    },

    [11201008] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201008,
        level = 8,
        name = "신격·생",
    },

    [11201009] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201009,
        level = 9,
        name = "신격·생",
    },

    [11201010] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201010,
        level = 10,
        name = "신격·생",
    },

    [11201011] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201011,
        level = 11,
        name = "신격·생",
    },

    [11201012] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201012,
        level = 12,
        name = "신격·생",
    },

    [11201013] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201013,
        level = 13,
        name = "신격·생",
    },

    [11201014] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201014,
        level = 14,
        name = "신격·생",
    },

    [11201015] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201015,
        level = 15,
        name = "신격·생",
    },

    [11201016] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201016,
        level = 16,
        name = "신격·생",
    },

    [11201017] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201017,
        level = 17,
        name = "신격·생",
    },

    [11201018] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201018,
        level = 18,
        name = "신격·생",
    },

    [11201019] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201019,
        level = 19,
        name = "신격·생",
    },

    [11201020] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201020,
        level = 20,
        name = "신격·생",
    },

    [11201021] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201021,
        level = 21,
        name = "신격·생",
    },

    [11201022] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201022,
        level = 22,
        name = "신격·생",
    },

    [11201023] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201023,
        level = 23,
        name = "신격·생",
    },

    [11201024] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201024,
        level = 24,
        name = "신격·생",
    },

    [11201025] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201025,
        level = 25,
        name = "신격·생",
    },

    [11201026] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201026,
        level = 26,
        name = "신격·생",
    },

    [11201027] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201027,
        level = 27,
        name = "신격·생",
    },

    [11201028] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201028,
        level = 28,
        name = "신격·생",
    },

    [11201029] = {
        buff = "{}",
        buff_ratio = "{maxhp=250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201029,
        level = 29,
        name = "신격·생",
    },

    [11201030] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201030,
        level = 30,
        name = "신격·생",
    },

    [11201031] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201031,
        level = 31,
        name = "신격·생",
    },

    [11201032] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201032,
        level = 32,
        name = "신격·생",
    },

    [11201033] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201033,
        level = 33,
        name = "신격·생",
    },

    [11201034] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201034,
        level = 34,
        name = "신격·생",
    },

    [11201035] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201035,
        level = 35,
        name = "신격·생",
    },

    [11201036] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201036,
        level = 36,
        name = "신격·생",
    },

    [11201037] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201037,
        level = 37,
        name = "신격·생",
    },

    [11201038] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201038,
        level = 38,
        name = "신격·생",
    },

    [11201039] = {
        buff = "{}",
        buff_ratio = "{maxhp=500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201039,
        level = 39,
        name = "신격·생",
    },

    [11201040] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201040,
        level = 40,
        name = "신격·생",
    },

    [11201041] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201041,
        level = 41,
        name = "신격·생",
    },

    [11201042] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201042,
        level = 42,
        name = "신격·생",
    },

    [11201043] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201043,
        level = 43,
        name = "신격·생",
    },

    [11201044] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201044,
        level = 44,
        name = "신격·생",
    },

    [11201045] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201045,
        level = 45,
        name = "신격·생",
    },

    [11201046] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201046,
        level = 46,
        name = "신격·생",
    },

    [11201047] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201047,
        level = 47,
        name = "신격·생",
    },

    [11201048] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201048,
        level = 48,
        name = "신격·생",
    },

    [11201049] = {
        buff = "{}",
        buff_ratio = "{maxhp=750}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201049,
        level = 49,
        name = "신격·생",
    },

    [11201050] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201050,
        level = 50,
        name = "신격·생",
    },

    [11201051] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201051,
        level = 51,
        name = "신격·생",
    },

    [11201052] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201052,
        level = 52,
        name = "신격·생",
    },

    [11201053] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201053,
        level = 53,
        name = "신격·생",
    },

    [11201054] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201054,
        level = 54,
        name = "신격·생",
    },

    [11201055] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201055,
        level = 55,
        name = "신격·생",
    },

    [11201056] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201056,
        level = 56,
        name = "신격·생",
    },

    [11201057] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201057,
        level = 57,
        name = "신격·생",
    },

    [11201058] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201058,
        level = 58,
        name = "신격·생",
    },

    [11201059] = {
        buff = "{}",
        buff_ratio = "{maxhp=1000}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201059,
        level = 59,
        name = "신격·생",
    },

    [11201060] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201060,
        level = 60,
        name = "신격·생",
    },

    [11201061] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201061,
        level = 61,
        name = "신격·생",
    },

    [11201062] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201062,
        level = 62,
        name = "신격·생",
    },

    [11201063] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201063,
        level = 63,
        name = "신격·생",
    },

    [11201064] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201064,
        level = 64,
        name = "신격·생",
    },

    [11201065] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201065,
        level = 65,
        name = "신격·생",
    },

    [11201066] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201066,
        level = 66,
        name = "신격·생",
    },

    [11201067] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201067,
        level = 67,
        name = "신격·생",
    },

    [11201068] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201068,
        level = 68,
        name = "신격·생",
    },

    [11201069] = {
        buff = "{}",
        buff_ratio = "{maxhp=1250}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201069,
        level = 69,
        name = "신격·생",
    },

    [11201070] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201070,
        level = 70,
        name = "신격·생",
    },

    [11201071] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201071,
        level = 71,
        name = "신격·생",
    },

    [11201072] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201072,
        level = 72,
        name = "신격·생",
    },

    [11201073] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201073,
        level = 73,
        name = "신격·생",
    },

    [11201074] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201074,
        level = 74,
        name = "신격·생",
    },

    [11201075] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201075,
        level = 75,
        name = "신격·생",
    },

    [11201076] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201076,
        level = 76,
        name = "신격·생",
    },

    [11201077] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201077,
        level = 77,
        name = "신격·생",
    },

    [11201078] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201078,
        level = 78,
        name = "신격·생",
    },

    [11201079] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201079,
        level = 79,
        name = "신격·생",
    },

    [11201080] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201080,
        level = 80,
        name = "신격·생",
    },

    [11201081] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201081,
        level = 81,
        name = "신격·생",
    },

    [11201082] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201082,
        level = 82,
        name = "신격·생",
    },

    [11201083] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201083,
        level = 83,
        name = "신격·생",
    },

    [11201084] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201084,
        level = 84,
        name = "신격·생",
    },

    [11201085] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201085,
        level = 85,
        name = "신격·생",
    },

    [11201086] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201086,
        level = 86,
        name = "신격·생",
    },

    [11201087] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201087,
        level = 87,
        name = "신격·생",
    },

    [11201088] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201088,
        level = 88,
        name = "신격·생",
    },

    [11201089] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201089,
        level = 89,
        name = "신격·생",
    },

    [11201090] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201090,
        level = 90,
        name = "신격·생",
    },

    [11201091] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201091,
        level = 91,
        name = "신격·생",
    },

    [11201092] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201092,
        level = 92,
        name = "신격·생",
    },

    [11201093] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201093,
        level = 93,
        name = "신격·생",
    },

    [11201094] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201094,
        level = 94,
        name = "신격·생",
    },

    [11201095] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201095,
        level = 95,
        name = "신격·생",
    },

    [11201096] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201096,
        level = 96,
        name = "신격·생",
    },

    [11201097] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201097,
        level = 97,
        name = "신격·생",
    },

    [11201098] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201098,
        level = 98,
        name = "신격·생",
    },

    [11201099] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201099,
        level = 99,
        name = "신격·생",
    },

    [11201100] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201100,
        level = 100,
        name = "신격·생",
    },

    [11201101] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201101,
        level = 101,
        name = "신격·생",
    },

    [11201102] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201102,
        level = 102,
        name = "신격·생",
    },

    [11201103] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201103,
        level = 103,
        name = "신격·생",
    },

    [11201104] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201104,
        level = 104,
        name = "신격·생",
    },

    [11201105] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201105,
        level = 105,
        name = "신격·생",
    },

    [11201106] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201106,
        level = 106,
        name = "신격·생",
    },

    [11201107] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201107,
        level = 107,
        name = "신격·생",
    },

    [11201108] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201108,
        level = 108,
        name = "신격·생",
    },

    [11201109] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201109,
        level = 109,
        name = "신격·생",
    },

    [11201110] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201110,
        level = 110,
        name = "신격·생",
    },

    [11201111] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201111,
        level = 111,
        name = "신격·생",
    },

    [11201112] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201112,
        level = 112,
        name = "신격·생",
    },

    [11201113] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201113,
        level = 113,
        name = "신격·생",
    },

    [11201114] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201114,
        level = 114,
        name = "신격·생",
    },

    [11201115] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201115,
        level = 115,
        name = "신격·생",
    },

    [11201116] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201116,
        level = 116,
        name = "신격·생",
    },

    [11201117] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201117,
        level = 117,
        name = "신격·생",
    },

    [11201118] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201118,
        level = 118,
        name = "신격·생",
    },

    [11201119] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201119,
        level = 119,
        name = "신격·생",
    },

    [11201120] = {
        buff = "{}",
        buff_ratio = "{maxhp=1500}",
        desc = "24시간 동안 캐릭터의 체력을 일시적으로 증가할 수 있다",
        id = 11201120,
        level = 120,
        name = "신격·생",
    },

    [11202001] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202001,
        level = 1,
        name = "신격·공",
    },

    [11202002] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202002,
        level = 2,
        name = "신격·공",
    },

    [11202003] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202003,
        level = 3,
        name = "신격·공",
    },

    [11202004] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202004,
        level = 4,
        name = "신격·공",
    },

    [11202005] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202005,
        level = 5,
        name = "신격·공",
    },

    [11202006] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202006,
        level = 6,
        name = "신격·공",
    },

    [11202007] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202007,
        level = 7,
        name = "신격·공",
    },

    [11202008] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202008,
        level = 8,
        name = "신격·공",
    },

    [11202009] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202009,
        level = 9,
        name = "신격·공",
    },

    [11202010] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202010,
        level = 10,
        name = "신격·공",
    },

    [11202011] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202011,
        level = 11,
        name = "신격·공",
    },

    [11202012] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202012,
        level = 12,
        name = "신격·공",
    },

    [11202013] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202013,
        level = 13,
        name = "신격·공",
    },

    [11202014] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202014,
        level = 14,
        name = "신격·공",
    },

    [11202015] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202015,
        level = 15,
        name = "신격·공",
    },

    [11202016] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202016,
        level = 16,
        name = "신격·공",
    },

    [11202017] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202017,
        level = 17,
        name = "신격·공",
    },

    [11202018] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202018,
        level = 18,
        name = "신격·공",
    },

    [11202019] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202019,
        level = 19,
        name = "신격·공",
    },

    [11202020] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202020,
        level = 20,
        name = "신격·공",
    },

    [11202021] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202021,
        level = 21,
        name = "신격·공",
    },

    [11202022] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202022,
        level = 22,
        name = "신격·공",
    },

    [11202023] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202023,
        level = 23,
        name = "신격·공",
    },

    [11202024] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202024,
        level = 24,
        name = "신격·공",
    },

    [11202025] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202025,
        level = 25,
        name = "신격·공",
    },

    [11202026] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202026,
        level = 26,
        name = "신격·공",
    },

    [11202027] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202027,
        level = 27,
        name = "신격·공",
    },

    [11202028] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202028,
        level = 28,
        name = "신격·공",
    },

    [11202029] = {
        buff = "{}",
        buff_ratio = "{attack=200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202029,
        level = 29,
        name = "신격·공",
    },

    [11202030] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202030,
        level = 30,
        name = "신격·공",
    },

    [11202031] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202031,
        level = 31,
        name = "신격·공",
    },

    [11202032] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202032,
        level = 32,
        name = "신격·공",
    },

    [11202033] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202033,
        level = 33,
        name = "신격·공",
    },

    [11202034] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202034,
        level = 34,
        name = "신격·공",
    },

    [11202035] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202035,
        level = 35,
        name = "신격·공",
    },

    [11202036] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202036,
        level = 36,
        name = "신격·공",
    },

    [11202037] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202037,
        level = 37,
        name = "신격·공",
    },

    [11202038] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202038,
        level = 38,
        name = "신격·공",
    },

    [11202039] = {
        buff = "{}",
        buff_ratio = "{attack=400}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202039,
        level = 39,
        name = "신격·공",
    },

    [11202040] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202040,
        level = 40,
        name = "신격·공",
    },

    [11202041] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202041,
        level = 41,
        name = "신격·공",
    },

    [11202042] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202042,
        level = 42,
        name = "신격·공",
    },

    [11202043] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202043,
        level = 43,
        name = "신격·공",
    },

    [11202044] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202044,
        level = 44,
        name = "신격·공",
    },

    [11202045] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202045,
        level = 45,
        name = "신격·공",
    },

    [11202046] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202046,
        level = 46,
        name = "신격·공",
    },

    [11202047] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202047,
        level = 47,
        name = "신격·공",
    },

    [11202048] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202048,
        level = 48,
        name = "신격·공",
    },

    [11202049] = {
        buff = "{}",
        buff_ratio = "{attack=600}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202049,
        level = 49,
        name = "신격·공",
    },

    [11202050] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202050,
        level = 50,
        name = "신격·공",
    },

    [11202051] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202051,
        level = 51,
        name = "신격·공",
    },

    [11202052] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202052,
        level = 52,
        name = "신격·공",
    },

    [11202053] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202053,
        level = 53,
        name = "신격·공",
    },

    [11202054] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202054,
        level = 54,
        name = "신격·공",
    },

    [11202055] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202055,
        level = 55,
        name = "신격·공",
    },

    [11202056] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202056,
        level = 56,
        name = "신격·공",
    },

    [11202057] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202057,
        level = 57,
        name = "신격·공",
    },

    [11202058] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202058,
        level = 58,
        name = "신격·공",
    },

    [11202059] = {
        buff = "{}",
        buff_ratio = "{attack=800}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202059,
        level = 59,
        name = "신격·공",
    },

    [11202060] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202060,
        level = 60,
        name = "신격·공",
    },

    [11202061] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202061,
        level = 61,
        name = "신격·공",
    },

    [11202062] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202062,
        level = 62,
        name = "신격·공",
    },

    [11202063] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202063,
        level = 63,
        name = "신격·공",
    },

    [11202064] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202064,
        level = 64,
        name = "신격·공",
    },

    [11202065] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202065,
        level = 65,
        name = "신격·공",
    },

    [11202066] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202066,
        level = 66,
        name = "신격·공",
    },

    [11202067] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202067,
        level = 67,
        name = "신격·공",
    },

    [11202068] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202068,
        level = 68,
        name = "신격·공",
    },

    [11202069] = {
        buff = "{}",
        buff_ratio = "{attack=1000}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202069,
        level = 69,
        name = "신격·공",
    },

    [11202070] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202070,
        level = 70,
        name = "신격·공",
    },

    [11202071] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202071,
        level = 71,
        name = "신격·공",
    },

    [11202072] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202072,
        level = 72,
        name = "신격·공",
    },

    [11202073] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202073,
        level = 73,
        name = "신격·공",
    },

    [11202074] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202074,
        level = 74,
        name = "신격·공",
    },

    [11202075] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202075,
        level = 75,
        name = "신격·공",
    },

    [11202076] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202076,
        level = 76,
        name = "신격·공",
    },

    [11202077] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202077,
        level = 77,
        name = "신격·공",
    },

    [11202078] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202078,
        level = 78,
        name = "신격·공",
    },

    [11202079] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202079,
        level = 79,
        name = "신격·공",
    },

    [11202080] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202080,
        level = 80,
        name = "신격·공",
    },

    [11202081] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202081,
        level = 81,
        name = "신격·공",
    },

    [11202082] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202082,
        level = 82,
        name = "신격·공",
    },

    [11202083] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202083,
        level = 83,
        name = "신격·공",
    },

    [11202084] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202084,
        level = 84,
        name = "신격·공",
    },

    [11202085] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202085,
        level = 85,
        name = "신격·공",
    },

    [11202086] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202086,
        level = 86,
        name = "신격·공",
    },

    [11202087] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202087,
        level = 87,
        name = "신격·공",
    },

    [11202088] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202088,
        level = 88,
        name = "신격·공",
    },

    [11202089] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202089,
        level = 89,
        name = "신격·공",
    },

    [11202090] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202090,
        level = 90,
        name = "신격·공",
    },

    [11202091] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202091,
        level = 91,
        name = "신격·공",
    },

    [11202092] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202092,
        level = 92,
        name = "신격·공",
    },

    [11202093] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202093,
        level = 93,
        name = "신격·공",
    },

    [11202094] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202094,
        level = 94,
        name = "신격·공",
    },

    [11202095] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202095,
        level = 95,
        name = "신격·공",
    },

    [11202096] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202096,
        level = 96,
        name = "신격·공",
    },

    [11202097] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202097,
        level = 97,
        name = "신격·공",
    },

    [11202098] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202098,
        level = 98,
        name = "신격·공",
    },

    [11202099] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202099,
        level = 99,
        name = "신격·공",
    },

    [11202100] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202100,
        level = 100,
        name = "신격·공",
    },

    [11202101] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202101,
        level = 101,
        name = "신격·공",
    },

    [11202102] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202102,
        level = 102,
        name = "신격·공",
    },

    [11202103] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202103,
        level = 103,
        name = "신격·공",
    },

    [11202104] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202104,
        level = 104,
        name = "신격·공",
    },

    [11202105] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202105,
        level = 105,
        name = "신격·공",
    },

    [11202106] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202106,
        level = 106,
        name = "신격·공",
    },

    [11202107] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202107,
        level = 107,
        name = "신격·공",
    },

    [11202108] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202108,
        level = 108,
        name = "신격·공",
    },

    [11202109] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202109,
        level = 109,
        name = "신격·공",
    },

    [11202110] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202110,
        level = 110,
        name = "신격·공",
    },

    [11202111] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202111,
        level = 111,
        name = "신격·공",
    },

    [11202112] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202112,
        level = 112,
        name = "신격·공",
    },

    [11202113] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202113,
        level = 113,
        name = "신격·공",
    },

    [11202114] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202114,
        level = 114,
        name = "신격·공",
    },

    [11202115] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202115,
        level = 115,
        name = "신격·공",
    },

    [11202116] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202116,
        level = 116,
        name = "신격·공",
    },

    [11202117] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202117,
        level = 117,
        name = "신격·공",
    },

    [11202118] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202118,
        level = 118,
        name = "신격·공",
    },

    [11202119] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202119,
        level = 119,
        name = "신격·공",
    },

    [11202120] = {
        buff = "{}",
        buff_ratio = "{attack=1200}",
        desc = "24시간 동안 캐릭터의 공격력을 일시적으로 증가할 수 있다",
        id = 11202120,
        level = 120,
        name = "신격·공",
    },

    [11203001] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203001,
        level = 1,
        name = "신격·방",
    },

    [11203002] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203002,
        level = 2,
        name = "신격·방",
    },

    [11203003] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203003,
        level = 3,
        name = "신격·방",
    },

    [11203004] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203004,
        level = 4,
        name = "신격·방",
    },

    [11203005] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203005,
        level = 5,
        name = "신격·방",
    },

    [11203006] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203006,
        level = 6,
        name = "신격·방",
    },

    [11203007] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203007,
        level = 7,
        name = "신격·방",
    },

    [11203008] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203008,
        level = 8,
        name = "신격·방",
    },

    [11203009] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203009,
        level = 9,
        name = "신격·방",
    },

    [11203010] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203010,
        level = 10,
        name = "신격·방",
    },

    [11203011] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203011,
        level = 11,
        name = "신격·방",
    },

    [11203012] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203012,
        level = 12,
        name = "신격·방",
    },

    [11203013] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203013,
        level = 13,
        name = "신격·방",
    },

    [11203014] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203014,
        level = 14,
        name = "신격·방",
    },

    [11203015] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203015,
        level = 15,
        name = "신격·방",
    },

    [11203016] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203016,
        level = 16,
        name = "신격·방",
    },

    [11203017] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203017,
        level = 17,
        name = "신격·방",
    },

    [11203018] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203018,
        level = 18,
        name = "신격·방",
    },

    [11203019] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203019,
        level = 19,
        name = "신격·방",
    },

    [11203020] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203020,
        level = 20,
        name = "신격·방",
    },

    [11203021] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203021,
        level = 21,
        name = "신격·방",
    },

    [11203022] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203022,
        level = 22,
        name = "신격·방",
    },

    [11203023] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203023,
        level = 23,
        name = "신격·방",
    },

    [11203024] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203024,
        level = 24,
        name = "신격·방",
    },

    [11203025] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203025,
        level = 25,
        name = "신격·방",
    },

    [11203026] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203026,
        level = 26,
        name = "신격·방",
    },

    [11203027] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203027,
        level = 27,
        name = "신격·방",
    },

    [11203028] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203028,
        level = 28,
        name = "신격·방",
    },

    [11203029] = {
        buff = "{defense=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203029,
        level = 29,
        name = "신격·방",
    },

    [11203030] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203030,
        level = 30,
        name = "신격·방",
    },

    [11203031] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203031,
        level = 31,
        name = "신격·방",
    },

    [11203032] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203032,
        level = 32,
        name = "신격·방",
    },

    [11203033] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203033,
        level = 33,
        name = "신격·방",
    },

    [11203034] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203034,
        level = 34,
        name = "신격·방",
    },

    [11203035] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203035,
        level = 35,
        name = "신격·방",
    },

    [11203036] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203036,
        level = 36,
        name = "신격·방",
    },

    [11203037] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203037,
        level = 37,
        name = "신격·방",
    },

    [11203038] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203038,
        level = 38,
        name = "신격·방",
    },

    [11203039] = {
        buff = "{defense=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203039,
        level = 39,
        name = "신격·방",
    },

    [11203040] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203040,
        level = 40,
        name = "신격·방",
    },

    [11203041] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203041,
        level = 41,
        name = "신격·방",
    },

    [11203042] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203042,
        level = 42,
        name = "신격·방",
    },

    [11203043] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203043,
        level = 43,
        name = "신격·방",
    },

    [11203044] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203044,
        level = 44,
        name = "신격·방",
    },

    [11203045] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203045,
        level = 45,
        name = "신격·방",
    },

    [11203046] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203046,
        level = 46,
        name = "신격·방",
    },

    [11203047] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203047,
        level = 47,
        name = "신격·방",
    },

    [11203048] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203048,
        level = 48,
        name = "신격·방",
    },

    [11203049] = {
        buff = "{defense=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203049,
        level = 49,
        name = "신격·방",
    },

    [11203050] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203050,
        level = 50,
        name = "신격·방",
    },

    [11203051] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203051,
        level = 51,
        name = "신격·방",
    },

    [11203052] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203052,
        level = 52,
        name = "신격·방",
    },

    [11203053] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203053,
        level = 53,
        name = "신격·방",
    },

    [11203054] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203054,
        level = 54,
        name = "신격·방",
    },

    [11203055] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203055,
        level = 55,
        name = "신격·방",
    },

    [11203056] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203056,
        level = 56,
        name = "신격·방",
    },

    [11203057] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203057,
        level = 57,
        name = "신격·방",
    },

    [11203058] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203058,
        level = 58,
        name = "신격·방",
    },

    [11203059] = {
        buff = "{defense=80}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203059,
        level = 59,
        name = "신격·방",
    },

    [11203060] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203060,
        level = 60,
        name = "신격·방",
    },

    [11203061] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203061,
        level = 61,
        name = "신격·방",
    },

    [11203062] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203062,
        level = 62,
        name = "신격·방",
    },

    [11203063] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203063,
        level = 63,
        name = "신격·방",
    },

    [11203064] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203064,
        level = 64,
        name = "신격·방",
    },

    [11203065] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203065,
        level = 65,
        name = "신격·방",
    },

    [11203066] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203066,
        level = 66,
        name = "신격·방",
    },

    [11203067] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203067,
        level = 67,
        name = "신격·방",
    },

    [11203068] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203068,
        level = 68,
        name = "신격·방",
    },

    [11203069] = {
        buff = "{defense=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203069,
        level = 69,
        name = "신격·방",
    },

    [11203070] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203070,
        level = 70,
        name = "신격·방",
    },

    [11203071] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203071,
        level = 71,
        name = "신격·방",
    },

    [11203072] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203072,
        level = 72,
        name = "신격·방",
    },

    [11203073] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203073,
        level = 73,
        name = "신격·방",
    },

    [11203074] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203074,
        level = 74,
        name = "신격·방",
    },

    [11203075] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203075,
        level = 75,
        name = "신격·방",
    },

    [11203076] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203076,
        level = 76,
        name = "신격·방",
    },

    [11203077] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203077,
        level = 77,
        name = "신격·방",
    },

    [11203078] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203078,
        level = 78,
        name = "신격·방",
    },

    [11203079] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203079,
        level = 79,
        name = "신격·방",
    },

    [11203080] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203080,
        level = 80,
        name = "신격·방",
    },

    [11203081] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203081,
        level = 81,
        name = "신격·방",
    },

    [11203082] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203082,
        level = 82,
        name = "신격·방",
    },

    [11203083] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203083,
        level = 83,
        name = "신격·방",
    },

    [11203084] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203084,
        level = 84,
        name = "신격·방",
    },

    [11203085] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203085,
        level = 85,
        name = "신격·방",
    },

    [11203086] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203086,
        level = 86,
        name = "신격·방",
    },

    [11203087] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203087,
        level = 87,
        name = "신격·방",
    },

    [11203088] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203088,
        level = 88,
        name = "신격·방",
    },

    [11203089] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203089,
        level = 89,
        name = "신격·방",
    },

    [11203090] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203090,
        level = 90,
        name = "신격·방",
    },

    [11203091] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203091,
        level = 91,
        name = "신격·방",
    },

    [11203092] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203092,
        level = 92,
        name = "신격·방",
    },

    [11203093] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203093,
        level = 93,
        name = "신격·방",
    },

    [11203094] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203094,
        level = 94,
        name = "신격·방",
    },

    [11203095] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203095,
        level = 95,
        name = "신격·방",
    },

    [11203096] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203096,
        level = 96,
        name = "신격·방",
    },

    [11203097] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203097,
        level = 97,
        name = "신격·방",
    },

    [11203098] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203098,
        level = 98,
        name = "신격·방",
    },

    [11203099] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203099,
        level = 99,
        name = "신격·방",
    },

    [11203100] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203100,
        level = 100,
        name = "신격·방",
    },

    [11203101] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203101,
        level = 101,
        name = "신격·방",
    },

    [11203102] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203102,
        level = 102,
        name = "신격·방",
    },

    [11203103] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203103,
        level = 103,
        name = "신격·방",
    },

    [11203104] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203104,
        level = 104,
        name = "신격·방",
    },

    [11203105] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203105,
        level = 105,
        name = "신격·방",
    },

    [11203106] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203106,
        level = 106,
        name = "신격·방",
    },

    [11203107] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203107,
        level = 107,
        name = "신격·방",
    },

    [11203108] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203108,
        level = 108,
        name = "신격·방",
    },

    [11203109] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203109,
        level = 109,
        name = "신격·방",
    },

    [11203110] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203110,
        level = 110,
        name = "신격·방",
    },

    [11203111] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203111,
        level = 111,
        name = "신격·방",
    },

    [11203112] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203112,
        level = 112,
        name = "신격·방",
    },

    [11203113] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203113,
        level = 113,
        name = "신격·방",
    },

    [11203114] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203114,
        level = 114,
        name = "신격·방",
    },

    [11203115] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203115,
        level = 115,
        name = "신격·방",
    },

    [11203116] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203116,
        level = 116,
        name = "신격·방",
    },

    [11203117] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203117,
        level = 117,
        name = "신격·방",
    },

    [11203118] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203118,
        level = 118,
        name = "신격·방",
    },

    [11203119] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203119,
        level = 119,
        name = "신격·방",
    },

    [11203120] = {
        buff = "{defense=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 방어력을 일시적으로 증가할 수 있다",
        id = 11203120,
        level = 120,
        name = "신격·방",
    },

    [11204001] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204001,
        level = 1,
        name = "신격·속",
    },

    [11204002] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204002,
        level = 2,
        name = "신격·속",
    },

    [11204003] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204003,
        level = 3,
        name = "신격·속",
    },

    [11204004] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204004,
        level = 4,
        name = "신격·속",
    },

    [11204005] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204005,
        level = 5,
        name = "신격·속",
    },

    [11204006] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204006,
        level = 6,
        name = "신격·속",
    },

    [11204007] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204007,
        level = 7,
        name = "신격·속",
    },

    [11204008] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204008,
        level = 8,
        name = "신격·속",
    },

    [11204009] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204009,
        level = 9,
        name = "신격·속",
    },

    [11204010] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204010,
        level = 10,
        name = "신격·속",
    },

    [11204011] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204011,
        level = 11,
        name = "신격·속",
    },

    [11204012] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204012,
        level = 12,
        name = "신격·속",
    },

    [11204013] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204013,
        level = 13,
        name = "신격·속",
    },

    [11204014] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204014,
        level = 14,
        name = "신격·속",
    },

    [11204015] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204015,
        level = 15,
        name = "신격·속",
    },

    [11204016] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204016,
        level = 16,
        name = "신격·속",
    },

    [11204017] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204017,
        level = 17,
        name = "신격·속",
    },

    [11204018] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204018,
        level = 18,
        name = "신격·속",
    },

    [11204019] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204019,
        level = 19,
        name = "신격·속",
    },

    [11204020] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204020,
        level = 20,
        name = "신격·속",
    },

    [11204021] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204021,
        level = 21,
        name = "신격·속",
    },

    [11204022] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204022,
        level = 22,
        name = "신격·속",
    },

    [11204023] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204023,
        level = 23,
        name = "신격·속",
    },

    [11204024] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204024,
        level = 24,
        name = "신격·속",
    },

    [11204025] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204025,
        level = 25,
        name = "신격·속",
    },

    [11204026] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204026,
        level = 26,
        name = "신격·속",
    },

    [11204027] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204027,
        level = 27,
        name = "신격·속",
    },

    [11204028] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204028,
        level = 28,
        name = "신격·속",
    },

    [11204029] = {
        buff = "{speed=10}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204029,
        level = 29,
        name = "신격·속",
    },

    [11204030] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204030,
        level = 30,
        name = "신격·속",
    },

    [11204031] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204031,
        level = 31,
        name = "신격·속",
    },

    [11204032] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204032,
        level = 32,
        name = "신격·속",
    },

    [11204033] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204033,
        level = 33,
        name = "신격·속",
    },

    [11204034] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204034,
        level = 34,
        name = "신격·속",
    },

    [11204035] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204035,
        level = 35,
        name = "신격·속",
    },

    [11204036] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204036,
        level = 36,
        name = "신격·속",
    },

    [11204037] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204037,
        level = 37,
        name = "신격·속",
    },

    [11204038] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204038,
        level = 38,
        name = "신격·속",
    },

    [11204039] = {
        buff = "{speed=20}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204039,
        level = 39,
        name = "신격·속",
    },

    [11204040] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204040,
        level = 40,
        name = "신격·속",
    },

    [11204041] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204041,
        level = 41,
        name = "신격·속",
    },

    [11204042] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204042,
        level = 42,
        name = "신격·속",
    },

    [11204043] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204043,
        level = 43,
        name = "신격·속",
    },

    [11204044] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204044,
        level = 44,
        name = "신격·속",
    },

    [11204045] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204045,
        level = 45,
        name = "신격·속",
    },

    [11204046] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204046,
        level = 46,
        name = "신격·속",
    },

    [11204047] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204047,
        level = 47,
        name = "신격·속",
    },

    [11204048] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204048,
        level = 48,
        name = "신격·속",
    },

    [11204049] = {
        buff = "{speed=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204049,
        level = 49,
        name = "신격·속",
    },

    [11204050] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204050,
        level = 50,
        name = "신격·속",
    },

    [11204051] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204051,
        level = 51,
        name = "신격·속",
    },

    [11204052] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204052,
        level = 52,
        name = "신격·속",
    },

    [11204053] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204053,
        level = 53,
        name = "신격·속",
    },

    [11204054] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204054,
        level = 54,
        name = "신격·속",
    },

    [11204055] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204055,
        level = 55,
        name = "신격·속",
    },

    [11204056] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204056,
        level = 56,
        name = "신격·속",
    },

    [11204057] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204057,
        level = 57,
        name = "신격·속",
    },

    [11204058] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204058,
        level = 58,
        name = "신격·속",
    },

    [11204059] = {
        buff = "{speed=40}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204059,
        level = 59,
        name = "신격·속",
    },

    [11204060] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204060,
        level = 60,
        name = "신격·속",
    },

    [11204061] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204061,
        level = 61,
        name = "신격·속",
    },

    [11204062] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204062,
        level = 62,
        name = "신격·속",
    },

    [11204063] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204063,
        level = 63,
        name = "신격·속",
    },

    [11204064] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204064,
        level = 64,
        name = "신격·속",
    },

    [11204065] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204065,
        level = 65,
        name = "신격·속",
    },

    [11204066] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204066,
        level = 66,
        name = "신격·속",
    },

    [11204067] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204067,
        level = 67,
        name = "신격·속",
    },

    [11204068] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204068,
        level = 68,
        name = "신격·속",
    },

    [11204069] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204069,
        level = 69,
        name = "신격·속",
    },

    [11204070] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204070,
        level = 70,
        name = "신격·속",
    },

    [11204071] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204071,
        level = 71,
        name = "신격·속",
    },

    [11204072] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204072,
        level = 72,
        name = "신격·속",
    },

    [11204073] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204073,
        level = 73,
        name = "신격·속",
    },

    [11204074] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204074,
        level = 74,
        name = "신격·속",
    },

    [11204075] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204075,
        level = 75,
        name = "신격·속",
    },

    [11204076] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204076,
        level = 76,
        name = "신격·속",
    },

    [11204077] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204077,
        level = 77,
        name = "신격·속",
    },

    [11204078] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204078,
        level = 78,
        name = "신격·속",
    },

    [11204079] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204079,
        level = 79,
        name = "신격·속",
    },

    [11204080] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204080,
        level = 80,
        name = "신격·속",
    },

    [11204081] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204081,
        level = 81,
        name = "신격·속",
    },

    [11204082] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204082,
        level = 82,
        name = "신격·속",
    },

    [11204083] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204083,
        level = 83,
        name = "신격·속",
    },

    [11204084] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204084,
        level = 84,
        name = "신격·속",
    },

    [11204085] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204085,
        level = 85,
        name = "신격·속",
    },

    [11204086] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204086,
        level = 86,
        name = "신격·속",
    },

    [11204087] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204087,
        level = 87,
        name = "신격·속",
    },

    [11204088] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204088,
        level = 88,
        name = "신격·속",
    },

    [11204089] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204089,
        level = 89,
        name = "신격·속",
    },

    [11204090] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204090,
        level = 90,
        name = "신격·속",
    },

    [11204091] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204091,
        level = 91,
        name = "신격·속",
    },

    [11204092] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204092,
        level = 92,
        name = "신격·속",
    },

    [11204093] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204093,
        level = 93,
        name = "신격·속",
    },

    [11204094] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204094,
        level = 94,
        name = "신격·속",
    },

    [11204095] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204095,
        level = 95,
        name = "신격·속",
    },

    [11204096] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204096,
        level = 96,
        name = "신격·속",
    },

    [11204097] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204097,
        level = 97,
        name = "신격·속",
    },

    [11204098] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204098,
        level = 98,
        name = "신격·속",
    },

    [11204099] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204099,
        level = 99,
        name = "신격·속",
    },

    [11204100] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204100,
        level = 100,
        name = "신격·속",
    },

    [11204101] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204101,
        level = 101,
        name = "신격·속",
    },

    [11204102] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204102,
        level = 102,
        name = "신격·속",
    },

    [11204103] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204103,
        level = 103,
        name = "신격·속",
    },

    [11204104] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204104,
        level = 104,
        name = "신격·속",
    },

    [11204105] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204105,
        level = 105,
        name = "신격·속",
    },

    [11204106] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204106,
        level = 106,
        name = "신격·속",
    },

    [11204107] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204107,
        level = 107,
        name = "신격·속",
    },

    [11204108] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204108,
        level = 108,
        name = "신격·속",
    },

    [11204109] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204109,
        level = 109,
        name = "신격·속",
    },

    [11204110] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204110,
        level = 110,
        name = "신격·속",
    },

    [11204111] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204111,
        level = 111,
        name = "신격·속",
    },

    [11204112] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204112,
        level = 112,
        name = "신격·속",
    },

    [11204113] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204113,
        level = 113,
        name = "신격·속",
    },

    [11204114] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204114,
        level = 114,
        name = "신격·속",
    },

    [11204115] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204115,
        level = 115,
        name = "신격·속",
    },

    [11204116] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204116,
        level = 116,
        name = "신격·속",
    },

    [11204117] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204117,
        level = 117,
        name = "신격·속",
    },

    [11204118] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204118,
        level = 118,
        name = "신격·속",
    },

    [11204119] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204119,
        level = 119,
        name = "신격·속",
    },

    [11204120] = {
        buff = "{speed=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 속도값을 일시적으로 증가할 수 있다",
        id = 11204120,
        level = 120,
        name = "신격·속",
    },

    [11205001] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205001,
        level = 1,
        name = "신격·폭",
    },

    [11205002] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205002,
        level = 2,
        name = "신격·폭",
    },

    [11205003] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205003,
        level = 3,
        name = "신격·폭",
    },

    [11205004] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205004,
        level = 4,
        name = "신격·폭",
    },

    [11205005] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205005,
        level = 5,
        name = "신격·폭",
    },

    [11205006] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205006,
        level = 6,
        name = "신격·폭",
    },

    [11205007] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205007,
        level = 7,
        name = "신격·폭",
    },

    [11205008] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205008,
        level = 8,
        name = "신격·폭",
    },

    [11205009] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205009,
        level = 9,
        name = "신격·폭",
    },

    [11205010] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205010,
        level = 10,
        name = "신격·폭",
    },

    [11205011] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205011,
        level = 11,
        name = "신격·폭",
    },

    [11205012] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205012,
        level = 12,
        name = "신격·폭",
    },

    [11205013] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205013,
        level = 13,
        name = "신격·폭",
    },

    [11205014] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205014,
        level = 14,
        name = "신격·폭",
    },

    [11205015] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205015,
        level = 15,
        name = "신격·폭",
    },

    [11205016] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205016,
        level = 16,
        name = "신격·폭",
    },

    [11205017] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205017,
        level = 17,
        name = "신격·폭",
    },

    [11205018] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205018,
        level = 18,
        name = "신격·폭",
    },

    [11205019] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205019,
        level = 19,
        name = "신격·폭",
    },

    [11205020] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205020,
        level = 20,
        name = "신격·폭",
    },

    [11205021] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205021,
        level = 21,
        name = "신격·폭",
    },

    [11205022] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205022,
        level = 22,
        name = "신격·폭",
    },

    [11205023] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205023,
        level = 23,
        name = "신격·폭",
    },

    [11205024] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205024,
        level = 24,
        name = "신격·폭",
    },

    [11205025] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205025,
        level = 25,
        name = "신격·폭",
    },

    [11205026] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205026,
        level = 26,
        name = "신격·폭",
    },

    [11205027] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205027,
        level = 27,
        name = "신격·폭",
    },

    [11205028] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205028,
        level = 28,
        name = "신격·폭",
    },

    [11205029] = {
        buff = "{critical_ratio=50}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205029,
        level = 29,
        name = "신격·폭",
    },

    [11205030] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205030,
        level = 30,
        name = "신격·폭",
    },

    [11205031] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205031,
        level = 31,
        name = "신격·폭",
    },

    [11205032] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205032,
        level = 32,
        name = "신격·폭",
    },

    [11205033] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205033,
        level = 33,
        name = "신격·폭",
    },

    [11205034] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205034,
        level = 34,
        name = "신격·폭",
    },

    [11205035] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205035,
        level = 35,
        name = "신격·폭",
    },

    [11205036] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205036,
        level = 36,
        name = "신격·폭",
    },

    [11205037] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205037,
        level = 37,
        name = "신격·폭",
    },

    [11205038] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205038,
        level = 38,
        name = "신격·폭",
    },

    [11205039] = {
        buff = "{critical_ratio=100}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205039,
        level = 39,
        name = "신격·폭",
    },

    [11205040] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205040,
        level = 40,
        name = "신격·폭",
    },

    [11205041] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205041,
        level = 41,
        name = "신격·폭",
    },

    [11205042] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205042,
        level = 42,
        name = "신격·폭",
    },

    [11205043] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205043,
        level = 43,
        name = "신격·폭",
    },

    [11205044] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205044,
        level = 44,
        name = "신격·폭",
    },

    [11205045] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205045,
        level = 45,
        name = "신격·폭",
    },

    [11205046] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205046,
        level = 46,
        name = "신격·폭",
    },

    [11205047] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205047,
        level = 47,
        name = "신격·폭",
    },

    [11205048] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205048,
        level = 48,
        name = "신격·폭",
    },

    [11205049] = {
        buff = "{critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205049,
        level = 49,
        name = "신격·폭",
    },

    [11205050] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205050,
        level = 50,
        name = "신격·폭",
    },

    [11205051] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205051,
        level = 51,
        name = "신격·폭",
    },

    [11205052] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205052,
        level = 52,
        name = "신격·폭",
    },

    [11205053] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205053,
        level = 53,
        name = "신격·폭",
    },

    [11205054] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205054,
        level = 54,
        name = "신격·폭",
    },

    [11205055] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205055,
        level = 55,
        name = "신격·폭",
    },

    [11205056] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205056,
        level = 56,
        name = "신격·폭",
    },

    [11205057] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205057,
        level = 57,
        name = "신격·폭",
    },

    [11205058] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205058,
        level = 58,
        name = "신격·폭",
    },

    [11205059] = {
        buff = "{critical_ratio=200}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205059,
        level = 59,
        name = "신격·폭",
    },

    [11205060] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205060,
        level = 60,
        name = "신격·폭",
    },

    [11205061] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205061,
        level = 61,
        name = "신격·폭",
    },

    [11205062] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205062,
        level = 62,
        name = "신격·폭",
    },

    [11205063] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205063,
        level = 63,
        name = "신격·폭",
    },

    [11205064] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205064,
        level = 64,
        name = "신격·폭",
    },

    [11205065] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205065,
        level = 65,
        name = "신격·폭",
    },

    [11205066] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205066,
        level = 66,
        name = "신격·폭",
    },

    [11205067] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205067,
        level = 67,
        name = "신격·폭",
    },

    [11205068] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205068,
        level = 68,
        name = "신격·폭",
    },

    [11205069] = {
        buff = "{critical_ratio=250}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205069,
        level = 69,
        name = "신격·폭",
    },

    [11205070] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205070,
        level = 70,
        name = "신격·폭",
    },

    [11205071] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205071,
        level = 71,
        name = "신격·폭",
    },

    [11205072] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205072,
        level = 72,
        name = "신격·폭",
    },

    [11205073] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205073,
        level = 73,
        name = "신격·폭",
    },

    [11205074] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205074,
        level = 74,
        name = "신격·폭",
    },

    [11205075] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205075,
        level = 75,
        name = "신격·폭",
    },

    [11205076] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205076,
        level = 76,
        name = "신격·폭",
    },

    [11205077] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205077,
        level = 77,
        name = "신격·폭",
    },

    [11205078] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205078,
        level = 78,
        name = "신격·폭",
    },

    [11205079] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205079,
        level = 79,
        name = "신격·폭",
    },

    [11205080] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205080,
        level = 80,
        name = "신격·폭",
    },

    [11205081] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205081,
        level = 81,
        name = "신격·폭",
    },

    [11205082] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205082,
        level = 82,
        name = "신격·폭",
    },

    [11205083] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205083,
        level = 83,
        name = "신격·폭",
    },

    [11205084] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205084,
        level = 84,
        name = "신격·폭",
    },

    [11205085] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205085,
        level = 85,
        name = "신격·폭",
    },

    [11205086] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205086,
        level = 86,
        name = "신격·폭",
    },

    [11205087] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205087,
        level = 87,
        name = "신격·폭",
    },

    [11205088] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205088,
        level = 88,
        name = "신격·폭",
    },

    [11205089] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205089,
        level = 89,
        name = "신격·폭",
    },

    [11205090] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205090,
        level = 90,
        name = "신격·폭",
    },

    [11205091] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205091,
        level = 91,
        name = "신격·폭",
    },

    [11205092] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205092,
        level = 92,
        name = "신격·폭",
    },

    [11205093] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205093,
        level = 93,
        name = "신격·폭",
    },

    [11205094] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205094,
        level = 94,
        name = "신격·폭",
    },

    [11205095] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205095,
        level = 95,
        name = "신격·폭",
    },

    [11205096] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205096,
        level = 96,
        name = "신격·폭",
    },

    [11205097] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205097,
        level = 97,
        name = "신격·폭",
    },

    [11205098] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205098,
        level = 98,
        name = "신격·폭",
    },

    [11205099] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205099,
        level = 99,
        name = "신격·폭",
    },

    [11205100] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205100,
        level = 100,
        name = "신격·폭",
    },

    [11205101] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205101,
        level = 101,
        name = "신격·폭",
    },

    [11205102] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205102,
        level = 102,
        name = "신격·폭",
    },

    [11205103] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205103,
        level = 103,
        name = "신격·폭",
    },

    [11205104] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205104,
        level = 104,
        name = "신격·폭",
    },

    [11205105] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205105,
        level = 105,
        name = "신격·폭",
    },

    [11205106] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205106,
        level = 106,
        name = "신격·폭",
    },

    [11205107] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205107,
        level = 107,
        name = "신격·폭",
    },

    [11205108] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205108,
        level = 108,
        name = "신격·폭",
    },

    [11205109] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205109,
        level = 109,
        name = "신격·폭",
    },

    [11205110] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205110,
        level = 110,
        name = "신격·폭",
    },

    [11205111] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205111,
        level = 111,
        name = "신격·폭",
    },

    [11205112] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205112,
        level = 112,
        name = "신격·폭",
    },

    [11205113] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205113,
        level = 113,
        name = "신격·폭",
    },

    [11205114] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205114,
        level = 114,
        name = "신격·폭",
    },

    [11205115] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205115,
        level = 115,
        name = "신격·폭",
    },

    [11205116] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205116,
        level = 116,
        name = "신격·폭",
    },

    [11205117] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205117,
        level = 117,
        name = "신격·폭",
    },

    [11205118] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205118,
        level = 118,
        name = "신격·폭",
    },

    [11205119] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205119,
        level = 119,
        name = "신격·폭",
    },

    [11205120] = {
        buff = "{critical_ratio=300}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타율을 일시적으로 증가할 수 있다",
        id = 11205120,
        level = 120,
        name = "신격·폭",
    },

    [11206001] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206001,
        level = 1,
        name = "신격·항",
    },

    [11206002] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206002,
        level = 2,
        name = "신격·항",
    },

    [11206003] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206003,
        level = 3,
        name = "신격·항",
    },

    [11206004] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206004,
        level = 4,
        name = "신격·항",
    },

    [11206005] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206005,
        level = 5,
        name = "신격·항",
    },

    [11206006] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206006,
        level = 6,
        name = "신격·항",
    },

    [11206007] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206007,
        level = 7,
        name = "신격·항",
    },

    [11206008] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206008,
        level = 8,
        name = "신격·항",
    },

    [11206009] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206009,
        level = 9,
        name = "신격·항",
    },

    [11206010] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206010,
        level = 10,
        name = "신격·항",
    },

    [11206011] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206011,
        level = 11,
        name = "신격·항",
    },

    [11206012] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206012,
        level = 12,
        name = "신격·항",
    },

    [11206013] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206013,
        level = 13,
        name = "신격·항",
    },

    [11206014] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206014,
        level = 14,
        name = "신격·항",
    },

    [11206015] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206015,
        level = 15,
        name = "신격·항",
    },

    [11206016] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206016,
        level = 16,
        name = "신격·항",
    },

    [11206017] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206017,
        level = 17,
        name = "신격·항",
    },

    [11206018] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206018,
        level = 18,
        name = "신격·항",
    },

    [11206019] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206019,
        level = 19,
        name = "신격·항",
    },

    [11206020] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206020,
        level = 20,
        name = "신격·항",
    },

    [11206021] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206021,
        level = 21,
        name = "신격·항",
    },

    [11206022] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206022,
        level = 22,
        name = "신격·항",
    },

    [11206023] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206023,
        level = 23,
        name = "신격·항",
    },

    [11206024] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206024,
        level = 24,
        name = "신격·항",
    },

    [11206025] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206025,
        level = 25,
        name = "신격·항",
    },

    [11206026] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206026,
        level = 26,
        name = "신격·항",
    },

    [11206027] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206027,
        level = 27,
        name = "신격·항",
    },

    [11206028] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206028,
        level = 28,
        name = "신격·항",
    },

    [11206029] = {
        buff = "{res_critical_ratio=30}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206029,
        level = 29,
        name = "신격·항",
    },

    [11206030] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206030,
        level = 30,
        name = "신격·항",
    },

    [11206031] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206031,
        level = 31,
        name = "신격·항",
    },

    [11206032] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206032,
        level = 32,
        name = "신격·항",
    },

    [11206033] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206033,
        level = 33,
        name = "신격·항",
    },

    [11206034] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206034,
        level = 34,
        name = "신격·항",
    },

    [11206035] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206035,
        level = 35,
        name = "신격·항",
    },

    [11206036] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206036,
        level = 36,
        name = "신격·항",
    },

    [11206037] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206037,
        level = 37,
        name = "신격·항",
    },

    [11206038] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206038,
        level = 38,
        name = "신격·항",
    },

    [11206039] = {
        buff = "{res_critical_ratio=60}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206039,
        level = 39,
        name = "신격·항",
    },

    [11206040] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206040,
        level = 40,
        name = "신격·항",
    },

    [11206041] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206041,
        level = 41,
        name = "신격·항",
    },

    [11206042] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206042,
        level = 42,
        name = "신격·항",
    },

    [11206043] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206043,
        level = 43,
        name = "신격·항",
    },

    [11206044] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206044,
        level = 44,
        name = "신격·항",
    },

    [11206045] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206045,
        level = 45,
        name = "신격·항",
    },

    [11206046] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206046,
        level = 46,
        name = "신격·항",
    },

    [11206047] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206047,
        level = 47,
        name = "신격·항",
    },

    [11206048] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206048,
        level = 48,
        name = "신격·항",
    },

    [11206049] = {
        buff = "{res_critical_ratio=90}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206049,
        level = 49,
        name = "신격·항",
    },

    [11206050] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206050,
        level = 50,
        name = "신격·항",
    },

    [11206051] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206051,
        level = 51,
        name = "신격·항",
    },

    [11206052] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206052,
        level = 52,
        name = "신격·항",
    },

    [11206053] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206053,
        level = 53,
        name = "신격·항",
    },

    [11206054] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206054,
        level = 54,
        name = "신격·항",
    },

    [11206055] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206055,
        level = 55,
        name = "신격·항",
    },

    [11206056] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206056,
        level = 56,
        name = "신격·항",
    },

    [11206057] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206057,
        level = 57,
        name = "신격·항",
    },

    [11206058] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206058,
        level = 58,
        name = "신격·항",
    },

    [11206059] = {
        buff = "{res_critical_ratio=120}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206059,
        level = 59,
        name = "신격·항",
    },

    [11206060] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206060,
        level = 60,
        name = "신격·항",
    },

    [11206061] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206061,
        level = 61,
        name = "신격·항",
    },

    [11206062] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206062,
        level = 62,
        name = "신격·항",
    },

    [11206063] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206063,
        level = 63,
        name = "신격·항",
    },

    [11206064] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206064,
        level = 64,
        name = "신격·항",
    },

    [11206065] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206065,
        level = 65,
        name = "신격·항",
    },

    [11206066] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206066,
        level = 66,
        name = "신격·항",
    },

    [11206067] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206067,
        level = 67,
        name = "신격·항",
    },

    [11206068] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206068,
        level = 68,
        name = "신격·항",
    },

    [11206069] = {
        buff = "{res_critical_ratio=150}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206069,
        level = 69,
        name = "신격·항",
    },

    [11206070] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206070,
        level = 70,
        name = "신격·항",
    },

    [11206071] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206071,
        level = 71,
        name = "신격·항",
    },

    [11206072] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206072,
        level = 72,
        name = "신격·항",
    },

    [11206073] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206073,
        level = 73,
        name = "신격·항",
    },

    [11206074] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206074,
        level = 74,
        name = "신격·항",
    },

    [11206075] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206075,
        level = 75,
        name = "신격·항",
    },

    [11206076] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206076,
        level = 76,
        name = "신격·항",
    },

    [11206077] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206077,
        level = 77,
        name = "신격·항",
    },

    [11206078] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206078,
        level = 78,
        name = "신격·항",
    },

    [11206079] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206079,
        level = 79,
        name = "신격·항",
    },

    [11206080] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206080,
        level = 80,
        name = "신격·항",
    },

    [11206081] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206081,
        level = 81,
        name = "신격·항",
    },

    [11206082] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206082,
        level = 82,
        name = "신격·항",
    },

    [11206083] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206083,
        level = 83,
        name = "신격·항",
    },

    [11206084] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206084,
        level = 84,
        name = "신격·항",
    },

    [11206085] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206085,
        level = 85,
        name = "신격·항",
    },

    [11206086] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206086,
        level = 86,
        name = "신격·항",
    },

    [11206087] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206087,
        level = 87,
        name = "신격·항",
    },

    [11206088] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206088,
        level = 88,
        name = "신격·항",
    },

    [11206089] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206089,
        level = 89,
        name = "신격·항",
    },

    [11206090] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206090,
        level = 90,
        name = "신격·항",
    },

    [11206091] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206091,
        level = 91,
        name = "신격·항",
    },

    [11206092] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206092,
        level = 92,
        name = "신격·항",
    },

    [11206093] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206093,
        level = 93,
        name = "신격·항",
    },

    [11206094] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206094,
        level = 94,
        name = "신격·항",
    },

    [11206095] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206095,
        level = 95,
        name = "신격·항",
    },

    [11206096] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206096,
        level = 96,
        name = "신격·항",
    },

    [11206097] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206097,
        level = 97,
        name = "신격·항",
    },

    [11206098] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206098,
        level = 98,
        name = "신격·항",
    },

    [11206099] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206099,
        level = 99,
        name = "신격·항",
    },

    [11206100] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206100,
        level = 100,
        name = "신격·항",
    },

    [11206101] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206101,
        level = 101,
        name = "신격·항",
    },

    [11206102] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206102,
        level = 102,
        name = "신격·항",
    },

    [11206103] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206103,
        level = 103,
        name = "신격·항",
    },

    [11206104] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206104,
        level = 104,
        name = "신격·항",
    },

    [11206105] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206105,
        level = 105,
        name = "신격·항",
    },

    [11206106] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206106,
        level = 106,
        name = "신격·항",
    },

    [11206107] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206107,
        level = 107,
        name = "신격·항",
    },

    [11206108] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206108,
        level = 108,
        name = "신격·항",
    },

    [11206109] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206109,
        level = 109,
        name = "신격·항",
    },

    [11206110] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206110,
        level = 110,
        name = "신격·항",
    },

    [11206111] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206111,
        level = 111,
        name = "신격·항",
    },

    [11206112] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206112,
        level = 112,
        name = "신격·항",
    },

    [11206113] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206113,
        level = 113,
        name = "신격·항",
    },

    [11206114] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206114,
        level = 114,
        name = "신격·항",
    },

    [11206115] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206115,
        level = 115,
        name = "신격·항",
    },

    [11206116] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206116,
        level = 116,
        name = "신격·항",
    },

    [11206117] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206117,
        level = 117,
        name = "신격·항",
    },

    [11206118] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206118,
        level = 118,
        name = "신격·항",
    },

    [11206119] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206119,
        level = 119,
        name = "신격·항",
    },

    [11206120] = {
        buff = "{res_critical_ratio=180}",
        buff_ratio = "{}",
        desc = "24시간 동안 캐릭터의 치명타 저항률을 일시적으로 증가할 수 있다",
        id = 11206120,
        level = 120,
        name = "신격·항",
    },

}
