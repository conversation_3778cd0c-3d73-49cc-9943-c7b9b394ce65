-- ./excel/log/travel.xlsx
return {

    ["travel_start"] = {
        desc = "원정시작",
        log_format = {["gap_second"] = {["id"] = "gap_second", ["desc"] = "보상간격"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["travel_second"] = {["id"] = "travel_second", ["desc"] = "원정시간"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "travel_start",
    },

    ["travel_stop"] = {
        desc = "원정중단",
        log_format = {["gap_second"] = {["id"] = "gap_second", ["desc"] = "보상간격"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["travel_second"] = {["id"] = "travel_second", ["desc"] = "원정시간"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "travel_stop",
    },

    ["friend_travel_mine"] = {
        desc = "친구 용병",
        log_format = {["friend_pid"] = {["id"] = "friend_pid", ["desc"] = "용병친구pid"}, ["partnerid"] = {["id"] = "partnerid", ["desc"] = "용병동료id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "friend_travel_mine",
    },

    ["mine_travel_friend"] = {
        desc = "나의 용병",
        log_format = {["friend_pid"] = {["id"] = "friend_pid", ["desc"] = "용병친구pid"}, ["partnerid"] = {["id"] = "partnerid", ["desc"] = "용병동료id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "mine_travel_friend",
    },

    ["add_travel_reward"] = {
        desc = "아이템 보상",
        log_format = {["amount"] = {["id"] = "amount", ["desc"] = "획득 수량"}, ["partnerid"] = {["id"] = "partnerid", ["desc"] = "동료id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["pos"] = {["id"] = "pos", ["desc"] = "동료위치설정"}, ["reward_count"] = {["id"] = "reward_count", ["desc"] = "보상횟수"}, ["sid"] = {["id"] = "sid", ["desc"] = "아이템id"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "add_travel_reward",
    },

    ["add_travel_exp"] = {
        desc = "경험치 보상",
        log_format = {["partner_exp"] = {["id"] = "partner_exp", ["desc"] = "경험치 획득"}, ["partnerid"] = {["id"] = "partnerid", ["desc"] = "동료id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["pos"] = {["id"] = "pos", ["desc"] = "동료위치설정"}, ["reward_count"] = {["id"] = "reward_count", ["desc"] = "보상횟수"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "add_travel_exp",
    },

    ["receive_travel_reward"] = {
        desc = "보상 수령",
        log_format = {["iteminfo"] = {["id"] = "iteminfo", ["desc"] = "보상상세"}, ["partner_exp"] = {["id"] = "partner_exp", ["desc"] = "동료경험치상세"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "receive_travel_reward",
    },

    ["receive_friend_reward"] = {
        desc = "용병 보상 수령",
        log_format = {["friend_pid"] = {["id"] = "friend_pid", ["desc"] = "용병친구pid"}, ["partner_exp"] = {["id"] = "partner_exp", ["desc"] = "경험치 정보"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "receive_friend_reward",
    },

    ["trigger_draw_card"] = {
        desc = "카드게임 활성화",
        log_format = {["end_second"] = {["id"] = "end_second", ["desc"] = "종료시간"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "trigger_draw_card",
    },

    ["remove_draw_card"] = {
        desc = "카드게임 종료",
        log_format = {["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["play_count"] = {["id"] = "play_count", ["desc"] = "카드게임횟수"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "remove_draw_card",
    },

    ["draw_card_start"] = {
        desc = "카드게임 1회 오픈",
        log_format = {["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["play_count"] = {["id"] = "play_count", ["desc"] = "카드게임횟수"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "draw_card_start",
    },

    ["draw_card_stop"] = {
        desc = "카드게임 1회 종료",
        log_format = {["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["play_count"] = {["id"] = "play_count", ["desc"] = "카드게임횟수"}, ["travel_type"] = {["id"] = "travel_type", ["desc"] = "원정유형"}},
        subtype = "draw_card_stop",
    },

}
