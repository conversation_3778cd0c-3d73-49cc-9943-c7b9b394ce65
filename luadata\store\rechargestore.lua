-- ./excel/store/rechargestore.xlsx
return {

    [1001] = {
        RMB = 33500,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "1",
        id = 1001,
        mark = 1,
        name = "#w270",
        payid = "com.kaopu.ylq.6",
        platform = 1,
        random_talk = {1},
        sort_id = 1,
    },

    [1002] = {
        RMB = 3000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "2",
        id = 1002,
        mark = 1,
        name = "#w2150",
        payid = "com.kaopu.ylq.12",
        platform = 1,
        random_talk = {1},
        sort_id = 2,
    },

    [1003] = {
        RMB = 7500,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "3",
        id = 1003,
        mark = 1,
        name = "#w2380",
        payid = "com.kaopu.ylq.30",
        platform = 1,
        random_talk = {1},
        sort_id = 3,
    },

    [1004] = {
        RMB = 17000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "4",
        id = 1004,
        mark = 1,
        name = "#w2880",
        payid = "com.kaopu.ylq.68",
        platform = 1,
        random_talk = {1},
        sort_id = 4,
    },

    [1005] = {
        RMB = 29000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "5",
        id = 1005,
        mark = 1,
        name = "#w21600",
        payid = "com.kaopu.ylq.128",
        platform = 1,
        random_talk = {1},
        sort_id = 5,
    },

    [1006] = {
        RMB = 68000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "7",
        id = 1006,
        mark = 1,
        name = "#w23880",
        payid = "com.kaopu.ylq.328",
        platform = 1,
        random_talk = {1},
        sort_id = 6,
    },

    [1007] = {
        RMB = 129000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "6",
        id = 1007,
        mark = 1,
        name = "#w28080",
        payid = "com.kaopu.ylq.648",
        platform = 1,
        random_talk = {1},
        sort_id = 7,
    },

    [1009] = {
        RMB = 7500,
        desc = "",
        front_str = "",
        icon = "8",
        id = 1009,
        mark = 2,
        name = "월정액",
        payid = "com.kaopu.ylq.yk",
        platform = 1,
        random_talk = {2},
        sort_id = 9,
    },

    [1010] = {
        RMB = 22000,
        desc = "",
        front_str = "",
        icon = "9",
        id = 1010,
        mark = 3,
        name = "영구권",
        payid = "com.kaopu.ylq.zsk",
        platform = 1,
        random_talk = {3},
        sort_id = 10,
    },

    [1011] = {
        RMB = 22000,
        desc = "",
        front_str = "",
        icon = "10",
        id = 1011,
        mark = 4,
        name = "성장기금",
        payid = "com.kaopu.ylq.czjj",
        platform = 1,
        random_talk = {4},
        sort_id = 11,
    },

    [2001] = {
        RMB = 1500,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "1",
        id = 2001,
        mark = 1,
        name = "#w270",
        payid = "com.kaopu.ylq.appstore.6",
        platform = 2,
        random_talk = {1},
        sort_id = 1,
    },

    [2002] = {
        RMB = 3000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "2",
        id = 2002,
        mark = 1,
        name = "#w2150",
        payid = "com.kaopu.ylq.appstore.12",
        platform = 2,
        random_talk = {1},
        sort_id = 2,
    },

    [2003] = {
        RMB = 7500,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "3",
        id = 2003,
        mark = 1,
        name = "#w2380",
        payid = "com.kaopu.ylq.appstore.30",
        platform = 2,
        random_talk = {1},
        sort_id = 3,
    },

    [2004] = {
        RMB = 17000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "4",
        id = 2004,
        mark = 1,
        name = "#w2880",
        payid = "com.kaopu.ylq.appstore.68",
        platform = 2,
        random_talk = {1},
        sort_id = 4,
    },

    [2005] = {
        RMB = 29000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "5",
        id = 2005,
        mark = 1,
        name = "#w21600",
        payid = "com.kaopu.ylq.appstore.128",
        platform = 2,
        random_talk = {1},
        sort_id = 5,
    },

    [2006] = {
        RMB = 68000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "7",
        id = 2006,
        mark = 1,
        name = "#w23880",
        payid = "com.kaopu.ylq.appstore.328",
        platform = 2,
        random_talk = {1},
        sort_id = 6,
    },

    [2007] = {
        RMB = 129000,
        desc = "",
        front_str = "goldcoinstore_",
        icon = "6",
        id = 2007,
        mark = 1,
        name = "#w28080",
        payid = "com.kaopu.ylq.appstore.648",
        platform = 2,
        random_talk = {1},
        sort_id = 7,
    },

    [2008] = {
        RMB = 7500,
        desc = "즉시 획득#w2300매일#w2100수령할 수 있습니다",
        front_str = "",
        icon = "8",
        id = 2008,
        mark = 2,
        name = "월정액",
        payid = "com.kaopu.ylq.appstore.yk",
        platform = 2,
        random_talk = {2},
        sort_id = 9,
    },

    [2009] = {
        RMB = 22000,
        desc = "즉식 획득#w2980매일#w2110수령할 수 있습니다",
        front_str = "",
        icon = "9",
        id = 2009,
        mark = 3,
        name = "영구권",
        payid = "com.kaopu.ylq.appstore.zsk",
        platform = 2,
        random_talk = {3},
        sort_id = 10,
    },

    [2010] = {
        RMB = 22000,
        desc = "레벨이 오를 수록 7배 반환합니다#w2",
        front_str = "",
        icon = "10",
        id = 2010,
        mark = 4,
        name = "성장기금",
        payid = "com.kaopu.ylq.appstore.czjj",
        platform = 2,
        random_talk = {4},
        sort_id = 11,
    },

}
