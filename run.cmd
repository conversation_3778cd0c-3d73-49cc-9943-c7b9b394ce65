cd /d E:\ChenxiDaobiao\daobiao
SET PYTHONPATH=./tools/Python27/Lib/site-packages
@echo off
color 07
chcp 936
rmdir /S /Q luadata
mkdir luadata
.\tools\Python27\python.exe xls2lua.py
if ERRORLEVEL 1 (
	color 04 
	pause
)

del /a /f gamedata\server\data.lua
.\tools\lua\lua.exe .\lua2game_scripts\server\init.lua luadata gamedata/server
if ERRORLEVEL 1 (
	color 04 
	pause
)
chcp 65001
if ERRORLEVEL 1 (
	color 04 
	pause
)

color 02
echo 服务端导表成功!!!!!!!!!!!!!!!!!!!!!!!!!!

echo.
echo ===========================================
echo 开始执行客户端数据转换...
echo ===========================================
echo.

REM 检查是否需要强制更新
if "%1"=="force" (
    echo 启用强制更新模式...
    set FORCE_UPDATE=1
) else (
    set FORCE_UPDATE=0
)

.\tools\lua\lua.exe .\client\convert\_run.lua
set CLIENT_ERROR_LEVEL=%ERRORLEVEL%
echo.
echo 客户端转换脚本执行完毕，返回码: %CLIENT_ERROR_LEVEL%

if %CLIENT_ERROR_LEVEL% NEQ 0 (
    color 04
    echo ERROR: 客户端转换失败，错误码: %CLIENT_ERROR_LEVEL%
    pause
    exit /b %CLIENT_ERROR_LEVEL%
)

echo.
echo 检查client/data目录状态...
if exist "client\data" (
    echo client\data目录存在
    dir "client\data" /b | find /c /v "" > temp_count.txt
    set /p FILE_COUNT=<temp_count.txt
    del temp_count.txt
    echo client\data目录中的文件数量: %FILE_COUNT%
) else (
    echo WARNING: client\data目录不存在！
)

chcp 65001
if ERRORLEVEL 1 (
    color 04
    pause
)

color 02
echo 客户端导表成功!!!!!!!!!!!!!!!!!!!!!!!!!!

pause
