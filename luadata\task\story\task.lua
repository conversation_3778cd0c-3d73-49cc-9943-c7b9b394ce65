-- ./excel/task/story/task.xlsx
return {

    [10001] = {
        AcceptCallPlot = 0,
        ChapterFb = "",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {"제두는 처음이라", "여기저기 구경해보세요."},
        acceptNpcId = 10000,
        autoDoNextTask = 10002,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "제두는 처음이라 배고파 죽겠어요,어서 맛있는 게 어디 있는지 찾아봐요!",
        goalDesc = {"둘러보다"},
        id = 10001,
        missiondone = {"NT10002"},
        name = "새로 온 사람",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10000", "E10000:10001"},
        submitNpcId = 10000,
        submitRewardStr = {"R1001"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10002] = {
        AcceptCallPlot = 0,
        ChapterFb = "",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10001,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "제두에게 이런 사람이 있을 줄은 몰랐는데,악의적으로 협박하는 사람이 가장 고약해요!",
        goalDesc = {"악인 먼저 고발"},
        id = 10002,
        missiondone = {"NT10003"},
        name = "실력을 보여주기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10001", "NC10002", "NC10003", "NC10004", "NC10005", "NC10006", "E10001:10002", "E10002:10002", "E10003:10002"},
        submitNpcId = 10001,
        submitRewardStr = {"R1002"},
        taskWalkingTips = "드디어 제두에 도착했습니다;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10003] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10007,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화의 끈질김에서 벗어났어요!",
        goalDesc = {"질문을 받다"},
        id = 10003,
        missiondone = {"NT10004"},
        name = "소녀 중화",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10007", "E10007:10003"},
        submitNpcId = 10007,
        submitRewardStr = {"R1003"},
        taskWalkingTips = "제두에 악패가 있을 줄이야,저 사람은 아까 그 사람이잖아!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10004] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10008,
        autoDoNextTask = 10005,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "국숫집 사장님께 무도대회의 경기 정보를 물어보세요!",
        goalDesc = {"대회 정보"},
        id = 10004,
        missiondone = {"NT10005"},
        name = "심문",
        playid = 10002,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10008", "E10008:10004"},
        submitNpcId = 10008,
        submitRewardStr = {"R1004"},
        taskWalkingTips = "앞에서 뭘 팔고 있는지 냄새가 좋네요!;none;none",
        tasktype = 15,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10005] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10009,
        autoDoNextTask = 10007,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "국숫집 사장님께 무도대회의 경기 정보를 물어보고 싶어요!",
        goalDesc = {"지난 역사"},
        id = 10005,
        missiondone = {"NT10007"},
        name = "이벤트",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10009", "E10009:10005"},
        submitNpcId = 10009,
        submitRewardStr = {"R1005"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10006] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10010,
        autoDoNextTask = 10007,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:35:8.7"},
        description = "국숫집 사장님께 무도대회의 경기 정보를 물어보고 싶어요!",
        goalDesc = {"중요한 정보"},
        id = 10006,
        missiondone = {"NT10007"},
        name = "종료",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10010", "E10010:10006"},
        submitNpcId = 10010,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10007] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10011,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞의 사람이 잔여 페이지의 존재를 알고 있는데,그녀는 누구일까요?",
        goalDesc = {"악인"},
        id = 10007,
        missiondone = {"NT10008"},
        name = "길을 묻다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10011", "E10011:10007"},
        submitNpcId = 10011,
        submitRewardStr = {"R1007"},
        taskWalkingTips = "아니,통수부는 어디에 있지?;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10008] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10012,
        autoDoNextTask = 10009,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "습격자와 대화를 통해 유용한 정보를 얻으세요.",
        goalDesc = {"혼란스러운"},
        id = 10008,
        missiondone = {"NT10009"},
        name = "질문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10012", "NC10013", "E10012:10008", "E10013:10008"},
        submitNpcId = 10012,
        submitRewardStr = {"R1008"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10009] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10014,
        autoDoNextTask = 10371,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "암투로 통수부가 장악당한 것도 전화위복이라고 할 수 있을까요?",
        goalDesc = {"통수부"},
        id = 10009,
        missiondone = {"NT10371"},
        name = "변장한 축복",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE10014:101000:5:25.7", "NC10014", "NC10015", "E10014:10009", "E10015:10009", "E5001:10009"},
        submitNpcId = 5001,
        submitRewardStr = {"R1009"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10010] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10016,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "사투 때문에 장관 출전 정지라니?골치가 아프네요.",
        goalDesc = {"행운과 불행은 서로에게 달려있다"},
        id = 10010,
        missiondone = {"NT10011"},
        name = "벌하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10016", "E10016:10010"},
        submitNpcId = 10016,
        submitRewardStr = {"R1010"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10011] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10301,
        autoDoNextTask = 10373,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "반드시 해명해야 합니다,자신이 고의로 싸운 것이 아니라는 걸.",
        goalDesc = {"공평과 불공평"},
        id = 10011,
        missiondone = {"NT10373"},
        name = "토론",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10301", "E10301:10011"},
        submitNpcId = 10301,
        submitRewardStr = {"R1011"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10012] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5012,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "제두무관의 형명 관장에게 도전하여 인정을 받으세요.",
        goalDesc = {"무관의 전투"},
        id = 10012,
        missiondone = {"NT10013"},
        name = "자격 인정",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5012:10012"},
        submitNpcId = 5012,
        submitRewardStr = {"R1012"},
        taskWalkingTips = "무관은 이쪽에 있는 것 같은데?;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10013] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5012,
        autoDoNextTask = 10014,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "형명의 추천을 획득했습니다!",
        goalDesc = {"추천인"},
        id = 10013,
        missiondone = {"NT10014"},
        name = "아는 사람",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5012:10013"},
        submitNpcId = 5012,
        submitRewardStr = {"R1013"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10014] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10302,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "통수부를 떠저는 길에 이상한 소녀에게 가로 막혔습니다!",
        goalDesc = {"흑열에게 답장"},
        id = 10014,
        missiondone = {"NT10375"},
        name = "동의",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10302", "E10302:10014", "NC10303", "E10303:10014"},
        submitNpcId = 10302,
        submitRewardStr = {"R1014"},
        taskWalkingTips = "다행히 아직 늦지 않았습니다!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10015] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10304,
        autoDoNextTask = 10016,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "통수부를 떠저는 길에 이상한 소녀에게 가로 막혔습니다!",
        goalDesc = {"이상한 여자"},
        id = 10015,
        missiondone = {"NT10016"},
        name = "설명",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10304", "E10304:10015"},
        submitNpcId = 10304,
        submitRewardStr = {"R1015"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10016] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10305,
        autoDoNextTask = 10017,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞에 뜻밖의 신비한 생물 한 마리가 나타났다!",
        goalDesc = {"그것을 잡아"},
        id = 10016,
        missiondone = {"NT10017"},
        name = "뚜뚜루",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10305", "E10305:10016", "NC10306", "E10306:10016"},
        submitNpcId = 10305,
        submitRewardStr = {"R1016"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10017] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10307,
        autoDoNextTask = 10378,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새 잡기 대전에 동참해주세요!",
        goalDesc = {"뚜뚜루 제어불능"},
        id = 10017,
        missiondone = {"NT10378"},
        name = "돌발 상황",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10307", "NC10308", "TTRACE10307:101000:30.4:12.4", "E10307:10017", "E10308:10017"},
        submitNpcId = 10308,
        submitRewardStr = {"R1017"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10018] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10309,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞에 있는 여동생에게 펫을 잡아오겠다고 약속하세요!",
        goalDesc = {"이루 동생"},
        id = 10018,
        missiondone = {"NT10019"},
        name = "위로",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10309", "E10309:10018"},
        submitNpcId = 10309,
        submitRewardStr = {"R1018"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10019] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10310,
        autoDoNextTask = 10020,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:44.5:13.3"},
        description = "집행관 이루가 도망간 뚜루새를 잡아오라고 명령합니다!",
        goalDesc = {"이루의 명령"},
        id = 10019,
        missiondone = {"NT10020"},
        name = "옮기다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10310", "E10310:10019"},
        submitNpcId = 10310,
        submitRewardStr = {"R1019"},
        taskWalkingTips = "정말 재밌는 아이네;아무데나 쏘아다니다 응보를 받을 줄 몰랐네;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10020] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5005,
        autoDoNextTask = 10379,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새 봤는지 행인에게 물어봅시다.",
        goalDesc = {"행인에게 묻다"},
        id = 10020,
        missiondone = {"NT10379"},
        name = "체포 작전",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5005:10020"},
        submitNpcId = 5005,
        submitRewardStr = {"R1020"},
        taskWalkingTips = "어디 간거죠?;none;드디어 당신을 찾았네요!",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10021] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10601,
        autoDoNextTask = 10382,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새를 따라가다.",
        goalDesc = {"체포를 실행"},
        id = 10021,
        missiondone = {"NT10382"},
        name = "목표 결정",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10601", "E10601:10021", "E5005:10021", "E5004:10021", "TTRACE10601:101000:46.5:20.4"},
        submitNpcId = 5005,
        submitRewardStr = {"R1021"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10022] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5004,
        autoDoNextTask = 10190,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새는 거의 잡을 뻔했는데, 아쉽게도 수녀님이 막으셨어요.",
        goalDesc = {"체포 실패"},
        id = 10022,
        missiondone = {"NT10384"},
        name = "수녀",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5004:10022", "NC10602", "E10602:10021"},
        submitNpcId = 5004,
        submitRewardStr = {"R1022"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10023] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10603,
        autoDoNextTask = 10025,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "비록 행동은 실패했지만 결과를 그 나리에게도 알려야 합니다.",
        goalDesc = {"통수부로 이동"},
        id = 10023,
        missiondone = {"NT10024"},
        name = "회신 결과",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10603", "NC10604", "E10603:10023", "E10604:10023"},
        submitNpcId = 10603,
        submitRewardStr = {"R1023"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10024] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10606,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "새 집행관 등장!",
        goalDesc = {"백"},
        id = 10024,
        missiondone = {"NT10385"},
        name = "집행관",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10605", "NC10606", "NC10607", "E10605:10024", "E10606:10024", "E10607:10024"},
        submitNpcId = 10606,
        submitRewardStr = {"R1024"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10025] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10608,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "또 막혀버렸다,이번엔 집행광 백이다!",
        goalDesc = {"데자뷰"},
        id = 10025,
        missiondone = {"NT10026"},
        name = "사고",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10608", "E10608:10025"},
        submitNpcId = 10608,
        submitRewardStr = {"R1025"},
        taskWalkingTips = "다행히 집행관들이 사리가 밝네요,절대 목적을 들키면 안 됩니다.;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10026] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10609,
        autoDoNextTask = 10027,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백 나리과 작별을 하고 황제 무도관으로 가서 형명에게 답례를 하세요!",
        goalDesc = {"백의 오해"},
        id = 10026,
        missiondone = {"NT10027"},
        name = "낯설다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10609", "E10609:10026"},
        submitNpcId = 10609,
        submitRewardStr = {"R1026"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10027] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"AI:11602:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5012,
        autoDoNextTask = 10029,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "형명을 대신해 화상주에게 답례를 보내세요.",
        goalDesc = {"관장 정명"},
        id = 10027,
        missiondone = {"NT10029"},
        name = "감사합니다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"I11602:1", "E5012:10027", "E5007:10027"},
        submitNpcId = 5007,
        submitRewardStr = {"R1027"},
        taskWalkingTips = "관장님의 도움에 감사를 드려야 합니다!;술냄새가 진하네요!;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10028] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5007,
        autoDoNextTask = 10029,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:43:26"},
        description = "제두 화상주와 대화를 통해 성당 관련 정보를 취하세요.",
        goalDesc = {"성당 정보"},
        id = 10028,
        missiondone = {"NT10029"},
        name = "헛소리",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5007:10028"},
        submitNpcId = 5007,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10029] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5004,
        autoDoNextTask = 10030,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "성당에 가서 화상주가 말한 모든 것을 검증하세요.",
        goalDesc = {"첫 성당 탐방"},
        id = 10029,
        missiondone = {"NT10030"},
        name = "확인하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5004:10029"},
        submitNpcId = 5004,
        submitRewardStr = {"R1029"},
        taskWalkingTips = "제두 성당에서 그 사람을 만날 수 있을까요......;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10030] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10610,
        autoDoNextTask = 10031,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "성당를 떠난 후 중화를 다시 만났습니다.",
        goalDesc = {"선수들"},
        id = 10030,
        missiondone = {"NT10031"},
        name = "우연히 만나다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10610", "NC10611", "E10610:10030", "E10611:10030", "GRADE20"},
        submitNpcId = 10610,
        submitRewardStr = {"R1030"},
        taskWalkingTips = "어떻게 성당에 잠입할 수 있을까요?;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10031] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10901,
        autoDoNextTask = 10032,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "새로운 친구를 사귀세요!다시 한번 만나세요,저는 중화라고 합니다.",
        goalDesc = {"중화"},
        id = 10031,
        missiondone = {"NT10032"},
        name = "신규 파트너",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10901", "NC10902", "TTRACE10901:101000:34.6:9.1", "E10901:10031", "E10902:10031"},
        submitNpcId = 10902,
        submitRewardStr = {"R1031"},
        taskWalkingTips = "모르는 사람이랑 면을 먹는 것보다 혼자 먹는게 훨씬 낫아요;그래요!그럼 가요!;none",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10032] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10903,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화와 담화를 나누세요.",
        goalDesc = {"중화에 대해 알아보기"},
        id = 10032,
        missiondone = {"NT10033"},
        name = "물려받다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10903", "E10903:10032"},
        submitNpcId = 10903,
        submitRewardStr = {"R1032"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10033] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10904,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "형명의 당부를 들으세요.",
        goalDesc = {"의뢰"},
        id = 10033,
        missiondone = {"NT10191"},
        name = "위험한 곳",
        playid = 10003,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10904", "E10904:10033"},
        submitNpcId = 10904,
        submitRewardStr = {"R1033"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10034] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5016,
        autoDoNextTask = 10192,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉관장의 조언.",
        goalDesc = {"상기시키다"},
        id = 10034,
        missiondone = {"NT10192"},
        name = "광석의 도시",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5016:10034"},
        submitNpcId = 5016,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10035] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10905,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "제두로 돌아가던 중 습격을 당했습니다.",
        goalDesc = {"요괴"},
        id = 10035,
        missiondone = {"NT10192"},
        name = "공격",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10905", "E10905:10035"},
        submitNpcId = 10905,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10036] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10906,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족의 습격을 받고 신부가 등장했습니다.",
        goalDesc = {"공격 후"},
        id = 10036,
        missiondone = {"NT10038"},
        name = "신부",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10906", "NC10907", "E10906:10036", "E10907:10036"},
        submitNpcId = 10906,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10037] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10908,
        autoDoNextTask = 10038,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "빨리 제두로 돌아가서 무도 대회에 참가하세요.",
        goalDesc = {"제두로 복귀"},
        id = 10037,
        missiondone = {"NT10038"},
        name = "급히 가다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE10908:101000:13:14", "NC10908", "NC10909", "NC10910", "E10908:10037", "E10909:10037", "E10910:10037"},
        submitNpcId = 10910,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10038] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10911,
        autoDoNextTask = 10039,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "첫 경기 시작!",
        goalDesc = {"대회 시작"},
        id = 10038,
        missiondone = {"NT10039"},
        name = "무도대회",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10911", "NC10912", "NC10913", "NC10914", "NC10915", "NC10916", "NC10917", "E10911:10038", "E10912:10038", "E10913:10038", "E10915:10038"},
        submitNpcId = 10911,
        submitRewardStr = {"R1038"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10039] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10918,
        autoDoNextTask = 10193,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "1차 심사를 거친 상대는 뜻밖에도 싸움을 멈추기를 원하지 않았습니다.",
        goalDesc = {"타도"},
        id = 10039,
        missiondone = {"NT10193"},
        name = "1차 심사",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10918", "NC10919", "NC10920", "NC10921", "NC10922", "NC10923", "E10918:10039"},
        submitNpcId = 10918,
        submitRewardStr = {"R1039"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10040] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,2",
        acceptConditionStr = {"AI:11604:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10924,
        autoDoNextTask = 10043,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"TI:11604:1:0"},
        description = "경기 후 상처를 치료해야 합니다.",
        goalDesc = {"예선 후"},
        id = 10040,
        missiondone = {"NT10043"},
        name = "걱정",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10924", "NC10925", "E10924:10040", "E10924:10040"},
        submitNpcId = 10924,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10041] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10926,
        autoDoNextTask = 10042,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "다음 경기에도 더 분발해야야 합니다!",
        goalDesc = {"재등장"},
        id = 10041,
        missiondone = {"NT10042"},
        name = "진급전",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10926", "NC10927", "NC10928", "NC10929", "NC10930", "NC10931", "NC10932", "NC10933", "NC10934", "E10926:10041", "E10933:10041", "E10930:10041"},
        submitNpcId = 10926,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10042] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10935,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뒤에 있는 가장 중요한 사람을 수호하기 위해,앞길이 아무리 험난해도 포기하지 않는다!",
        goalDesc = {"치연 대전"},
        id = 10042,
        missiondone = {"NT10043"},
        name = "도발",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10935", "NC10936", "NC10937", "NC10938", "NC10939", "NC10940", "E10935:10042"},
        submitNpcId = 10935,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10043] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10941,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "경기장을 제어하지 못해 집행관들이 등장했습니다.",
        goalDesc = {"집행관 흑"},
        id = 10043,
        missiondone = {"NT10044"},
        name = "적수 등장",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10941", "NC10942", "NC10943", "NC10944", "NC10945", "NC10946", "NC10947", "NC10948", "NC10949", "E10941:10043", "E10942:10043", "E10943:10043", "E10944:10043"},
        submitNpcId = 10941,
        submitRewardStr = {"R1043"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10044] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10952,
        autoDoNextTask = 10045,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "전례없는 혼란스러운 무도대회였지만 결과는!",
        goalDesc = {"예상치 못한 상황"},
        id = 10044,
        missiondone = {"NT10045"},
        name = "싸울지 말지",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10951", "NC10952", "NC10953", "NC10954", "NC10955", "NC10956", "NC10957", "NC10958", "NC10959", "TTRACE10952:101000:17.8:15.5", "E10952:10044", "E10953:10044", "E10959:10044"},
        submitNpcId = 10953,
        submitRewardStr = {"R1044"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10045] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10960,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "끝까지 버틴 결과,경기가 계속됩니다!",
        goalDesc = {"절대 포기하지 마"},
        id = 10045,
        missiondone = {"NT10046"},
        name = "견지",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10960", "NC10961", "NC10962", "NC10963", "NC10964", "NC10965", "NC10966", "E10960:10045", "E10961:10045"},
        submitNpcId = 10960,
        submitRewardStr = {"R1045"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10046] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10968,
        autoDoNextTask = 10047,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "상대를 얕보는 것은 경기의 금기이며,언제나 최선을 다해야 합니다!",
        goalDesc = {"전력을 다하다"},
        id = 10046,
        missiondone = {"NT10047"},
        name = "무도",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10967", "NC10968", "NC10969", "NC10970", "NC10971", "NC10972", "NC10973", "E10967:10046", "E10968:10046"},
        submitNpcId = 10968,
        submitRewardStr = {"R1046"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10047] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,4",
        acceptConditionStr = {"AI:11611:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10974,
        autoDoNextTask = 10048,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"TI:11611:1:0"},
        description = "상처를 치유한 후 최후의 일전을 준비하세요.",
        goalDesc = {"이루의 약"},
        id = 10047,
        missiondone = {"NT10048"},
        name = "응원",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10974", "NC10975", "E10974:10047", "E10975:10047"},
        submitNpcId = 10975,
        submitRewardStr = {"R1047"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10048] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10976,
        autoDoNextTask = 10049,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "상처투성이가 된 상대를 보고 중화는 어떻게 해야 할지 막막했습니다.",
        goalDesc = {"망설이다"},
        id = 10048,
        missiondone = {"NT10049"},
        name = "설득",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10976", "NC10977", "TTRACE10976:101000:17.8:15.5", "E10976:10048", "E10977:10048"},
        submitNpcId = 10977,
        submitRewardStr = {"R1048"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10049] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10978,
        autoDoNextTask = 10050,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "파이널 대결이 시작됩니다.",
        goalDesc = {"우승의 전투"},
        id = 10049,
        missiondone = {"NT10050"},
        name = "마지막 전투",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10978", "NC10979", "NC10980", "NC10981", "NC10982", "NC10983", "NC10984", "E10978:10049", "E10982:10049"},
        submitNpcId = 10978,
        submitRewardStr = {"R1049"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10050] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10986,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "각자의 신앙을 위해 최선을 다하세요!",
        goalDesc = {"마음속의 믿음"},
        id = 10050,
        missiondone = {"NT10051"},
        name = "의지",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10985", "NC10986", "NC10987", "NC10988", "NC10989", "NC10990", "NC10991", "E10986:10050"},
        submitNpcId = 10986,
        submitRewardStr = {"R1050"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10051] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10993,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "제98회 세계 무도대회의 파이널 우승자가 탄생했습니다!",
        goalDesc = {"우승자 단생"},
        id = 10051,
        missiondone = {"NT10053"},
        name = "영광의 전투",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10992", "NC10993", "NC10994", "NC10995", "NC10996", "NC10997", "NC10998", "E10992:10051", "E10993:10051"},
        submitNpcId = 10993,
        submitRewardStr = {"R1051"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10052] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10999,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "대회 후 선언할 일이 있다고 하여,중화와 함께 형명을 찾아가세요.",
        goalDesc = {"보상"},
        id = 10052,
        missiondone = {"NT10053"},
        name = "경기 후",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10999", "NC11000", "TTRACE10999:101000:3.6:4", "E10999:10052", "E5012:10052"},
        submitNpcId = 5012,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10053] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5012,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "어떤 이유로,통수부에는 신병 3명을 받아들였습니다.",
        goalDesc = {"신병 초대"},
        id = 10053,
        missiondone = {"NT10194"},
        name = "놀라다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11001", "NC11002", "E11001:10053", "E11002:10053", "E5012:10053"},
        submitNpcId = 5012,
        submitRewardStr = {"R1053"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10054] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11004,
        autoDoNextTask = 10194,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "새로운 파트너 단을 만났습니다.",
        goalDesc = {"단의 초대"},
        id = 10054,
        missiondone = {"NT10194"},
        name = "신규 파트너",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11003", "NC11004", "E11003:10054", "E11004:10054"},
        submitNpcId = 11004,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10055] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11005,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "갑자기 도움을 청하는 요괴를 만났습니다.",
        goalDesc = {"길 잃은 요족"},
        id = 10055,
        missiondone = {"NT10057"},
        name = "풍죽림",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11005", "NC11006", "TTRACE11005:202000:26.5:3.2", "E11005:10055", "E11006:10055"},
        submitNpcId = 11006,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10056] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11008,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "모처럼 좋은 일을 했는데 결국 나쁜 일이 되었습니다.",
        goalDesc = {"두 종족의 모순"},
        id = 10056,
        missiondone = {"NT10057"},
        name = "화나다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11007", "NC11008", "E11007:10056"},
        submitNpcId = 11008,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10057] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11009,
        autoDoNextTask = 10058,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "상고요족과 계약을 맺었다는 사실을 절대 다른 사람이 알면 안 됩니다.",
        goalDesc = {"요족 숨결"},
        id = 10057,
        missiondone = {"NT10058"},
        name = "후퇴",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11009", "NC11010", "E11009:10057", "E11010:10057"},
        submitNpcId = 11009,
        submitRewardStr = {"R1057"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10058] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11011,
        autoDoNextTask = 10059,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:16.5:18.5"},
        description = "요족의 추격을 받고 서둘러 풍죽림을 떠났습니다.",
        goalDesc = {"제두로 복귀"},
        id = 10058,
        missiondone = {"NT10059"},
        name = "철수",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11011", "E11011:10058"},
        submitNpcId = 11011,
        submitRewardStr = {"R1058"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10059] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11012,
        autoDoNextTask = 10195,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화의 초대를 받아 함께 제두도서관으로 향합니다.",
        goalDesc = {"정보 찾기"},
        id = 10059,
        missiondone = {"NT10195"},
        name = "초대",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11012", "NC11013", "TTRACE11012:101000:10:25", "E11012:10059", "E11013:10059"},
        submitNpcId = 11013,
        submitRewardStr = {"R1059"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10060] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11015,
        autoDoNextTask = 10064,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도서관 앞에서 단을 만났습니다.",
        goalDesc = {"단의 초대"},
        id = 10060,
        missiondone = {"NT10064"},
        name = "우연한 만남",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11014", "NC11015", "E11014:10060", "E11015:10060"},
        submitNpcId = 11015,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10061] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10312,
        autoDoNextTask = 10064,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "여러분은 미래에 어떤 부서로 가고 싶은지 모르겠네요.",
        goalDesc = {"삼인 토론"},
        id = 10061,
        missiondone = {"NT10064"},
        name = "목표",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10312", "E10312:10061"},
        submitNpcId = 10312,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10062] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"AI:11603:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5003,
        autoDoNextTask = 10064,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:13.2:24.6", "TI:11603:1:0"},
        description = "교염의 지시에 따라 증명서를 등록하세요.",
        goalDesc = {"입관 준비"},
        id = 10062,
        missiondone = {"NT10064"},
        name = "신원 등록",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5003:10062"},
        submitNpcId = 5003,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10063] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5003,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "제국 기록사구는 모든 사람이 들어갈 수 있는 곳이 아닐거라고 생각하지 못했습니다.",
        goalDesc = {"출입 불가"},
        id = 10063,
        missiondone = {"NT10064"},
        name = "특수 지역",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E10313:10063", "E5003:10063"},
        submitNpcId = 5003,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10064] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5003,
        autoDoNextTask = 10065,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "먼저 공공구역에서 자료를 열람하기로 결정했습니다.",
        goalDesc = {"열람 방법"},
        id = 10064,
        missiondone = {"NT10065"},
        name = "자문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E10314:10064", "E10315:10064", "E5003:10064"},
        submitNpcId = 5003,
        submitRewardStr = {"R1064"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10065] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10316,
        autoDoNextTask = 10066,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "자료를 통해 통수부를 알아보세요.",
        goalDesc = {"통수부"},
        id = 10065,
        missiondone = {"NT10066"},
        name = "자료",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10316", "E10316:10065", "NC10317", "E10317:10065"},
        submitNpcId = 10316,
        submitRewardStr = {"R1065"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10066] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"AI:11610:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10319,
        autoDoNextTask = 10067,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "자료를 제두 도서관의 관리 교염에게 돌려주세요.",
        goalDesc = {"자료 반환"},
        id = 10066,
        missiondone = {"NT10067"},
        name = "교염",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"I11610:1", "NC10318", "E10318:10066", "NC10319", "E10319:10066", "E5003:10066"},
        submitNpcId = 5003,
        submitRewardStr = {"R1066"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10067] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5003,
        autoDoNextTask = 10068,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도서관에 7년 전 사건에 대한 기록이 있는지 보세요.",
        goalDesc = {"기밀 이벤트"},
        id = 10067,
        missiondone = {"NT10068"},
        name = "조사",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5003:10067"},
        submitNpcId = 5003,
        submitRewardStr = {"R1067"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10068] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10320,
        autoDoNextTask = 10196,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단과 함께 통수부에 가서 신병표를 제출하세요.",
        goalDesc = {"양식 제출"},
        id = 10068,
        missiondone = {"NT10196"},
        name = "결정하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10320", "E10320:10068", "NC10321", "E10321:10068"},
        submitNpcId = 10320,
        submitRewardStr = {"R1068"},
        taskWalkingTips = "사장님의 시그니처 메뉴가 그립네요!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10069] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10323,
        autoDoNextTask = 10196,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "통수부로 가는 길에 중화를 만났다.",
        goalDesc = {"함께 행동하다"},
        id = 10069,
        missiondone = {"NT10196"},
        name = "우연히 만나다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10323", "E10323:10069"},
        submitNpcId = 10323,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10070] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10324,
        autoDoNextTask = 10196,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "통수부 집행관에게 결심을 밝히세요.",
        goalDesc = {"백 찾기"},
        id = 10070,
        missiondone = {"NT10196"},
        name = "신병",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10324", "E10324:10070"},
        submitNpcId = 10324,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10071] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"AI:11603:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10612,
        autoDoNextTask = 10196,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:8.16:24.3", "TI:11603:1:0"},
        description = "통수부 집행관의 신병 부하가 되세요!",
        goalDesc = {"초기 소개"},
        id = 10071,
        missiondone = {"NT10196"},
        name = "입사",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10612", "E10612:10071", "E10613:10071"},
        submitNpcId = 10612,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10072] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10615,
        autoDoNextTask = 10196,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "통수부 집행관은 신병들에게 새로운 지령을 내렸습니다.",
        goalDesc = {"집행관 흑"},
        id = 10072,
        missiondone = {"NT10196"},
        name = "가르치다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10614", "NC10615", "E10614:10072", "E10615:10072"},
        submitNpcId = 10615,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10073] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10617,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집행관은 신병들에게 생동감이 넘치는 수업을 했습니다.",
        goalDesc = {"흑의 경고"},
        id = 10073,
        missiondone = {"NT10074"},
        name = "신병일전",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10617", "NC10619", "E10617:10073", "E10619:10073"},
        submitNpcId = 10617,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10074] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10620,
        autoDoNextTask = 10075,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집행관 백은 모두에게 두툼한 자료를 주었는데,위에 기록된 것은 뜻밖에도?",
        goalDesc = {"정보 기억"},
        id = 10074,
        missiondone = {"NT10075"},
        name = "신규 퀘스트",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10620", "E10620:10074"},
        submitNpcId = 10620,
        submitRewardStr = {"R1074"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10075] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10623,
        autoDoNextTask = 10076,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "전설에 의하면,광석의 도시는 일찍이 약재로 쓸 수 있는 광물이 많이 생산되었다고 합니다.",
        goalDesc = {"회생석"},
        id = 10075,
        missiondone = {"NT10076"},
        name = "독특한 광물",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10623", "NC10624", "E10623:10075", "E10624:10075"},
        submitNpcId = 10623,
        submitRewardStr = {"R1075"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10076] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10625,
        autoDoNextTask = 10077,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "회생석과 요괴족은 도대체 어떤 관련이 있는 걸까요?",
        goalDesc = {"돌과 요족"},
        id = 10076,
        missiondone = {"NT10077"},
        name = "질문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10625", "E10625:10076", "E10625:10076", "E10625:10076"},
        submitNpcId = 10625,
        submitRewardStr = {"R1076"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10077] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10628,
        autoDoNextTask = 10078,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집행관 백이 광석의 도시에서 미친 요괴를 체포한 것에 대해 말해 달라고 요청했습니다.",
        goalDesc = {"광포한 요괴"},
        id = 10077,
        missiondone = {"NT10078"},
        name = "요족의란",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10628", "E10628:10077", "E10629:10077", "E10630:10077"},
        submitNpcId = 10628,
        submitRewardStr = {"R1077"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10078] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10631,
        autoDoNextTask = 10079,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도서관을 찾아 7년 전 월견도 사건을 조사하세요.",
        goalDesc = {"월견도 사건"},
        id = 10078,
        missiondone = {"NT10079"},
        name = "조사",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10631", "NC10632", "E10631:10078", "E10632:10078"},
        submitNpcId = 10631,
        submitRewardStr = {"R1078"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10079] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5003,
        autoDoNextTask = 10080,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "공식 자료에 7년 전 월견도 사건이 이렇게 많이 기록될 줄은 몰랐습니다!",
        goalDesc = {"봉인된 사건"},
        id = 10079,
        missiondone = {"NT10080"},
        name = "비밀",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5003:10079"},
        submitNpcId = 5003,
        submitRewardStr = {"R1079"},
        taskWalkingTips = "도서관에서 올해의 일을 찾을 수 있을까요?7년 전 월견도의 그날 밤......;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10080] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10633,
        autoDoNextTask = 10081,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:9:7"},
        description = "3인조 부대가 광석의 도시를 향해 출발!",
        goalDesc = {"광석의 도시"},
        id = 10080,
        missiondone = {"NT10081"},
        name = "사건 조사",
        playid = 10004,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10633", "NC10634", "E10633:10080", "E10634:10080"},
        submitNpcId = 10633,
        submitRewardStr = {"R1080"},
        taskWalkingTips = "자료를 찾다보니 시간을 깜박했네요!늦지는 않았겠네요.;none;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10081] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10019,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "방금 광석의 도시에 도착한 세 사람은 곧 특별한 상황에 직면합니다!",
        goalDesc = {"습격 당하다"},
        id = 10081,
        missiondone = {"NT10082"},
        name = "잘못된 시작",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10019", "NC10020", "NC10021", "E10019:10081", "E10020:10081", "E10021:10081"},
        submitNpcId = 10019,
        submitRewardStr = {"R1081"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10082] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10022,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "갑자기 건장한 남자가 나타났고,신부 걸수도 잇달아 왔습니다.",
        goalDesc = {"신부 걸수"},
        id = 10082,
        missiondone = {"NT10083"},
        name = "구조",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10022", "NC10023", "E10022:10082", "E10023:10082"},
        submitNpcId = 10022,
        submitRewardStr = {"R1082"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10083] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10024,
        autoDoNextTask = 10084,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신부 걸수에게 곰패의 문제를 알아봅시다.",
        goalDesc = {"유랑인"},
        id = 10083,
        missiondone = {"NT10084"},
        name = "곰패",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10024", "NC10025", "E10024:10083", "E10025:10083"},
        submitNpcId = 10024,
        submitRewardStr = {"R1083"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10084] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10026,
        autoDoNextTask = 10085,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "곰패의 사건을 따라 조사 해보세요.",
        goalDesc = {"새로운 의심"},
        id = 10084,
        missiondone = {"NT10085"},
        name = "논의",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10026", "NC10027", "E10026:10084", "E10027:10084"},
        submitNpcId = 10026,
        submitRewardStr = {"R1084"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10085] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10028,
        autoDoNextTask = 10086,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "곰패를 발견한 호위대에게 당시 상황을 물었습니다.",
        goalDesc = {"최초의 발견자"},
        id = 10085,
        missiondone = {"NT10086"},
        name = "조사 지속",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10028", "TTRACE10028:200000:24.5:2.3", "NC10029", "NC10030", "E10028:10085", "E10029:10085", "E10030:10085"},
        submitNpcId = 10029,
        submitRewardStr = {"R1085"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10086] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10031,
        autoDoNextTask = 10087,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "곰패를 발견한 호위대에게 당시 상황을 물었습니다.",
        goalDesc = {"호위대"},
        id = 10086,
        missiondone = {"NT10087"},
        name = "단서",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10031", "NC10032", "NC10033", "NC10034", "E10031:10086", "E10032:10086", "E10033:10086", "E10034:10086"},
        submitNpcId = 10031,
        submitRewardStr = {"R1086"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10087] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10035,
        autoDoNextTask = 10088,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "호위대 팀장의 말에 따라,작은 단서를 획득했습니다.",
        goalDesc = {"1달 전"},
        id = 10087,
        missiondone = {"NT10088"},
        name = "논의",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10035", "NC10036", "E10035:10087", "E10036:10087"},
        submitNpcId = 10035,
        submitRewardStr = {"R1087"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10088] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10037,
        autoDoNextTask = 10089,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:9:14"},
        description = "호위대에게 회생석에 대한 상황을 물었습니다.",
        goalDesc = {"증언"},
        id = 10088,
        missiondone = {"NT10089"},
        name = "조사 계속",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10037", "NC10038", "NC10039", "NC10040", "E10037:10088", "E10038:10088", "E10039:10088", "E10040:10088"},
        submitNpcId = 10037,
        submitRewardStr = {"R1088"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10089] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10041,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "회생석의 광산지를 조사하러 떠납니다.",
        goalDesc = {"채광장"},
        id = 10089,
        missiondone = {"NT10090"},
        name = "견학",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10041", "NC10042", "E10041:10089", "E10042:10089"},
        submitNpcId = 10041,
        submitRewardStr = {"R1089"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10090] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5015,
        autoDoNextTask = 10091,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "통수부가 자신의 환자를 다치게 했다고 오해하여 화를 냅니다.",
        goalDesc = {"오해"},
        id = 10090,
        missiondone = {"NT10091"},
        name = "의사의 분노",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5015:10090"},
        submitNpcId = 5015,
        submitRewardStr = {"R1090"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10091] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5015,
        autoDoNextTask = 10185,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "간은 한달 전 곰패를 만났을 때를 회상합니다.",
        goalDesc = {"1달 전"},
        id = 10091,
        missiondone = {"NT10185"},
        name = "회억",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5015:10091"},
        submitNpcId = 5015,
        submitRewardStr = {"R1091"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10092] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5015,
        autoDoNextTask = 10093,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "곰패와 다시 한번 소통을 시도합니다.",
        goalDesc = {"곰패"},
        id = 10092,
        missiondone = {"NT10093"},
        name = "설득",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5014:10092", "E5015:10092"},
        submitNpcId = 5014,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10093] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10043,
        autoDoNextTask = 10094,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "친구들과 곰패의 사건에 대해 이야기를 시도합니다.",
        goalDesc = {"단혼애"},
        id = 10093,
        missiondone = {"NT10094"},
        name = "이벤트 복구",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10043", "NC10044", "E10043:10093", "E10044:10093"},
        submitNpcId = 10043,
        submitRewardStr = {"R1093"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10094] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10045,
        autoDoNextTask = 10095,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화는 불현듯 자신이 들었던 단혼애에 대한 이야기가 떠올랐습니다.",
        goalDesc = {"혼백단결의 곳"},
        id = 10094,
        missiondone = {"NT10095"},
        name = "중화의 경험",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10045", "NC10046", "E10045:10094", "E10045:10094"},
        submitNpcId = 10045,
        submitRewardStr = {"R1094"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10095] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5015,
        autoDoNextTask = 10096,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "의사 간은 곰패의 혈액 샘플을 받아 백에게 가져다주세요.",
        goalDesc = {"혈액 샘플"},
        id = 10095,
        missiondone = {"NT10096"},
        name = "실천",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5015:10095"},
        submitNpcId = 5015,
        submitRewardStr = {"R1095"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10096] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10047,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "혈액 샘플만 이루에게 건네면,뭔가가 검출될지도 모릅니다.",
        goalDesc = {"과학팀"},
        id = 10096,
        missiondone = {"NT10097"},
        name = "목적",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10047", "NC10048", "E10047:10096", "E10048:10096", "E5015:10096"},
        submitNpcId = 5015,
        submitRewardStr = {"R1096"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10097] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10049,
        autoDoNextTask = 10098,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뭐 하려고 뚜루새가 다시 나타났을까요?",
        goalDesc = {"뚜뚜루"},
        id = 10097,
        missiondone = {"NT10098"},
        name = "재회",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10049", "E10049:10097"},
        submitNpcId = 10049,
        submitRewardStr = {"R1097"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10098] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10050,
        autoDoNextTask = 10099,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새 몸에 묶인게 무기라니?",
        goalDesc = {"뚜뚜루"},
        id = 10098,
        missiondone = {"NT10099"},
        name = "체포",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10050", "TTRACE10050:200000:23:19", "E10050:10098", "E5016:10098"},
        submitNpcId = 5016,
        submitRewardStr = {"R1098"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10099] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5016,
        autoDoNextTask = 10100,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새는 회생석과 연관이 있다니?",
        goalDesc = {"봉관장"},
        id = 10099,
        missiondone = {"NT10100"},
        name = "횡재",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5016:10099"},
        submitNpcId = 5016,
        submitRewardStr = {"R1099"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10100] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5016,
        autoDoNextTask = 10101,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "과거의 광석 도시는 원래 이런 모습이었습니다.",
        goalDesc = {"오래된 도시 잔해"},
        id = 10100,
        missiondone = {"NT10101"},
        name = "운수물",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5016:10100"},
        submitNpcId = 5016,
        submitRewardStr = {"R1100"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10101] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5017,
        autoDoNextTask = 10102,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신부님께 과거 광석 도시에 대해 물었습니다.",
        goalDesc = {"성심성당"},
        id = 10101,
        missiondone = {"NT10102"},
        name = "신비한 면",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5017:10101"},
        submitNpcId = 5017,
        submitRewardStr = {"R1101"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10102] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,3",
        acceptConditionStr = {"AI:11606:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5017,
        autoDoNextTask = 10103,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신부를 도와 물자를 봉 관장에게 보내세요.",
        goalDesc = {"걸수의 부탁"},
        id = 10102,
        missiondone = {"NT10103"},
        name = "돕다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"I11606:1", "E5017:10102", "E5016:10102"},
        submitNpcId = 5016,
        submitRewardStr = {"R1102"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10103] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5016,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉 관장은 당신과 겨루기를 원합니다.",
        goalDesc = {"봉의 요청"},
        id = 10103,
        missiondone = {"NT10104"},
        name = "서로에게서 배우다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10103", "E10103:10103", "E5016:10103"},
        submitNpcId = 5016,
        submitRewardStr = {"R1103"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10104] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5016,
        autoDoNextTask = 10105,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "엄숙한 봉 관장이 모처럼 남을 칭찬합니다.",
        goalDesc = {"봉의 일침"},
        id = 10104,
        missiondone = {"NT10105"},
        name = "칭찬",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10104", "E10104:10104", "E5016:10104"},
        submitNpcId = 5016,
        submitRewardStr = {"R1104"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10105] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoDoNextTask = 10186,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "시간이 많이 지체되었네,통수부로 돌아가려던 참이었는데,그때......",
        goalDesc = {"소환"},
        id = 10105,
        missiondone = {"NT10186"},
        name = "돌아가다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:10105"},
        submitNpcId = 5018,
        submitRewardStr = {"R1105"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10106] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoDoNextTask = 10107,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "무언가 규정이 있는 것 같은데,물어보기전에 물건부터 살까?",
        goalDesc = {"수생과 대화"},
        id = 10106,
        missiondone = {"NT10107"},
        name = "값을 깍다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:10106"},
        submitNpcId = 5018,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10107] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoDoNextTask = 10108,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이왕 만난 김에 관련된 상황에대해 물어보고 알아봅시다.",
        goalDesc = {"예행 심문"},
        id = 10107,
        missiondone = {"NT10108"},
        name = "문의하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:10107"},
        submitNpcId = 5018,
        submitRewardStr = {"R1107"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10108] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10327,
        autoDoNextTask = 10109,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "오랫동안 오지 않자 중화와 단은 걱정을 합니다.",
        goalDesc = {"늦은 사람"},
        id = 10108,
        missiondone = {"NT10109"},
        name = "불안",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE10327:101000:18:19", "NC10327", "E10327:10108", "NC10328", "E10328:10108", "NC10329", "E10329:10108"},
        submitNpcId = 10328,
        submitRewardStr = {"R1108"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10109] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10330,
        autoDoNextTask = 10110,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "광석 도시의 회생석 광원에 대한 조사가 완료되어 결과를 백에게 알립니다.",
        goalDesc = {"조사 종료"},
        id = 10109,
        missiondone = {"NT10110"},
        name = "제두로 돌아오다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10330", "E10330:10109", "NC10331", "E10331:10109", "NC10332", "E10332:10109"},
        submitNpcId = 10330,
        submitRewardStr = {"R1109"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10110] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10333,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:28.7:14.7"},
        description = "무의식에 일의 진상을 폭로하여,반드시 돌아가 해결을 해야 합니다.",
        goalDesc = {"기만"},
        id = 10110,
        missiondone = {"NT10111"},
        name = "상심",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10333", "E10333:10110", "NC10334", "E10334:10110", "NC10335", "E10335:10110"},
        submitNpcId = 10333,
        submitRewardStr = {"R1110"},
        taskWalkingTips = "제두에 돌아가 백 나리에게 보고하세요.;괘씸한 사기꾼!;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10111] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoDoNextTask = 10187,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "수생을 찾아 정의를 되찾다.",
        goalDesc = {"패왕 조건"},
        id = 10111,
        missiondone = {"NT10187"},
        name = "고집 부리다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:10111"},
        submitNpcId = 5018,
        submitRewardStr = {"R1111"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10112] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoDoNextTask = 10113,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이......이것이 진정한 회생석.",
        goalDesc = {"껍질 1개"},
        id = 10112,
        missiondone = {"NT10113"},
        name = "보상",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:10112"},
        submitNpcId = 5018,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10113] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoDoNextTask = 10114,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "채석장에서 귀신이 나온다는 소문이 있다?",
        goalDesc = {"새로운 발견"},
        id = 10113,
        missiondone = {"NT10114"},
        name = "소문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:10113"},
        submitNpcId = 5018,
        submitRewardStr = {"R1113"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10114] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10641,
        autoDoNextTask = 10115,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "호위대의 팀장에서 채석장에 귀신이 나온 일에 대해 물어보세요.",
        goalDesc = {"귀신이 들린 사건"},
        id = 10114,
        missiondone = {"NT10115"},
        name = "방문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10641", "E10641:10114"},
        submitNpcId = 10641,
        submitRewardStr = {"R1114"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10115] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10642,
        autoDoNextTask = 10116,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "채석장의 지하를 조사하려면 반드시 누군가의 도움을 받아야 합니다.",
        goalDesc = {"좋은 생각"},
        id = 10115,
        missiondone = {"NT10116"},
        name = "조력자",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10642", "NC10643", "E10642:10115", "E10643:10115"},
        submitNpcId = 10642,
        submitRewardStr = {"R1115"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10116] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5018,
        autoDoNextTask = 10117,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:3.28:17.86"},
        description = "여러분과 함께 채석장 조사에서 복귀를 합니다.",
        goalDesc = {"재조사"},
        id = 10116,
        missiondone = {"NT10117"},
        name = "위협하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5018:10116"},
        submitNpcId = 5018,
        submitRewardStr = {"R1116"},
        taskWalkingTips = "요족에게 어려운 것도 아닙니다!;뭔가를 알아낼 수 있을거라고 믿습니다!;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10117] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10644,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "의외의 수획이 있을줄 알았는데,이렇게 깜짝 놀랄줄이야.",
        goalDesc = {"광굴 조사"},
        id = 10117,
        missiondone = {"NT10118"},
        name = "잠입",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10644", "NC10645", "NC10647", "E10644:10117", "E10645:10117", "E10647:10117"},
        submitNpcId = 10644,
        submitRewardStr = {"R1117"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10118] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10650,
        autoDoNextTask = 10119,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "화상주가 갑자기 나타나 모두를 깜짝 놀라게 했습니다.",
        goalDesc = {"설명"},
        id = 10118,
        missiondone = {"NT10119"},
        name = "공포 후",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10650", "E10650:10118"},
        submitNpcId = 10650,
        submitRewardStr = {"R1118"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10119] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10653,
        autoDoNextTask = 10120,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "깊은 오해",
        goalDesc = {"진실"},
        id = 10119,
        missiondone = {"NT10120"},
        name = "대화 퀘스트",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10653", "E10653:10119"},
        submitNpcId = 10653,
        submitRewardStr = {"R1119"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10120] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"AI:11602:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10655,
        autoDoNextTask = 10121,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "화상주를 도와 봉 관장에게 선물을 보내세요.",
        goalDesc = {"아버지와 딸"},
        id = 10120,
        missiondone = {"NT10121"},
        name = "선물주기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"I11602:1", "E5016:10120", "E10655:10120", "NC10655"},
        submitNpcId = 5016,
        submitRewardStr = {"R1120"},
        taskWalkingTips = "납득하기 어려운 결과입니다.이런 일이 일어날 줄은 몰랐습니다!;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10121] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11016,
        autoDoNextTask = 10122,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "갑자기 시간이 멈추었습니다!",
        goalDesc = {"멈춤"},
        id = 10121,
        missiondone = {"NT10122"},
        name = "적수",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11016", "NC11017", "NC11018", "E11016:10121", "E11017:10121", "E11018:10121"},
        submitNpcId = 11016,
        submitRewardStr = {"R1121"},
        taskWalkingTips = "내가 정말 잘못 맞힌 걸까요?;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10122] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11021,
        autoDoNextTask = 10123,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신비로운 요족이 나타났습니다,그는 도대체 무슨 생각일까요?",
        goalDesc = {"요족 등장록"},
        id = 10122,
        missiondone = {"NT10123"},
        name = "시간이 사라지다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11019", "NC11020", "NC11021", "E11021:10122"},
        submitNpcId = 11021,
        submitRewardStr = {"R1122"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10123] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11024,
        autoDoNextTask = 10124,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이 요족의 스킬은 뜻밖에도 시간을 제어하는 것입니다.",
        goalDesc = {"두개의 그"},
        id = 10123,
        missiondone = {"NT10124"},
        name = "엇갈린 시간과 공간",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11022", "NC11023", "NC11024", "NC11025", "TTRACE11024:200000:9.2:14.5", "E11024:10123", "E11025:10123"},
        submitNpcId = 11025,
        submitRewardStr = {"R1123"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10124] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11026,
        autoDoNextTask = 10125,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE200000:4:18.5"},
        description = "남당주의 스킬을 통해 신비한 소리를 들었습니다.",
        goalDesc = {"시간을 거슬러"},
        id = 10124,
        missiondone = {"NT10125"},
        name = "소리",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11026", "E11026:10124"},
        submitNpcId = 11026,
        submitRewardStr = {"R1124"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10125] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11027,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "기회는 단 한 번뿐인데,그 소리는 도대체 어디서 드려오는 걸까요?",
        goalDesc = {"왕의 약속"},
        id = 10125,
        missiondone = {"NT10126"},
        name = "힌트",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11027", "E11027:10125"},
        submitNpcId = 11027,
        submitRewardStr = {"R1125"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10126] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11028,
        autoDoNextTask = 10127,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중요한 파트너까지 피해를 입게 해서는 안됩니다!",
        goalDesc = {"중요한 사람"},
        id = 10126,
        missiondone = {"NT10127"},
        name = "죄",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11028", "NC11029", "E11028:10126", "E11029:10126"},
        submitNpcId = 11028,
        submitRewardStr = {"R1126"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10127] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11030,
        autoDoNextTask = 10128,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "마음속의 추측을 두 사람에게 알리세요.",
        goalDesc = {"수상한 일"},
        id = 10127,
        missiondone = {"NT10128"},
        name = "논의",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11030", "NC11031", "E11030:10127", "E11031:10127", "E11032:10127"},
        submitNpcId = 11030,
        submitRewardStr = {"R1127"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10128] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11035,
        autoDoNextTask = 10129,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "정보팀의 원작 등장!",
        goalDesc = {"정보팀"},
        id = 10128,
        missiondone = {"NT10129"},
        name = "원작",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11033", "NC11034", "NC11035", "E11033:10128", "E11034:10128", "E11035:10128"},
        submitNpcId = 11035,
        submitRewardStr = {"R1128"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10129] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11038,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백에게 보고의 최신 상황을 보고하세요.",
        goalDesc = {"장점과 단점"},
        id = 10129,
        missiondone = {"NT10130"},
        name = "경고",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11036", "NC11037", "NC11038", "NC11039", "TTRACE11038:101000:7.9:23.5", "E11038:10129", "E11039:10129"},
        submitNpcId = 11039,
        submitRewardStr = {"R1129"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10130] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11041,
        autoDoNextTask = 10131,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단과 중화 모두가 할 말이 있는 것 같습니다.",
        goalDesc = {"어찌할 수 없는"},
        id = 10130,
        missiondone = {"NT10131"},
        name = "슬픔",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11040", "NC11041", "E11040:10130", "E11041:10130"},
        submitNpcId = 11041,
        submitRewardStr = {"R1130"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10131] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10656,
        autoDoNextTask = 10132,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:24:11.76"},
        description = "중화는 당신을 걱정합니다.",
        goalDesc = {"내면의 생각"},
        id = 10131,
        missiondone = {"NT10132"},
        name = "화남",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10656", "E10656:10131", "E10657:10131"},
        submitNpcId = 10656,
        submitRewardStr = {"R1131"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10132] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10658,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "원작이 트집을 잡는데 한 판 붙을까?",
        goalDesc = {"싸움"},
        id = 10132,
        missiondone = {"NT10133"},
        name = "선물주기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10658", "E10658:10132"},
        submitNpcId = 10658,
        submitRewardStr = {"R1132"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10133] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10659,
        autoDoNextTask = 10134,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "원작이 무슨 생각인지는 몰라도,밥 한 끼를 사는 것은 괜찮은 것 같습니다.",
        goalDesc = {"국수 계속 먹기"},
        id = 10133,
        missiondone = {"NT10134"},
        name = "감사",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10659", "NC10660", "TTRACE10659:101000:25:11.76", "E10659:10133", "E10660:10133"},
        submitNpcId = 10660,
        submitRewardStr = {"R1133"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10134] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10661,
        autoDoNextTask = 10135,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "당신은 도대체 누구죠?",
        goalDesc = {"질문"},
        id = 10134,
        missiondone = {"NT10135"},
        name = "원작의 의문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10661", "E10661:10134"},
        submitNpcId = 10661,
        submitRewardStr = {"R1134"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10135] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10662,
        autoDoNextTask = 10136,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "애초에 무도대회에서 우승한다고 해서 통수부에 들어가는 것은 아닙니다.",
        goalDesc = {"보증인"},
        id = 10135,
        missiondone = {"NT10136"},
        name = "막다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10662", "E10662:10135"},
        submitNpcId = 10662,
        submitRewardStr = {"R1135"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10136] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10663,
        autoDoNextTask = 10137,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "드디어 두 번째 퀘스트,단혹애의 수색이 펼쳐집니다.",
        goalDesc = {"단혼애"},
        id = 10136,
        missiondone = {"NT10137"},
        name = "재임용",
        playid = 10005,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10663", "NC10664", "NC10665", "E10663:10136", "E10664:10136", "E10665:10136"},
        submitNpcId = 10663,
        submitRewardStr = {"R1136"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10137] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10666,
        autoDoNextTask = 10138,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:16:16"},
        description = "단혼애에 어떤 비밀이 숨겨져 있는지,수사를 진행해야 합니다!",
        goalDesc = {"단혼애 방문"},
        id = 10137,
        missiondone = {"NT10138"},
        name = "탐색 전개",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10666", "NC10667", "NC10668", "E10666:10137", "E10667:10137", "E10668:10137"},
        submitNpcId = 10666,
        submitRewardStr = {"R1137"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10138] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10669,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "3인조의 분위기가 무겁습니다!",
        goalDesc = {"단과 중화"},
        id = 10138,
        missiondone = {"NT10139"},
        name = "무거운 분위기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10669", "NC10670", "E10669:10138", "E10670:10138"},
        submitNpcId = 10669,
        submitRewardStr = {"R1138"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10139] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10673,
        autoDoNextTask = 10140,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단혼애에 와서 첫 번째 촌민을 만났습니다.",
        goalDesc = {"길을 막는 촌민"},
        id = 10139,
        missiondone = {"NT10140"},
        name = "저지당하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10673", "E10673:10139"},
        submitNpcId = 10673,
        submitRewardStr = {"R1139"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10140] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10674,
        autoDoNextTask = 10141,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "촌민에게 몇 가지 간단한 질문을 하고 그를 따라 채석장으로 가세요.",
        goalDesc = {"촌민에게 질문"},
        id = 10140,
        missiondone = {"NT10141"},
        name = "초보적인 조사",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10674", "TTRACE10674:201000:3.1:13.48", "E10674:10140", "E5039:10140"},
        submitNpcId = 5039,
        submitRewardStr = {"R1140"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10141] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5039,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "파수꾼의 도발!",
        goalDesc = {"파수꾼"},
        id = 10141,
        missiondone = {"NT10142"},
        name = "전투로 확인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11125", "E11125:10141", "E5039:10141"},
        submitNpcId = 5039,
        submitRewardStr = {"R1141"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10142] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5039,
        autoDoNextTask = 10188,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "싸워서 해결할 수 없는 일은 없어!",
        goalDesc = {"서로를 모른다"},
        id = 10142,
        missiondone = {"NT10188"},
        name = "채석장 확인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11126", "E11126:10142", "E5039:10142"},
        submitNpcId = 5039,
        submitRewardStr = {"R1142"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10143] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5039,
        autoDoNextTask = 10144,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단혼애 석장의 경영 상황에 대해 물으세요.",
        goalDesc = {"결석"},
        id = 10143,
        missiondone = {"NT10144"},
        name = "채석장 운영",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11127", "E11127:10143", "E5039:10143"},
        submitNpcId = 5039,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10144] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11128,
        autoDoNextTask = 10145,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단혼애 석장의 안전 문제를 물으세요.",
        goalDesc = {"채석장 조사"},
        id = 10144,
        missiondone = {"NT10145"},
        name = "안전 문제",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11128", "E11128:10144", "E5039:10144"},
        submitNpcId = 11128,
        submitRewardStr = {"R1144"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10145] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11130,
        autoDoNextTask = 10146,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "채석장의 현장에 이상이 없는지 살펴보세요.",
        goalDesc = {"증거 찾기"},
        id = 10145,
        missiondone = {"NT10146"},
        name = "둘러보기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11129", "E11129:10145", "NC11130", "E11130:10145"},
        submitNpcId = 11130,
        submitRewardStr = {"R1145"},
        taskWalkingTips = "이 채석장 정말 크네요!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10146] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11131,
        autoDoNextTask = 10147,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화는 두 사람의 권유에 마음이 아팠습니다.",
        goalDesc = {"중화의 의견"},
        id = 10146,
        missiondone = {"NT10147"},
        name = "설득",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11131", "E11131:10146"},
        submitNpcId = 11131,
        submitRewardStr = {"R1146"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10147] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5039,
        autoDoNextTask = 10148,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:21:17"},
        description = "3인조 부대는 단혼애의 돌마을에 대해 조사를 하기로 했습니다.",
        goalDesc = {"촌락 조사"},
        id = 10147,
        missiondone = {"NT10148"},
        name = "돌 마을",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E11132:10147", "E5039:10147"},
        submitNpcId = 5039,
        submitRewardStr = {"R1147"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10148] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11133,
        autoDoNextTask = 10149,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "촌민들이 요괴족에게 습경당한 상황을 알아보세요.",
        goalDesc = {"광포한 요족"},
        id = 10148,
        missiondone = {"NT10149"},
        name = "촌민의 묘사",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11133", "E11133:10148"},
        submitNpcId = 11133,
        submitRewardStr = {"R1148"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10149] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11137,
        autoDoNextTask = 10150,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "멀리서 큰 소리로 도움을 청하는 촌민에게 무슨 일이 생긴건가요?",
        goalDesc = {"촌락 구조"},
        id = 10149,
        missiondone = {"NT10150"},
        name = "공격",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11137", "NC10574", "E11137:10149", "NC11134", "E11134:10149", "NC11135", "E11135:10149", "TTRACE11134:201000:24:8"},
        submitNpcId = 11135,
        submitRewardStr = {"R1149"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10150] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11136,
        autoDoNextTask = 10151,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이 마을에 침입한 요괴를 반드시 체포해야 합니다!",
        goalDesc = {"사희와 대결"},
        id = 10150,
        missiondone = {"NT10151"},
        name = "광포한 요족",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11136", "E11136:10150"},
        submitNpcId = 11136,
        submitRewardStr = {"R1150"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10151] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10051,
        autoDoNextTask = 10152,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "사희는 요족의 언어를 사용하여 당신에게 조용히 메세지를 전합니다.",
        goalDesc = {"참과 거짓"},
        id = 10151,
        missiondone = {"NT10152"},
        name = "밀어",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10051", "E10051:10151"},
        submitNpcId = 10051,
        submitRewardStr = {"R1151"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10152] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10052,
        autoDoNextTask = 10153,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "사희가 도망치자,단은 정말 화가 났습니다.",
        goalDesc = {"단의 분노"},
        id = 10152,
        missiondone = {"NT10153"},
        name = "비난",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10052", "NC10053", "E10052:10152", "E10053:10152"},
        submitNpcId = 10052,
        submitRewardStr = {"R1152"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10153] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5022,
        autoDoNextTask = 10154,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "촌민들이 하는 말이 다 사실인가요?",
        goalDesc = {"촌민 위안"},
        id = 10153,
        missiondone = {"NT10154"},
        name = "불평",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5022:10153"},
        submitNpcId = 5022,
        submitRewardStr = {"R1153"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10154] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"AI:11604:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5021,
        autoDoNextTask = 10155,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:19:8", "TI:11604:1:0"},
        description = "마을은 감사의 표시로 당신에게 상처치유를 위한 약품을 줬습니다.",
        goalDesc = {"상처 치료"},
        id = 10154,
        missiondone = {"NT10155"},
        name = "고마움",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5021:10154"},
        submitNpcId = 5021,
        submitRewardStr = {"R1154"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10155] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5021,
        autoDoNextTask = 10156,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "마을에 곰패의 일에 대해 알아봐 달라고 부탁을 하세요.",
        goalDesc = {"촌장의 말"},
        id = 10155,
        missiondone = {"NT10156"},
        name = "실종자",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5021:10155"},
        submitNpcId = 5021,
        submitRewardStr = {"R1155"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10156] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10054,
        autoDoNextTask = 10157,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화는 당신과 단의 상황이 걱정되지만 어쩔 수 없습니다.",
        goalDesc = {"중화의 위안"},
        id = 10156,
        missiondone = {"NT10157"},
        name = "걱정",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10054", "E10054:10156"},
        submitNpcId = 10054,
        submitRewardStr = {"R1156"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10157] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10055,
        autoDoNextTask = 10158,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "군중은 통수부에 불만이 가득해 보입니다.",
        goalDesc = {"두 종족의 모순"},
        id = 10157,
        missiondone = {"NT10158"},
        name = "숨기다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10055", "NC10056", "E10055:10157", "E10056:10157"},
        submitNpcId = 10055,
        submitRewardStr = {"R1157"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10158] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10057,
        autoDoNextTask = 10159,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "촌민들이 평화속에 숨겨진 위험을 실토했습니다.",
        goalDesc = {"평화 아래"},
        id = 10158,
        missiondone = {"NT10159"},
        name = "숨겨진 위험",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10057", "E10057:10158"},
        submitNpcId = 10057,
        submitRewardStr = {"R1158"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10159] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10058,
        autoDoNextTask = 10160,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞에 있는 사람은 도대체 누구일까요?",
        goalDesc = {"공격"},
        id = 10159,
        missiondone = {"NT10160"},
        name = "미지의 인물",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10058", "E10058:10159"},
        submitNpcId = 10058,
        submitRewardStr = {"R1159"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10160] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10059,
        autoDoNextTask = 10161,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "납치될 뻔한 순간 뚜루새가 나타났습니다!",
        goalDesc = {"구조"},
        id = 10160,
        missiondone = {"NT10161"},
        name = "위협하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10059", "NC10060", "E10059:10160", "E10060:10160"},
        submitNpcId = 10059,
        submitRewardStr = {"R1160"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10161] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10675,
        autoDoNextTask = 10162,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새를 데리고 중화를 만나러 갑니다.",
        goalDesc = {"중화 찾기"},
        id = 10161,
        missiondone = {"NT10162"},
        name = "합류",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10675", "NC10676", "TTRACE10675:201000:19:16.5", "E10675:10161", "E10676:10161"},
        submitNpcId = 10676,
        submitRewardStr = {"R1161"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10162] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10678,
        autoDoNextTask = 10163,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루새는?당신은 누구죠?",
        goalDesc = {"언니"},
        id = 10162,
        missiondone = {"NT10163"},
        name = "산 자의 큰 변화",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10678", "E10678:10162"},
        submitNpcId = 10678,
        submitRewardStr = {"R1162"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10163] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10679,
        autoDoNextTask = 10164,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "통수부로 데려오려 했는데,미처......",
        goalDesc = {"난폭한 공격"},
        id = 10163,
        missiondone = {"NT10164"},
        name = "이상한 요족",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10679", "E10679:10163"},
        submitNpcId = 10679,
        submitRewardStr = {"R1163"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10164] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10681,
        autoDoNextTask = 10165,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "순간,뚜루새가 다시 돌아온건가요?",
        goalDesc = {"무슨 일이에요"},
        id = 10164,
        missiondone = {"NT10165"},
        name = "눈 깜짝할 사이에",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10681", "E10681:10164"},
        submitNpcId = 10681,
        submitRewardStr = {"R1164"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10165] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10683,
        autoDoNextTask = 10166,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신기한 뚜루새?",
        goalDesc = {"변신한 뚜루"},
        id = 10165,
        missiondone = {"NT10166"},
        name = "추측",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10683", "E10683:10165"},
        submitNpcId = 10683,
        submitRewardStr = {"R1165"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10166] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10684,
        autoDoNextTask = 10167,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단에게 정중히 사과를 한다 해도 용서하지 않을 거야.",
        goalDesc = {"용서를 구하다"},
        id = 10166,
        missiondone = {"NT10167"},
        name = "사과",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10684", "NC10685", "E10684:10166", "E10685:10166"},
        submitNpcId = 10684,
        submitRewardStr = {"R1166"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10167] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10686,
        autoDoNextTask = 10168,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:15.96:15.86"},
        description = "눈앞의 사람들이 너무 대단해서,먼저 철수할 수밖에 없다!",
        goalDesc = {"판관"},
        id = 10167,
        missiondone = {"NT10168"},
        name = "갑자기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10686", "E10686:10167"},
        submitNpcId = 10686,
        submitRewardStr = {"R1167"},
        taskWalkingTips = "none;그에게서 아주 무서운 기운이 나네;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10168] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10687,
        autoDoNextTask = 10169,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도망갈 수 없는 이상 힘껏 싸우는 수밖에!",
        goalDesc = {"전쟁 준비"},
        id = 10168,
        missiondone = {"NT10169"},
        name = "탈출 불가",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10687", "E10687:10168"},
        submitNpcId = 10687,
        submitRewardStr = {"R1168"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10169] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10688,
        autoDoNextTask = 10170,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "손에 남은 페이지는 요영계의 책에서 나온 것이다!",
        goalDesc = {"남은 페이지의 진상"},
        id = 10169,
        missiondone = {"NT10170"},
        name = "요영계",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10688", "E10688:10169"},
        submitNpcId = 10688,
        submitRewardStr = {"R1169"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10170] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11112,
        autoDoNextTask = 10171,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "원작의 출현,묘지 대집합!",
        goalDesc = {"원작을 따라가다"},
        id = 10170,
        missiondone = {"NT10171"},
        name = "만나다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11112", "NC11113", "NC11114", "E11112:10170", "TTRACE11112:201000:10:18", "E11113:10170", "E5020:10170"},
        submitNpcId = 11113,
        submitRewardStr = {"R1170"},
        taskWalkingTips = "잠깐...저거 원작인가요?;none;none",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10171] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11115,
        autoDoNextTask = 10172,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "정보 팀장 추무는 등장하지 않는다!",
        goalDesc = {"추무의 비난"},
        id = 10171,
        missiondone = {"NT10172"},
        name = "가르치다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11115", "NC11116", "NC11117", "E11115:10171", "E5020:10171"},
        submitNpcId = 11115,
        submitRewardStr = {"R1171"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10172] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5020,
        autoDoNextTask = 10173,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "알고 보니 암암리에 일어난 모든 일들이 다 알려졌네요.",
        goalDesc = {"염탐"},
        id = 10172,
        missiondone = {"NT10189"},
        name = "폭로",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11120", "E11118:10172", "E5020:10172", "E11120:10172", "E11119:10172"},
        submitNpcId = 5020,
        submitRewardStr = {"R1172"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10173] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5020,
        autoDoNextTask = 10174,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "정보팀의 나리들에게 단혼애에 대한 조사를 보고합니다.",
        goalDesc = {"사건 보고서"},
        id = 10173,
        missiondone = {"NT10174"},
        name = "실종 사건",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11123", "E11121:10173", "E11122:10173", "E11123:10173", "E5020:10173"},
        submitNpcId = 5020,
        submitRewardStr = {"R1173"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10174] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5020,
        autoDoNextTask = 10175,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "소부대의 내부 갈등은 차마 눈여겨 볼 수 없습니다.",
        goalDesc = {"장관의 분노"},
        id = 10174,
        missiondone = {"NT10175"},
        name = "교훈",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5020:10174"},
        submitNpcId = 5020,
        submitRewardStr = {"R1174"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10175] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,6",
        acceptConditionStr = {"AI:11604:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5020,
        autoDoNextTask = 10176,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE201000:9:18", "TI:11604:1:0"},
        description = "추무 나리는 보는 것만큼 엄격하지 않습니다.",
        goalDesc = {"자비를 베풀다"},
        id = 10175,
        missiondone = {"NT10176"},
        name = "감단",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5020:10175"},
        submitNpcId = 5020,
        submitRewardStr = {"R1175"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10176] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11111,
        autoDoNextTask = 10177,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백에게 방문 결과를 제출하세요.",
        goalDesc = {"제두로 돌아오다"},
        id = 10176,
        missiondone = {"NT10177"},
        name = "백에게 답장",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11111", "E11111:10176"},
        submitNpcId = 11111,
        submitRewardStr = {"R1176"},
        taskWalkingTips = "이번 조사는 실패한 셈이죠......;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10177] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11124,
        autoDoNextTask = 10178,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "퀘스트를 완료 했으니 단을 찾으세요.",
        goalDesc = {"단과 대화"},
        id = 10177,
        missiondone = {"NT10178"},
        name = "화해",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11124", "E11124:10177"},
        submitNpcId = 11124,
        submitRewardStr = {"R1177"},
        taskWalkingTips = "백 나리께서 화를 안 내시다니?;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10178] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11110,
        autoDoNextTask = 10179,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백에게 담보를 선 이유를 캐물으세요.",
        goalDesc = {"백의 목적"},
        id = 10178,
        missiondone = {"NT10179"},
        name = "왜",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11110", "E11110:10178"},
        submitNpcId = 11110,
        submitRewardStr = {"R1178"},
        taskWalkingTips = "도대체 왜......;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10179] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11108,
        autoDoNextTask = 10180,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "호비가 새끼 고양이를 찾을 수 있도록 도와주세요.",
        goalDesc = {"고양이 찾기"},
        id = 10179,
        missiondone = {"NT10180"},
        name = "호비의 부탁",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE11108:101000:13.8:20.6", "NC11108", "NC11109", "E11108:10179", "E11109:10179"},
        submitNpcId = 11109,
        submitRewardStr = {"R1179"},
        taskWalkingTips = "백 나리을 만난 기억이 전혀 없어요!",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10180] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11106,
        autoDoNextTask = 10181,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞의 요괴들은 도대체 무슨 생각을 하는걸까요?",
        goalDesc = {"호비 따라가기"},
        id = 10180,
        missiondone = {"NT10181"},
        name = "가이드",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11106", "E11106:10180", "TTRACE11106:101000:43:25", "NC11107", "E11107:10180"},
        submitNpcId = 11107,
        submitRewardStr = {"R1180"},
        taskWalkingTips = "none;날 데리고 어디로 가려고?왜 성당로 부른 거지?;none",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10181] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11105,
        autoDoNextTask = 10182,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "성당에 뭐가 있나요?",
        goalDesc = {"요족 지시"},
        id = 10181,
        missiondone = {"NT10182"},
        name = "암시",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11105", "E11105:10181", "E11104:10181"},
        submitNpcId = 11105,
        submitRewardStr = {"R1181"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10182] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,7",
        acceptConditionStr = {"AI:11606:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11103,
        autoDoNextTask = 10183,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신부님을 도와 단혼애의 마을에 물자를 배달하세요.",
        goalDesc = {"물자 운송"},
        id = 10182,
        missiondone = {"NT10183"},
        name = "물자",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"I11606:1", "NC11103", "E11103:10182", "E5021:10182"},
        submitNpcId = 5021,
        submitRewardStr = {"R1182"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10183] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11102,
        autoDoNextTask = 10184,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신벌?어디서 들어본 것 같은데!",
        goalDesc = {"사자 강림"},
        id = 10183,
        missiondone = {"NT10184"},
        name = "신의 형벌",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11102", "E11102:10183"},
        submitNpcId = 11102,
        submitRewardStr = {"R1183"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10184] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11100,
        autoDoNextTask = 10197,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞에 있는 사람이 집행관의 배신자라니!",
        goalDesc = {"추무의 분노"},
        id = 10184,
        missiondone = {"NT10197"},
        name = "배신도",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11100", "E11100:10184", "NC11101", "E11101:10184"},
        submitNpcId = 11100,
        submitRewardStr = {"R1184"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10185] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5014,
        autoDoNextTask = 10093,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "곰패와 다시 한번 소통을 시도합니다.",
        goalDesc = {"곰패에게 질문"},
        id = 10185,
        missiondone = {"NT10093"},
        name = "설득",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5014:10185"},
        submitNpcId = 5014,
        submitRewardStr = {"R1185"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10186] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11138,
        autoDoNextTask = 10107,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "호위팀 대원과 대화를 나누세요.",
        goalDesc = {"이상한 사람"},
        id = 10186,
        missiondone = {"NT10107"},
        name = "이방인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11138", "E11138:10186"},
        submitNpcId = 11138,
        submitRewardStr = {"R1186"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10187] = {
        AcceptCallPlot = 0,
        ChapterFb = "3,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11140,
        autoDoNextTask = 10113,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이......이것이 진정한 회생석.",
        goalDesc = {"껍질 1개"},
        id = 10187,
        missiondone = {"NT10113"},
        name = "작은 보상",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11139", "NC11140", "E5018:10187", "E11139:10187", "E11140:10187"},
        submitNpcId = 11140,
        submitRewardStr = {"R1187"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10188] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11141,
        autoDoNextTask = 10144,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단혼애 석장의 경영 상황에 대해 물으세요.",
        goalDesc = {"석재 운영"},
        id = 10188,
        missiondone = {"NT10144"},
        name = "채석장",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11141", "E11141:10188", "E5039:10188"},
        submitNpcId = 11141,
        submitRewardStr = {"R1188"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10189] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11142,
        autoDoNextTask = 10174,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "정보팀의 나리들에게 단혼애에 대한 조사를 보고합니다.",
        goalDesc = {"사건 보고서"},
        id = 10189,
        missiondone = {"NT10174"},
        name = "실종 사건",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11142", "E11142:10189", "E5020:10189"},
        submitNpcId = 11142,
        submitRewardStr = {},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10190] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11143,
        autoDoNextTask = 10025,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "비록 행동은 실패했지만 결과를 그 나리에게도 알려야 합니다.",
        goalDesc = {"통수부로 이동"},
        id = 10190,
        missiondone = {"NT10025"},
        name = "체포 결과",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11143", "NC11144", "NC11145", "E11143:10190", "E11144:10190", "E11145:10190"},
        submitNpcId = 11143,
        submitRewardStr = {"R1190"},
        taskWalkingTips = "도망가게 하다니!어쨌든 사과부터 해야겠어요.;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10191] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5016,
        autoDoNextTask = 10192,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉관장의 조언.",
        goalDesc = {"상기시키다"},
        id = 10191,
        missiondone = {"NT10192"},
        name = "요족의 재앙",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11146", "E11146:10191", "E5016:10191"},
        submitNpcId = 11146,
        submitRewardStr = {"R1191"},
        taskWalkingTips = "이곳은 정말 황량하네요!앞에 있는 것은?;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10192] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11147,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족의 습격을 받고 신부가 등장했습니다.",
        goalDesc = {"신부의 말"},
        id = 10192,
        missiondone = {"NT10038"},
        name = "공격 후",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE11149:101000:13:14", "NC11147", "NC11148", "NC11149", "NC11150", "NC11151", "E11147:10192", "E11148:10192", "E11149:10192", "E11150:10192", "E11151:10192"},
        submitNpcId = 11151,
        submitRewardStr = {"R1192"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10193] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11152,
        autoDoNextTask = 10043,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "경기 후 상처를 치료해야 합니다.",
        goalDesc = {"예선 후"},
        id = 10193,
        missiondone = {"NT10043"},
        name = "걱정",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11152", "NC11153", "NC11154", "NC11155", "NC11156", "NC11157", "NC11158", "NC11159", "NC11160", "E11152:10193", "E11153:10193", "E11154:10193"},
        submitNpcId = 11154,
        submitRewardStr = {"R1193"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10194] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11161,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "갑자기 도움을 청하는 요괴를 만났습니다.",
        goalDesc = {"길 잃은 요족"},
        id = 10194,
        missiondone = {"NT10057"},
        name = "풍죽림 첫 탐색",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11161", "NC11162", "TTRACE11161:202000:26.5:3.2", "E11161:10194", "E11162:10194"},
        submitNpcId = 11162,
        submitRewardStr = {"R1194"},
        taskWalkingTips = "저건 요족?;none;none",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10195] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11164,
        autoDoNextTask = 10064,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도서관 문 앞에서 교염의 제지를 받았습니다.",
        goalDesc = {"막다"},
        id = 10195,
        missiondone = {"NT10064"},
        name = "도서관",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11163", "NC11164", "E11163:10195", "E11164:10195", "E5003:10195"},
        submitNpcId = 5003,
        submitRewardStr = {"R1195"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10196] = {
        AcceptCallPlot = 0,
        ChapterFb = "2,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11166,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집행관은 신병들에게 생동감이 넘치는 수업을 했습니다.",
        goalDesc = {"신병일전"},
        id = 10196,
        missiondone = {"NT10074"},
        name = "흑의 경고",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11166", "NC11165", "E11166:10196", "E11165:10196"},
        submitNpcId = 11166,
        submitRewardStr = {"R1196"},
        taskWalkingTips = "드디어 통수부에 합류하다니!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10197] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11167,
        autoDoNextTask = 10198,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "원작의 제지,통수부의 최고 기밀!",
        goalDesc = {"원작을 따라가다"},
        id = 10197,
        missiondone = {"NT10198"},
        name = "원한",
        playid = 10006,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11167", "NC11168", "NC11169", "NC11170", "E11167:10197", "E11168:10197", "E11169:10197", "E11170:10197", "TTRACE11167:101000:8:22"},
        submitNpcId = 11168,
        submitRewardStr = {"R1197"},
        taskWalkingTips = "none;도대체 어떻게 된 일이죠?;none",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10198] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11171,
        autoDoNextTask = 10199,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "비밀의 한 귀퉁이가 곧 무너질 것입니다.",
        goalDesc = {"특수 회의"},
        id = 10198,
        missiondone = {"NT10199"},
        name = "긴급 소집 명령",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11171", "NC11172", "E11171:10198", "E11172:10198"},
        submitNpcId = 11171,
        submitRewardStr = {"R1198"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10199] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11174,
        autoDoNextTask = 10200,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백에게 통수부의 최종 목적을 알아보세요.",
        goalDesc = {"배후의 사람"},
        id = 10199,
        missiondone = {"NT10200"},
        name = "목적",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11173", "NC11174", "E11173:10199", "E11174:10199"},
        submitNpcId = 11174,
        submitRewardStr = {"R1199"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10200] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11175,
        autoDoNextTask = 10201,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집행관 흑열이 심문을 시작했습니다!",
        goalDesc = {"고급팀"},
        id = 10200,
        missiondone = {"NT10201"},
        name = "심문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11175", "E11175:10200"},
        submitNpcId = 11175,
        submitRewardStr = {"R1200"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10201] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11176,
        autoDoNextTask = 10202,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "역후의 변화가 회생석과 관련이 있는걸까요?",
        goalDesc = {"혼란시키다"},
        id = 10201,
        missiondone = {"NT10202"},
        name = "추측",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11176", "NC11177", "NC11178", "E11176:10201", "E11177:10201", "E11178:10201"},
        submitNpcId = 11178,
        submitRewardStr = {"R1201"},
        taskWalkingTips = "none;이렇게 심각한 일이라니!;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10202] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11179,
        autoDoNextTask = 10203,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE210400:14:9"},
        description = "모든것이 시작된 곳으로 돌아가 단서를 찾으세요.",
        goalDesc = {"팔문촌으로 돌아가기"},
        id = 10202,
        missiondone = {"NT10203"},
        name = "증거 찾기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11179", "E11179:10202"},
        submitNpcId = 11179,
        submitRewardStr = {"R1202"},
        taskWalkingTips = "나리께서 뭘 알고 계시는 거죠?;none;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10203] = {
        AcceptCallPlot = 0,
        ChapterFb = "4,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoDoNextTask = 10204,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "고양이 식당에서 야옹이에게 제지를 당했습니다.",
        goalDesc = {"야옹이와 다시 만나기"},
        id = 10203,
        missiondone = {"NT10204"},
        name = "죽마고우",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5034:10203"},
        submitNpcId = 5034,
        submitRewardStr = {"R1203"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10204] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,1",
        acceptConditionStr = {"AI:11601:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoDoNextTask = 10205,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"TI:11601:1:0"},
        description = "마음이 착한 야옹이가 뜨거운 찐빵을 대접합니다.",
        goalDesc = {"찐빵 먹기"},
        id = 10204,
        missiondone = {"NT10205"},
        name = "고양이 식당",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5034:10204"},
        submitNpcId = 5034,
        submitRewardStr = {"R1204"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10205] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5033,
        autoDoNextTask = 10206,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "고양이 식당의 사장님 등장!",
        goalDesc = {"고양이 사부"},
        id = 10205,
        missiondone = {"NT10206"},
        name = "고양이 애호가",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5033:10205", "NC11180", "E11180:10205"},
        submitNpcId = 5033,
        submitRewardStr = {"R1205"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10206] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11181,
        autoDoNextTask = 10207,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이철단을 따라 원대부를 만나세요.",
        goalDesc = {"철알 따라가기"},
        id = 10206,
        missiondone = {"NT10207"},
        name = "방문객",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11181", "E11181:10206", "E5038:10206", "TTRACE11181:210400:8:4"},
        submitNpcId = 5038,
        submitRewardStr = {"R1206"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10207] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5038,
        autoDoNextTask = 10208,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "원대부와 한 번 겨뤄보세요.",
        goalDesc = {"대장부"},
        id = 10207,
        missiondone = {"NT10208"},
        name = "강철의 사나이",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11182", "E5038:10207"},
        submitNpcId = 5038,
        submitRewardStr = {"R1207"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10208] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11183,
        autoDoNextTask = 10209,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "촌민에게 팔문촌과 월견도에 대해 물으세요.",
        goalDesc = {"팔문월견"},
        id = 10208,
        missiondone = {"NT10209"},
        name = "상호 연결",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11183", "E5038:10208", "E11183:10208"},
        submitNpcId = 11183,
        submitRewardStr = {"R1208"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10209] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5033,
        autoDoNextTask = 10210,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "고양이에게 푹 빠져 헤어나올 수  없는 사부님이 당신이게 이런 말로 형용할 수 없는 일을 요구하다니!",
        goalDesc = {"특이한 취미"},
        id = 10209,
        missiondone = {"NT10210"},
        name = "유치한 커플",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5033:10209"},
        submitNpcId = 5033,
        submitRewardStr = {"R1209"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10210] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,3",
        acceptConditionStr = {"AI:11601:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5033,
        autoDoNextTask = 10211,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "고양이 식당의 맛있는 찐빵을 백 할머니에게 가져다 드리세요.",
        goalDesc = {"백 할머니"},
        id = 10210,
        missiondone = {"NT10211"},
        name = "집으로 가기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"I11601:1", "E5033:10210", "E5035:10210"},
        submitNpcId = 5035,
        submitRewardStr = {"R1210"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10211] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5035,
        autoDoNextTask = 10212,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "할머니의 속마음을 일러주세요.",
        goalDesc = {"수수께끼 뒤에"},
        id = 10211,
        missiondone = {"NT10212"},
        name = "마음 내키지 않는",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5035:10211"},
        submitNpcId = 5035,
        submitRewardStr = {"R1211"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10212] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoDoNextTask = 10213,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE210400:17:9"},
        description = "할머니의 요구에 따라 국물을 만드는 재료를 찾다가 마주친 것은......",
        goalDesc = {"어둠의 적수"},
        id = 10212,
        missiondone = {"NT10213"},
        name = "잠복",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E11184:10212", "E5034:10212"},
        submitNpcId = 5034,
        submitRewardStr = {"R1212"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10213] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11185,
        autoDoNextTask = 10214,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "적은 뜻밖에도 자신을 미행하다 팔문촌에 이르렀습니다!",
        goalDesc = {"적수 등장"},
        id = 10213,
        missiondone = {"NT10214"},
        name = "사미",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11185", "E11185:10213"},
        submitNpcId = 11185,
        submitRewardStr = {"R1213"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10214] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11186,
        autoDoNextTask = 10215,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞에 있는 사람은 대체 누구야!",
        goalDesc = {"배후의 사람"},
        id = 10214,
        missiondone = {"NT10215"},
        name = "조장자",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11186", "E11186:10214"},
        submitNpcId = 11186,
        submitRewardStr = {"R1214"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10215] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11187,
        autoDoNextTask = 10216,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "어둠이 깃들자 마침내 억누르지 못하고 행동을 개시했습니다!",
        goalDesc = {"진실의 모퉁이"},
        id = 10215,
        missiondone = {"NT10216"},
        name = "환각",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11187", "NC11188", "NC11189", "E11187:10215", "E11189:10215"},
        submitNpcId = 11187,
        submitRewardStr = {"R1215"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10216] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11192,
        autoDoNextTask = 10217,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "마인드 능력자,이족,그들은 무엇을 말하고 있는 거지?",
        goalDesc = {"부도승"},
        id = 10216,
        missiondone = {"NT10217"},
        name = "친구 또는 적수",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11190", "NC11191", "NC11192", "NC11193", "E11191:10216", "E11192:10216", "E11193:10216"},
        submitNpcId = 11192,
        submitRewardStr = {"R1216"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10217] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11194,
        autoDoNextTask = 10218,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "묘옥수와 야옹이의 도움을 받아 백 할머니를 구하세요.",
        goalDesc = {"파트너의 중요성"},
        id = 10217,
        missiondone = {"NT10218"},
        name = "구조",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE11194:210400:19:10", "NC11194", "NC11195", "E11194:10217", "E11195:10217", "E5034:10217"},
        submitNpcId = 5034,
        submitRewardStr = {"R1217"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10218] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5033,
        autoDoNextTask = 10219,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "팔문촌은 예전에 요족의 거처였나요?",
        goalDesc = {"팔문후예"},
        id = 10218,
        missiondone = {"NT10219"},
        name = "요촌",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5033:10218"},
        submitNpcId = 5033,
        submitRewardStr = {"R1218"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10219] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5035,
        autoDoNextTask = 10220,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "부도의 당부.",
        goalDesc = {"사과"},
        id = 10219,
        missiondone = {"NT10220"},
        name = "컨트롤 실패",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11196", "E11196:10219", "E5035:10219"},
        submitNpcId = 11196,
        submitRewardStr = {"R1219"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10220] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5035,
        autoDoNextTask = 10221,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "네가 단독 행동을 했기에 할머니가 화가 났어.",
        goalDesc = {"화난 할머니"},
        id = 10220,
        missiondone = {"NT10221"},
        name = "정산",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5035:10220"},
        submitNpcId = 5035,
        submitRewardStr = {"R1220"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10221] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5035,
        autoDoNextTask = 10222,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "할머니의 입에서 월견도의 옛일을 알게 되었습니다.",
        goalDesc = {"할머니의 회상"},
        id = 10221,
        missiondone = {"NT10222"},
        name = "과거",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11197", "E5035:10221", "E5034:10221", "E11197:10221"},
        submitNpcId = 5035,
        submitRewardStr = {"R1221"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10222] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoDoNextTask = 10223,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE210400:16:9"},
        description = "누가 엿듣고 있습니다!",
        goalDesc = {"숨어 듣는 사람"},
        id = 10222,
        missiondone = {"NT10223"},
        name = "누구",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5034:10222"},
        submitNpcId = 5034,
        submitRewardStr = {"R1222"},
        taskWalkingTips = "none;방금 그건 요족인가요?바로 앞에!;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10223] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11198,
        autoDoNextTask = 10224,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "상고요족?견요에게 비밀을 캐물으세요.",
        goalDesc = {"추궁"},
        id = 10223,
        missiondone = {"NT10224"},
        name = "견요",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11198", "E11198:10223"},
        submitNpcId = 11198,
        submitRewardStr = {"R1223"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10224] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11199,
        autoDoNextTask = 10225,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "견요가 당신에게 새로운 단서에 대해 폭로했습니다.",
        goalDesc = {"새로운 단서"},
        id = 10224,
        missiondone = {"NT10225"},
        name = "성녀",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11199", "E11199:10224"},
        submitNpcId = 11199,
        submitRewardStr = {"R1224"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10225] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5034,
        autoDoNextTask = 10226,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "야옹이에게 신비한 사람을 추격한 결과를 알려주세요.",
        goalDesc = {"야옹이에게 답장"},
        id = 10225,
        missiondone = {"NT10226"},
        name = "주제 전환",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5034:10225"},
        submitNpcId = 5034,
        submitRewardStr = {"R1225"},
        taskWalkingTips = "성녀?;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10226] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5035,
        autoDoNextTask = 10227,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "불확실한 미래에 대해 할머니는 자신의 태도를 밝혔습니다.",
        goalDesc = {"주동적으로 출격"},
        id = 10226,
        missiondone = {"NT10227"},
        name = "태도",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5035:10226"},
        submitNpcId = 5035,
        submitRewardStr = {"R1226"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10227] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11200,
        autoDoNextTask = 10228,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "신부는 부도에게 추격한 결과를 알렸습니다.",
        goalDesc = {"부도의 일침"},
        id = 10227,
        missiondone = {"NT10228"},
        name = "사제의 행방",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11200", "E11200:10227"},
        submitNpcId = 11200,
        submitRewardStr = {"R1227"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10228] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11201,
        autoDoNextTask = 10229,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "부도의 입에서 제국의 잘 알려지지 않는 면을 알 수 있습니다.",
        goalDesc = {"정신 능력자"},
        id = 10228,
        missiondone = {"NT10229"},
        name = "신부의 행방",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11201", "E11201:10228"},
        submitNpcId = 11201,
        submitRewardStr = {"R1228"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10229] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5035,
        autoDoNextTask = 10230,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백 할머니에게 당신의 결정을 알려주세요.",
        goalDesc = {"월견도 조사"},
        id = 10229,
        missiondone = {"NT10230"},
        name = "결정하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5035:10229"},
        submitNpcId = 5035,
        submitRewardStr = {"R1229"},
        taskWalkingTips = "부도 선배님은 과연 대단하시네요!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10230] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5033,
        autoDoNextTask = 10231,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:20:11"},
        description = "7년 후,드디어 월견도로 돌아왔습니다!",
        goalDesc = {"월견도로 이동"},
        id = 10230,
        missiondone = {"NT10231"},
        name = "잠시 작별",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5033:10230"},
        submitNpcId = 5033,
        submitRewardStr = {"R1230"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10231] = {
        AcceptCallPlot = 0,
        ChapterFb = "5,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5031,
        autoDoNextTask = 10232,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "월견도에 어떻게 사람이 있는 거지?가서 보자.",
        goalDesc = {"인간이 아닌"},
        id = 10231,
        missiondone = {"NT10232"},
        name = "모험",
        playid = 10007,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5031:10231"},
        submitNpcId = 5031,
        submitRewardStr = {"R1231"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10232] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5031,
        autoDoNextTask = 10233,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "월견도에 다른 사람도 왔나요?",
        goalDesc = {"만나다"},
        id = 10232,
        missiondone = {"NT10233"},
        name = "귀신?",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5031:10232", "E11202:10232", "E11203:10232"},
        submitNpcId = 5031,
        submitRewardStr = {"R1232"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10233] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11205,
        autoDoNextTask = 10234,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "세상에 인족과 요족을 제외하면,귀족도 있나요?",
        goalDesc = {"진면모"},
        id = 10233,
        missiondone = {"NT10234"},
        name = "새 종족",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11204", "NC11205", "E11205:10233"},
        submitNpcId = 11205,
        submitRewardStr = {"R1233"},
        taskWalkingTips = "그건!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10234] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11206,
        autoDoNextTask = 10235,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백과 대화를 하던 중 월견도의 사건 당일 밤 상황에 파악했습니다.",
        goalDesc = {"흑백의 의도"},
        id = 10234,
        missiondone = {"NT10235"},
        name = "참과 거짓 집행관",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11206", "NC11207", "E11206:10234", "E11207:10234"},
        submitNpcId = 11206,
        submitRewardStr = {"R1234"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10235] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11208,
        autoDoNextTask = 10236,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "귀신의 세계?백과 대화를 하면 더 많은 정보를 얻을 수 있습니다.",
        goalDesc = {"백의 설명"},
        id = 10235,
        missiondone = {"NT10236"},
        name = "안내인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11208", "NC11209", "E11208:10235"},
        submitNpcId = 11208,
        submitRewardStr = {"R1235"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10236] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5031,
        autoDoNextTask = 10237,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "귀유자와 대화를 해 단서를 획득해보세요.",
        goalDesc = {"귀유자에게 추궁"},
        id = 10236,
        missiondone = {"NT10237"},
        name = "다시 시도하십시오",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5031:10236"},
        submitNpcId = 5031,
        submitRewardStr = {"R1236"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10237] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11210,
        autoDoNextTask = 10238,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "모두 세상의 잘못이야!흑과 대치하세요.",
        goalDesc = {"누구의 잘못"},
        id = 10237,
        missiondone = {"NT10238"},
        name = "원한",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11210", "E11210:10237"},
        submitNpcId = 11210,
        submitRewardStr = {"R1237"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10238] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11211,
        autoDoNextTask = 10239,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:14.5:15.3"},
        description = "적이야 우정이냐?백과 대화를 나누세요.",
        goalDesc = {"의심"},
        id = 10238,
        missiondone = {"NT10239"},
        name = "운명의 배열",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11211", "NC11212", "E11211:10238", "E11212:10238"},
        submitNpcId = 11211,
        submitRewardStr = {"R1238"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10239] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11214,
        autoDoNextTask = 10240,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "새로운 요족이 출현했습니다!만주사화와 대화를 나누세요.",
        goalDesc = {"만주사화"},
        id = 10239,
        missiondone = {"NT10240"},
        name = "월견금지",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11214", "E11214:10239"},
        submitNpcId = 11214,
        submitRewardStr = {"R1239"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10240] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11216,
        autoDoNextTask = 10241,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "사화의 정서가 매우 불안정합니다!",
        goalDesc = {"토끼"},
        id = 10240,
        missiondone = {"NT10241"},
        name = "새로운 캐릭터",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11215", "NC11216", "E11215:10240", "E11216:10240"},
        submitNpcId = 11216,
        submitRewardStr = {"R1240"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10241] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11217,
        autoDoNextTask = 10242,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족이 왜 월견도에 있는거죠?",
        goalDesc = {"흑백 도착"},
        id = 10241,
        missiondone = {"NT10242"},
        name = "기억 상실",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11217", "NC11218", "NC11219", "NC11220", "E11217:10241", "E11218:10241", "E11219:10241", "E11220:10241"},
        submitNpcId = 11217,
        submitRewardStr = {"R1241"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10242] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11221,
        autoDoNextTask = 10243,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백이 사화를 데려가는 것을 제지하세요.",
        goalDesc = {"사화 따라가기"},
        id = 10242,
        missiondone = {"NT10243"},
        name = "귀족",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11221", "NC11222", "E11221:10242", "E11222:10242", "TTRACE11221:204000:16:13"},
        submitNpcId = 11222,
        submitRewardStr = {"R1242"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10243] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11223,
        autoDoNextTask = 10244,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "사화와 대화 중,흑의 제지를 받았습니다.",
        goalDesc = {"흑과 대화"},
        id = 10243,
        missiondone = {"NT10244"},
        name = "갈등",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11223", "NC11224", "NC11225", "E11223:10243", "E11224:10243"},
        submitNpcId = 11223,
        submitRewardStr = {"R1243"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10244] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11226,
        autoDoNextTask = 10245,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "흑과 백의 목적이 이거였구나.",
        goalDesc = {"백의 제안"},
        id = 10244,
        missiondone = {"NT10245"},
        name = "동맹",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11226", "E11226:10244"},
        submitNpcId = 11226,
        submitRewardStr = {"R1244"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10245] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11227,
        autoDoNextTask = 10246,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "누가 섬에 온것이지?백과 함께 상황을 알아보세요.",
        goalDesc = {"적수 상륙"},
        id = 10245,
        missiondone = {"NT10246"},
        name = "침입하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE11227:204000:20.6:7.8", "NC11227", "NC11228", "E11227:10245"},
        submitNpcId = 11228,
        submitRewardStr = {"R1245"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10246] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11229,
        autoDoNextTask = 10247,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "곰패가 월견도에 나타나다니!",
        goalDesc = {"곰패 재등장"},
        id = 10246,
        missiondone = {"NT10247"},
        name = "조사 대상",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11229", "E11229:10246"},
        submitNpcId = 11229,
        submitRewardStr = {"R1246"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10247] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11230,
        autoDoNextTask = 10248,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:15:7"},
        description = "백과 헤어지고,어둠속에 숨어 있는 적을 찾아보세요.",
        goalDesc = {"곤경에 빠지다"},
        id = 10247,
        missiondone = {"NT10248"},
        name = "어둠의 사람",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11230", "E11230:10247"},
        submitNpcId = 11230,
        submitRewardStr = {"R1247"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10248] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11231,
        autoDoNextTask = 10249,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "개조인이 나온 후 타도가 모습을 드러냈습니다.",
        goalDesc = {"개조인 병단"},
        id = 10248,
        missiondone = {"NT10249"},
        name = "포위",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11231", "NC11232", "NC11233", "NC11234", "NC11235", "E11231:10248", "E11236:10248"},
        submitNpcId = 11231,
        submitRewardStr = {"R1248"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10249] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11237,
        autoDoNextTask = 10250,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "타도는 성당 사람인가요?",
        goalDesc = {"타도와 대화"},
        id = 10249,
        missiondone = {"NT10250"},
        name = "성당 음모",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11237", "E11237:10249"},
        submitNpcId = 11237,
        submitRewardStr = {"R1249"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10250] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11238,
        autoDoNextTask = 10251,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "타도를 체포하는 과정에 사고가 났는데,이건?",
        goalDesc = {"타도 잡기"},
        id = 10250,
        missiondone = {"NT10251"},
        name = "체포하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11238", "NC11239", "E11238:10250", "E11239:10250"},
        submitNpcId = 11238,
        submitRewardStr = {"R1250"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10251] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11241,
        autoDoNextTask = 10252,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "고양이가 길을 막아 나쁜 사람이 도망친다!이 고양이는 어떻게 된 거지!",
        goalDesc = {"고양이"},
        id = 10251,
        missiondone = {"NT10252"},
        name = "사고",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11240", "NC11241", "E11240:10251", "E11241:10251", "E11242:10251"},
        submitNpcId = 11241,
        submitRewardStr = {"R1251"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10252] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11243,
        autoDoNextTask = 10253,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족의 저지로 타도는 도망갔습니다,녹리와 대화를 나누세요.",
        goalDesc = {"녹리와 대화"},
        id = 10252,
        missiondone = {"NT10253"},
        name = "신사 거부",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11243", "E11243:10252"},
        submitNpcId = 11243,
        submitRewardStr = {"R1252"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10253] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11244,
        autoDoNextTask = 10254,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:20:11.5"},
        description = "그를 이렇게 보낼 수 없어요!월견도의 요족을 찾으세요.",
        goalDesc = {"요족 찾기"},
        id = 10253,
        missiondone = {"NT10254"},
        name = "헛수고",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11244", "NC11245", "E11244:10253", "E11245:10253"},
        submitNpcId = 11244,
        submitRewardStr = {"R1253"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10254] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11246,
        autoDoNextTask = 10255,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "작은 계략을 써서 월견도의 요족을 찾으세요.",
        goalDesc = {"고양이 따라가기"},
        id = 10254,
        missiondone = {"NT10255"},
        name = "위협하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11246", "E11246:10254", "E5029:10254", "TTRACE11246:204000:6.6:10.5"},
        submitNpcId = 5029,
        submitRewardStr = {"R1254"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10255] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5030,
        autoDoNextTask = 10256,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "녹리의 단서를 따라 영월을 찾으세요.",
        goalDesc = {"요족의 생각"},
        id = 10255,
        missiondone = {"NT10256"},
        name = "놀리다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5030:10255"},
        submitNpcId = 5030,
        submitRewardStr = {"R1255"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10256] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5030,
        autoDoNextTask = 10257,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "토끼의 놀이에 참여하세요.",
        goalDesc = {"영월과 대화"},
        id = 10256,
        missiondone = {"NT10257"},
        name = "사과",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11247", "E5030:10256", "E11247:10256"},
        submitNpcId = 11247,
        submitRewardStr = {"R1256"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10257] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5030,
        autoDoNextTask = 10258,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "인족이 요령계와 평화약조를 파기하다니,왜?",
        goalDesc = {"두 종족의 전쟁"},
        id = 10257,
        missiondone = {"NT10258"},
        name = "천년의 계약",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11248", "E5030:10257"},
        submitNpcId = 5030,
        submitRewardStr = {"R1257"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10258] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5030,
        autoDoNextTask = 10259,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족도 귀족의 존재를 알고 있었단 말입니까?",
        goalDesc = {"대화 거부"},
        id = 10258,
        missiondone = {"NT10259"},
        name = "떠나다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5030:10258", "E5029:10258"},
        submitNpcId = 5029,
        submitRewardStr = {"R1258"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10259] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11249,
        autoDoNextTask = 10260,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백은 곰패를 쫓지못하고 돌아오던 중 광소를 만났는데?",
        goalDesc = {"백과 대화하기"},
        id = 10259,
        missiondone = {"NT10260"},
        name = "추격 후",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11249", "E11249:10259"},
        submitNpcId = 11249,
        submitRewardStr = {"R1259"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10260] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11251,
        autoDoNextTask = 10261,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "흑은 명계에서 돌아왔습니다,흑과 대화를 나누세요.",
        goalDesc = {"흑 복귀"},
        id = 10260,
        missiondone = {"NT10261"},
        name = "합의에 이르다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11250", "NC11251", "E11250:10260", "E11251:10260"},
        submitNpcId = 11251,
        submitRewardStr = {"R1260"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10261] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11252,
        autoDoNextTask = 10262,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "복직,중급팀 총집결!",
        goalDesc = {"통수부 백"},
        id = 10261,
        missiondone = {"NT10262"},
        name = "직무 회복",
        playid = 10008,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11252", "NC11253", "NC11254", "E11252:10261", "E11253:10261"},
        submitNpcId = 11252,
        submitRewardStr = {"R1261"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10262] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11255,
        autoDoNextTask = 10263,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이번 작전의 직접적인 지휘관 흑열은 3인조에게 명령을 내릴 것입니다!",
        goalDesc = {"흑열 계획"},
        id = 10262,
        missiondone = {"NT10263"},
        name = "고급 부서",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11255", "E11255:10262"},
        submitNpcId = 11255,
        submitRewardStr = {"R1262"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10263] = {
        AcceptCallPlot = 0,
        ChapterFb = "6,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11257,
        autoDoNextTask = 10264,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE202000:5:5"},
        description = "출동,3인조가 풍죽림의 조사를 시작했습니다.",
        goalDesc = {"풍죽림으로 이동"},
        id = 10263,
        missiondone = {"NT10264"},
        name = "한 발짝 앞으로",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11256", "NC11257", "NC11258", "E11256:10263", "E11257:10263", "E11258:10263"},
        submitNpcId = 11257,
        submitRewardStr = {"R1263"},
        taskWalkingTips = "드디어 풍죽림에 대한 조사를 시작합니다!;none;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10264] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5026,
        autoDoNextTask = 10265,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족 사이에 숨은 아이돌?염익과 대화를 나누세요.",
        goalDesc = {"비익새"},
        id = 10264,
        missiondone = {"NT10265"},
        name = "아이돌 그룹",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5026:10264"},
        submitNpcId = 5026,
        submitRewardStr = {"R1264"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10265] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11259,
        autoDoNextTask = 10266,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족의 경계심이 너무 강해서 겉보기에 온화하지만 무해한 중화를 교류의 선구자로 삼아야 합니다.",
        goalDesc = {"중화 출동"},
        id = 10265,
        missiondone = {"NT10266"},
        name = "조사가 시작되다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11259", "NC11260", "E11259:10265", "E11260:10265"},
        submitNpcId = 11259,
        submitRewardStr = {"R1265"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10266] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5026,
        autoDoNextTask = 10267,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화가 조사를 진행할 때 비익조의 또 다른 멤버가 나타났습니다.",
        goalDesc = {"비익과 대화"},
        id = 10266,
        missiondone = {"NT10267"},
        name = "조사가 진행되는 동안",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5026:10266"},
        submitNpcId = 5026,
        submitRewardStr = {"R1266"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10267] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5027,
        autoDoNextTask = 10268,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "청익의 출현은 중화를 더욱 흥분시켰습니다.",
        goalDesc = {"다른 파트너"},
        id = 10267,
        missiondone = {"NT10268"},
        name = "사랑과 정의",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5027:10267"},
        submitNpcId = 5027,
        submitRewardStr = {"R1267"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10268] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11261,
        autoDoNextTask = 10269,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "죽림에 요족이 있으니,계속 조사해주세요.",
        goalDesc = {"판다 할아버지"},
        id = 10268,
        missiondone = {"NT10269"},
        name = "조사 계속속",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11261", "E11261:10268"},
        submitNpcId = 11261,
        submitRewardStr = {"R1268"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10269] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5024,
        autoDoNextTask = 10270,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "다른 요족과 비슷한 대답으로 모두를 곤욕을 치렀던 집양이 나타났습니다!",
        goalDesc = {"이상한 아이"},
        id = 10269,
        missiondone = {"NT10270"},
        name = "곤경에 빠지다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5024:10269"},
        submitNpcId = 5024,
        submitRewardStr = {"R1269"},
        taskWalkingTips = "저기 할아버지께 여쭤보자.;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10270] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5025,
        autoDoNextTask = 10271,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "버려진 아이 집양을 만나 상황을 파악하고 있습니다.",
        goalDesc = {"집양과 대화"},
        id = 10270,
        missiondone = {"NT10271"},
        name = "버려진 아들",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5025:10270"},
        submitNpcId = 5025,
        submitRewardStr = {"R1270"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10271] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5024,
        autoDoNextTask = 10272,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집양은 이미 요족에 녹아들었고 일반적인 방식으로 유리한 단서를 얻을 수 없습니다.",
        goalDesc = {"할아버지의 경고"},
        id = 10271,
        missiondone = {"NT10272"},
        name = "호단",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5024:10271"},
        submitNpcId = 5024,
        submitRewardStr = {"R1271"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10272] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11262,
        autoDoNextTask = 10273,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집양에 대치하기 위해 3인조를 보내 중화와 영전합니다.",
        goalDesc = {"중화의 공헌"},
        id = 10272,
        missiondone = {"NT10273"},
        name = "최고의 파트너",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11262", "E11262:10272"},
        submitNpcId = 11262,
        submitRewardStr = {"R1272"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10273] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11263,
        autoDoNextTask = 10274,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집양한테 유용한 정보를 획득합니다.",
        goalDesc = {"단서 획득"},
        id = 10273,
        missiondone = {"NT10274"},
        name = "헛소리",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11263", "E11263:10273"},
        submitNpcId = 11263,
        submitRewardStr = {"R1273"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10274] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11264,
        autoDoNextTask = 10275,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "강아지풀로 집양을 간지럽혀 달래기.",
        goalDesc = {"집양 간지럽히기"},
        id = 10274,
        missiondone = {"NT10275"},
        name = "고문",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11264", "NC11265", "E11264:10274", "E11265:10274"},
        submitNpcId = 11264,
        submitRewardStr = {"R1274"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10275] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11266,
        autoDoNextTask = 10276,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "풍죽림의 요족들이 이런 중요한 일을 숨기고 집양과 대화를 해 자백을 받아냈습니다.",
        goalDesc = {"집양의 자백"},
        id = 10275,
        missiondone = {"NT10276"},
        name = "요족 실종",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11266", "E11266:10275"},
        submitNpcId = 11266,
        submitRewardStr = {"R1275"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10276] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11267,
        autoDoNextTask = 10277,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "집양의 부름을 듣고 집이가 달려왔습니다.",
        goalDesc = {"집이"},
        id = 10276,
        missiondone = {"NT10277"},
        name = "새로운 단서",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11267", "NC11268", "E11267:10276", "E11268:10276"},
        submitNpcId = 11267,
        submitRewardStr = {"R1276"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10277] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11269,
        autoDoNextTask = 10278,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "다툼 끝에 집이는 당신에게 힌트를 주었습니다.",
        goalDesc = {"풍죽림 관리"},
        id = 10277,
        missiondone = {"NT10278"},
        name = "힌트",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11269", "E11269:10277"},
        submitNpcId = 11269,
        submitRewardStr = {"R1277"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10278] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,5",
        acceptConditionStr = {"AI:11610:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11270,
        autoDoNextTask = 10279,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"TI:11610:1:0"},
        description = "자료를 뒤적였습니다.",
        goalDesc = {"여족 도하"},
        id = 10278,
        missiondone = {"NT10279"},
        name = "자료 찾기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11270", "E11270:10278"},
        submitNpcId = 11270,
        submitRewardStr = {"R1278"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10279] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11271,
        autoDoNextTask = 10280,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "죽림의 깊은 곳에서 피리소리가 들려옵니다,누군가가 있는 것 같습니다.",
        goalDesc = {"중화의 발견"},
        id = 10279,
        missiondone = {"NT10280"},
        name = "나팔소리",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11271", "E11271:10279"},
        submitNpcId = 11271,
        submitRewardStr = {"R1279"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10280] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11272,
        autoDoNextTask = 10281,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "죽림은 앞에서 당신을 기다립니다,당신에게 할 말이 있어 보입니다.",
        goalDesc = {"청죽"},
        id = 10280,
        missiondone = {"NT10281"},
        name = "기록",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11272", "E11272:10280"},
        submitNpcId = 11272,
        submitRewardStr = {"R1280"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10281] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11273,
        autoDoNextTask = 10282,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도하를 계속해서 찾으세요.",
        goalDesc = {"계속 전진"},
        id = 10281,
        missiondone = {"NT10282"},
        name = "청죽을 밀치다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11273", "NC11274", "E11273:10281", "E11274:10281"},
        submitNpcId = 11274,
        submitRewardStr = {"R1281"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10282] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11275,
        autoDoNextTask = 10283,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도하를 계속해서 찾으세요.",
        goalDesc = {"호비와 재회"},
        id = 10282,
        missiondone = {"NT10283"},
        name = "요족금지",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11275", "E11275:10282"},
        submitNpcId = 11275,
        submitRewardStr = {"R1282"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10283] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11276,
        autoDoNextTask = 10284,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "호비의 안내로 도하를 만났습니다.",
        goalDesc = {"호비 따라가기"},
        id = 10283,
        missiondone = {"NT10284"},
        name = "길을 이끌다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11276", "E11276:10283", "E5028:10283", "TTRACE11276:202000:2.5:11"},
        submitNpcId = 5028,
        submitRewardStr = {"R1283"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10284] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5028,
        autoDoNextTask = 10285,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "도하의 승인을 획득했습니다.",
        goalDesc = {"도하와 결투"},
        id = 10284,
        missiondone = {"NT10285"},
        name = "자격 결정",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5028:10284"},
        submitNpcId = 5028,
        submitRewardStr = {"R1284"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10285] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,7",
        acceptConditionStr = {"AI:11610:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5028,
        autoDoNextTask = 10286,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"TI:11610:1:0"},
        description = "도하가 당신에게 요족의 정보가 담긴 자료를 건넸습니다.",
        goalDesc = {"자료 확인"},
        id = 10285,
        missiondone = {"NT10286"},
        name = "요족 등록",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5028:10285"},
        submitNpcId = 5028,
        submitRewardStr = {"R1285"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10286] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11277,
        autoDoNextTask = 10287,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "호비의 안내로 도하를 만났습니다.",
        goalDesc = {"호비 따라가기"},
        id = 10286,
        missiondone = {"NT10287"},
        name = "요왕 만남",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11277", "NC11291", "E11277:10286", "E11291:10286", "TTRACE11277:202000:5.4:19"},
        submitNpcId = 11291,
        submitRewardStr = {"R1286"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10287] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11278,
        autoDoNextTask = 10288,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "7년 후,다시 요왕을 만났습니다.",
        goalDesc = {"까마귀"},
        id = 10287,
        missiondone = {"NT10288"},
        name = "요족의 마스터",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11278", "E11278:10287"},
        submitNpcId = 11278,
        submitRewardStr = {"R1287"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10288] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11279,
        autoDoNextTask = 10289,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요왕의 힌트를 획득했는데 성녀가 성당에 숨어있단 말인가?",
        goalDesc = {"까마귀 힌트"},
        id = 10288,
        missiondone = {"NT10289"},
        name = "성녀의 행방",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11279", "E11279:10288"},
        submitNpcId = 11279,
        submitRewardStr = {"R1288"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10289] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11280,
        autoDoNextTask = 10290,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "자료를 가지고 단과 합류했습니다.",
        goalDesc = {"단을 찾기"},
        id = 10289,
        missiondone = {"NT10290"},
        name = "보상",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11280", "NC11281", "NC11282", "E11280:10289", "E11281:10289", "TTRACE11280:202000:8.4:5.4"},
        submitNpcId = 11281,
        submitRewardStr = {"R1289"},
        taskWalkingTips = "none;요족들이 성녀를 언급했다!;none",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10290] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11284,
        autoDoNextTask = 10291,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단과 중화가 합류한 후 함께 통수부로 돌아왔습니다.",
        goalDesc = {"흑열에게 답장"},
        id = 10290,
        missiondone = {"NT10291"},
        name = "통수부로 복귀",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11283", "NC11284", "NC11285", "E11284:10290", "E11285:10290"},
        submitNpcId = 11285,
        submitRewardStr = {"R1290"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10291] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11286,
        autoDoNextTask = 10292,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족의 인족 습격 사건의 진짜 원인이 회생석 실험을 위해서인가?",
        goalDesc = {"흑의 경고"},
        id = 10291,
        missiondone = {"NT10292"},
        name = "회생석 실험",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11286", "E11286:10291"},
        submitNpcId = 11286,
        submitRewardStr = {"R1291"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10292] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11287,
        autoDoNextTask = 10293,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "지금까지 얻은 단서에 따라 다음 대책을 백과 상의합니다.",
        goalDesc = {"백과 대화하기"},
        id = 10292,
        missiondone = {"NT10293"},
        name = "논의",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11287", "NC11288", "E11287:10292", "TTRACE11287:101000:7.2:24.8"},
        submitNpcId = 11288,
        submitRewardStr = {"R1292"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10293] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"AI:11603:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11289,
        autoDoNextTask = 10294,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"TI:11603:1:0"},
        description = "통수부의 성당의 옛 문서를 살펴보세요.",
        goalDesc = {"성당 본부"},
        id = 10293,
        missiondone = {"NT10294"},
        name = "유명진",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11289", "E11289:10293"},
        submitNpcId = 11289,
        submitRewardStr = {"R1293"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10294] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11290,
        autoDoNextTask = 10295,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "다음 단계,유명진을 탐색하세요!",
        goalDesc = {"대책"},
        id = 10294,
        missiondone = {"NT10295"},
        name = "신의 형벌의 의미",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11290", "E11290:10294"},
        submitNpcId = 11290,
        submitRewardStr = {"R1294"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10295] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11292,
        autoDoNextTask = 10296,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이번 작전에 대해 단은 할 말이 있는 것 같습니다.",
        goalDesc = {"단의 의심"},
        id = 10295,
        missiondone = {"NT10296"},
        name = "누설자",
        playid = 10009,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11292", "NC11293", "E11292:10295", "E11293:10295"},
        submitNpcId = 11292,
        submitRewardStr = {"R1295"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10296] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11294,
        autoDoNextTask = 10297,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화가 망치를 들고 술집 앞에서 당신을 기다립니다.",
        goalDesc = {"중화와 대화"},
        id = 10296,
        missiondone = {"NT10297"},
        name = "속삭임",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11294", "E11294:10296"},
        submitNpcId = 11294,
        submitRewardStr = {"R1296"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10297] = {
        AcceptCallPlot = 0,
        ChapterFb = "7,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11295,
        autoDoNextTask = 10298,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:12:13"},
        description = "백과 함께 유명진을 향합니다.",
        goalDesc = {"유명진 출발"},
        id = 10297,
        missiondone = {"NT10298"},
        name = "조사 전개",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11295", "NC11296", "E11295:10297", "E11296:10297", "E11297:10297"},
        submitNpcId = 11295,
        submitRewardStr = {"R1297"},
        taskWalkingTips = "중화의 직감이 정확하다고 할 수밖에 없네!;none;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10298] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11298,
        autoDoNextTask = 10299,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "처음 왔을때,유명진에서 새로운 요족 멤버인 송구자를 만났습니다.",
        goalDesc = {"송구자"},
        id = 10298,
        missiondone = {"NT10299"},
        name = "적의",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11298", "E11298:10298"},
        submitNpcId = 11298,
        submitRewardStr = {"R1298"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10299] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11299,
        autoDoNextTask = 10300,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족의 가면을 쓴 후 백과 대화를 나눕니다.",
        goalDesc = {"백의 긍정"},
        id = 10299,
        missiondone = {"NT10300"},
        name = "은폐하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11299", "E11299:10299"},
        submitNpcId = 11299,
        submitRewardStr = {"R1299"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10300] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 10691,
        autoDoNextTask = 10301,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백과 함께 페허된 성당를 조사하러 왔습니다.",
        goalDesc = {"페허의 중"},
        id = 10300,
        missiondone = {"NT10301"},
        name = "성당 본부",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC10691", "E10691:10300", "E10692:10300"},
        submitNpcId = 10691,
        submitRewardStr = {"R1300"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10301] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11302,
        autoDoNextTask = 10302,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백과 함께 북명호 옆에서 깨어난 설연화요를 발견했습니다.",
        goalDesc = {"졸린 연"},
        id = 10301,
        missiondone = {"NT10302"},
        name = "설연화요",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE11302:205000:11:18", "NC11302", "NC11303", "E11302:10301", "E11303:10301"},
        submitNpcId = 11303,
        submitRewardStr = {"R1301"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10302] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11304,
        autoDoNextTask = 10303,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "연에게 폐허의 과거에 대해 물었습니다.",
        goalDesc = {"연의 기억"},
        id = 10302,
        missiondone = {"NT10303"},
        name = "취증",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11304", "E11304:10302"},
        submitNpcId = 11304,
        submitRewardStr = {"R1302"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10303] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11305,
        autoDoNextTask = 10304,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:6:8"},
        description = "백과 함께 북명호 옆에서 깨어난 설연화요를 발견했습니다.",
        goalDesc = {"증인"},
        id = 10303,
        missiondone = {"NT10304"},
        name = "과거 전쟁",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11305", "E11305:10303"},
        submitNpcId = 11305,
        submitRewardStr = {"R1303"},
        taskWalkingTips = "none;드디어 실마리를 찾았습니다!;none",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10304] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11306,
        autoDoNextTask = 10305,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "혼석이 갑자기 나타나,안 좋은 소식을 전했습니다.",
        goalDesc = {"혼석"},
        id = 10304,
        missiondone = {"NT10305"},
        name = "사고",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11306", "E11306:10304"},
        submitNpcId = 11306,
        submitRewardStr = {"R1304"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10305] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11307,
        autoDoNextTask = 10306,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "송구자는 혼석을 물리치고 백을 구하자고 제안했습니다.",
        goalDesc = {"사람 뺏기"},
        id = 10305,
        missiondone = {"NT10306"},
        name = "제안",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11307", "NC11308", "NC11309", "E11307:10305", "E11309:10305"},
        submitNpcId = 11309,
        submitRewardStr = {"R1305"},
        taskWalkingTips = "괜히 죄 없는 사람을 데려가면 안됩니다!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10306] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11310,
        autoDoNextTask = 10307,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백은 명계로 돌아가기전 당신에게 당부할 말이 있습니다.",
        goalDesc = {"백의 충고"},
        id = 10306,
        missiondone = {"NT10307"},
        name = "구조 실패",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11310", "NC11311", "E11310:10306", "E11311:10306"},
        submitNpcId = 11310,
        submitRewardStr = {"R1306"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10307] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11312,
        autoDoNextTask = 10308,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "요족이 귀족을 미워하는 이유가 바로 이 때문인가요?송구자와 대화를 통해 자초지종을 알아보세요.",
        goalDesc = {"옳음과 그름"},
        id = 10307,
        missiondone = {"NT10308"},
        name = "증오의 원인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11312", "E11312:10307"},
        submitNpcId = 11312,
        submitRewardStr = {"R1307"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10308] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5061,
        autoDoNextTask = 10309,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "북명 성녀의 명을 받아 유명진에서 아들이 돌아오기를 기다리고 있습니다!",
        goalDesc = {"북명의 기다림"},
        id = 10308,
        missiondone = {"NT10309"},
        name = "성녀 등장",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5061:10308", "E11313:10308"},
        submitNpcId = 5061,
        submitRewardStr = {"R1308"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10309] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11314,
        autoDoNextTask = 10310,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈 앞에 있는 사람이 바로 애타게 찾고 있던 성녀 백수입니다!",
        goalDesc = {"백수"},
        id = 10309,
        missiondone = {"NT10310"},
        name = "운명적인 만남",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11314", "E11314:10309", "E11346:10309"},
        submitNpcId = 11314,
        submitRewardStr = {"R1309"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10310] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11316,
        autoDoNextTask = 10311,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "대단하네요!정신의 지배를 받는 백수와 광소의 등장!",
        goalDesc = {"광소의 등장"},
        id = 10310,
        missiondone = {"NT10311"},
        name = "정신적 지배",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11315", "NC11316", "E11315:10310", "E11316:10310", "E11347:10310"},
        submitNpcId = 11316,
        submitRewardStr = {"R1310"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10311] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11317,
        autoDoNextTask = 10312,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "광소무적,백소의 초강력 정신력 역지배!",
        goalDesc = {"역컨트롤"},
        id = 10311,
        missiondone = {"NT10312"},
        name = "대항",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11317", "E11317:10311", "E11348:10311"},
        submitNpcId = 11317,
        submitRewardStr = {"R1311"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10312] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5061,
        autoDoNextTask = 10313,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "능력 부족의 백수의 그림자가 사라졌습니다,그녀는 어디에 있는 거죠?",
        goalDesc = {"백수는 어디에"},
        id = 10312,
        missiondone = {"NT10313"},
        name = "연락두절",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11318", "E11318:10312", "E5061:10312"},
        submitNpcId = 5061,
        submitRewardStr = {"R1312"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10313] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5061,
        autoDoNextTask = 10314,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단혼애 채석장에서 만난 호연은 유명진에 나타났는데,과연 결혼에 문제가 생긴 것 같습니다!",
        goalDesc = {"연을 재회"},
        id = 10313,
        missiondone = {"NT10314"},
        name = "일련의 사건",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11319", "E11319:10313", "E5061:10313"},
        submitNpcId = 5061,
        submitRewardStr = {"R1313"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10314] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11320,
        autoDoNextTask = 10315,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "연의 출현은 바로 속임수의 가장 좋은 시기입니다!",
        goalDesc = {"연의 헛소리"},
        id = 10314,
        missiondone = {"NT10315"},
        name = "강해지는 방법",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11320", "E11320:10314"},
        submitNpcId = 11320,
        submitRewardStr = {"R1314"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10315] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11321,
        autoDoNextTask = 10316,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "하마터면 연에게 걸려 북명에게 넘길뻔 했다.",
        goalDesc = {"죄행 인정"},
        id = 10315,
        missiondone = {"NT10316"},
        name = "처리 방법",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11321", "NC11322", "E11321:10315", "E11322:10315"},
        submitNpcId = 11322,
        submitRewardStr = {"R1315"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10316] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5061,
        autoDoNextTask = 10317,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "북명에게 다음 계획을 알리세요.",
        goalDesc = {"성당 잠입"},
        id = 10316,
        missiondone = {"NT10317"},
        name = "의도하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5061:10316"},
        submitNpcId = 5061,
        submitRewardStr = {"R1316"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10317] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11323,
        autoDoNextTask = 10318,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "유명진 일행은 뜻밖에도 많은 지인들을 만났는데 그들은 도대체 무슨 일을 계획하고 있는걸까요?",
        goalDesc = {"반위와 단"},
        id = 10317,
        missiondone = {"NT10318"},
        name = "예기치 않은",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11323", "NC11324", "E11323:10317", "E5061:10317", "E11324:10317"},
        submitNpcId = 5061,
        submitRewardStr = {"R1317"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10318] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11325,
        autoDoNextTask = 10319,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "반위와 대화를 하던 중 진화의 이론에 대해 알게 되었습니다.",
        goalDesc = {"강해지는 힘"},
        id = 10318,
        missiondone = {"NT10319"},
        name = "매혹",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11325", "E11325:10318"},
        submitNpcId = 11325,
        submitRewardStr = {"R1318"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10319] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5061,
        autoDoNextTask = 10320,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "혼자 인대본영을 개조하던 중,북명은 자신의 견해를 밝혔습니다.",
        goalDesc = {"북명의 권고"},
        id = 10319,
        missiondone = {"NT10320"},
        name = "수사 포기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5061:10319"},
        submitNpcId = 5061,
        submitRewardStr = {"R1319"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10320] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11326,
        autoDoNextTask = 10321,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "반위와 이야기를 나누고 있을 때 걸수가 나타났습니다.",
        goalDesc = {"반위와 대화"},
        id = 10320,
        missiondone = {"NT10321"},
        name = "신부 등장",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11326", "NC11327", "E11326:10320", "E11327:10320"},
        submitNpcId = 11326,
        submitRewardStr = {"R1320"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10321] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11330,
        autoDoNextTask = 10322,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "위기의 상황 걸수는 뭔가를 알아차린 것 같습니다.",
        goalDesc = {"걸수의 초대"},
        id = 10321,
        missiondone = {"NT10322"},
        name = "위기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11328", "NC11329", "NC11330", "E11328:10321", "E11329:10321", "E11330:10321"},
        submitNpcId = 11330,
        submitRewardStr = {"R1321"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10322] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11331,
        autoDoNextTask = 10323,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "걸수에게 인족의 신분을 들켜 북명의 도움으로 철수했습니다.",
        goalDesc = {"북명의 도움"},
        id = 10322,
        missiondone = {"NT10323"},
        name = "철수",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11331", "E11331:10322", "E5061:10322"},
        submitNpcId = 11331,
        submitRewardStr = {"R1322"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10323] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11332,
        autoDoNextTask = 10324,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE205000:26:6"},
        description = "철수에 성공하여 수녀 리스와 우연히 마주쳤습니다.",
        goalDesc = {"동쪽으로 철퇴"},
        id = 10323,
        missiondone = {"NT10324"},
        name = "연을 피하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11332", "E11332:10323", "E5061:10323"},
        submitNpcId = 11332,
        submitRewardStr = {"R1323"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10324] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5056,
        autoDoNextTask = 10325,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "수녀 리스와 대화를 통해 유명진에 두 개의 성당가 있었다는 것을 알게 되었습니다.",
        goalDesc = {"리스와 대화"},
        id = 10324,
        missiondone = {"NT10325"},
        name = "두 성당",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5056:10324"},
        submitNpcId = 5056,
        submitRewardStr = {"R1324"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10325] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11333,
        autoDoNextTask = 10326,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단이 일부러 길목에 남아있습니다,당신한테 할 말이 있어 보입니다.",
        goalDesc = {"단의 경고"},
        id = 10325,
        missiondone = {"NT10326"},
        name = "경고",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11333", "E11333:10325"},
        submitNpcId = 11333,
        submitRewardStr = {"R1325"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10326] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11334,
        autoDoNextTask = 10327,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "서로 상투적인 말을 주고 받은 후 단의 의문점이 점점 많아졌습니다.",
        goalDesc = {"통수부 간첩"},
        id = 10326,
        missiondone = {"NT10327"},
        name = "의심",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11334", "E11334:10326"},
        submitNpcId = 11334,
        submitRewardStr = {"R1326"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10327] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5060,
        autoDoNextTask = 10328,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "단과 싸우는 동안 순찰팀의 주의를 끌었습니다.",
        goalDesc = {"주의를 끌다"},
        id = 10327,
        missiondone = {"NT10328"},
        name = "싸움을 설득하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11335", "E11335:10327", "E5060:10327"},
        submitNpcId = 11335,
        submitRewardStr = {"R1327"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10328] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11337,
        autoDoNextTask = 10329,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중급팀의 대사건으로 중화는 매우 초조합니다!",
        goalDesc = {"중화 찾기"},
        id = 10328,
        missiondone = {"NT10329"},
        name = "집행관 실종",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE11337:101000:18:16", "NC11337", "NC11336", "E11336:10328", "E11337:10328"},
        submitNpcId = 11336,
        submitRewardStr = {"R1328"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10329] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11338,
        autoDoNextTask = 10330,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "이루 집행관에게 흑과 백을 목격했는지에 대해 물으세요.",
        goalDesc = {"이루와 대화"},
        id = 10329,
        missiondone = {"NT10330"},
        name = "집행관 수색",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11338", "E11338:10329"},
        submitNpcId = 11338,
        submitRewardStr = {"R1329"},
        taskWalkingTips = "none;일은 나리의 말처럼 쉽지 않습니다.;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10330] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11339,
        autoDoNextTask = 10331,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "지금까지 획득한 단서에 근거하여 다음 대책을 상의하세요.",
        goalDesc = {"다음 계책"},
        id = 10330,
        missiondone = {"NT10331"},
        name = "충화의 고뇌",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11339", "NC11425", "E11425:10330", "E11339:10330", "TTRACE11339:101000:14.3:10.5"},
        submitNpcId = 11425,
        submitRewardStr = {"R1330"},
        taskWalkingTips = "정말 돌아오지 않았다니!;none;none",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10331] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11341,
        autoDoNextTask = 10332,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중화와 함께 제두로 막 돌아온 단을 만났고,중급팀의 현재 상황에 대한 의견을 제시했습니다.",
        goalDesc = {"단의 의견"},
        id = 10331,
        missiondone = {"NT10332"},
        name = "전근 신청",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11341", "E11341:10331"},
        submitNpcId = 11341,
        submitRewardStr = {"R1331"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10332] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11342,
        autoDoNextTask = 10333,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "길목에서 단에게 가로막혔는데,그는 무슨 말을 하려는 거죠?",
        goalDesc = {"단의 의도"},
        id = 10332,
        missiondone = {"NT10333"},
        name = "서로를 시험하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11342", "E11342:10332"},
        submitNpcId = 11342,
        submitRewardStr = {"R1332"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10333] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11343,
        autoDoNextTask = 10334,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "흑 나리과 백 나리을 찾는 유일한 방법은 하나뿐입니다.",
        goalDesc = {"부도에게 도움 요청"},
        id = 10333,
        missiondone = {"NT10334"},
        name = "명계의 입구",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11343", "NC11345", "E11343:10333", "TTRACE11343:205000:10:18.5"},
        submitNpcId = 11345,
        submitRewardStr = {"R1333"},
        taskWalkingTips = "이젠 그를 찾을 수밖에 없습니다!;none;none",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10334] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11349,
        autoDoNextTask = 10335,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE206000:10:16"},
        description = "부도의 도움으로 성공적으로 명계로 향합니다.",
        goalDesc = {"명계로 이동"},
        id = 10334,
        missiondone = {"NT10335"},
        name = "이계의 여행",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11349", "E11349:10334"},
        submitNpcId = 11349,
        submitRewardStr = {"R1334"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10335] = {
        AcceptCallPlot = 0,
        ChapterFb = "8,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5062,
        autoDoNextTask = 10336,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "마침내 명계에 도착하였고 행인을 찾아 알아봅시다.",
        goalDesc = {"귀족"},
        id = 10335,
        missiondone = {"NT10336"},
        name = "평행 세계",
        playid = 10010,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5062:10335"},
        submitNpcId = 5062,
        submitRewardStr = {"R1335"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10336] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5062,
        autoDoNextTask = 10337,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "귀족은 모두 소통하기가 이렇게 어려운가요?다음 행인을 찾아봅시다.",
        goalDesc = {"이상한 세계"},
        id = 10336,
        missiondone = {"NT10337"},
        name = "서로 삼키다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5062:10336"},
        submitNpcId = 5062,
        submitRewardStr = {"R1336"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10337] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11350,
        autoDoNextTask = 10338,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "백귀가 도망치는데,도대체 누가 온것이죠?",
        goalDesc = {"귀유자"},
        id = 10337,
        missiondone = {"NT10338"},
        name = "도망치다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11350", "NC11351", "E11350:10337", "E11351:10337"},
        submitNpcId = 11351,
        submitRewardStr = {"R1337"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10338] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11352,
        autoDoNextTask = 10339,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "명계에도 질서를  유지하는 조직이 있다니?그녀는......",
        goalDesc = {"아방"},
        id = 10338,
        missiondone = {"NT10339"},
        name = "순찰대",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11352", "NC11353", "E11352:10338", "E11353:10338"},
        submitNpcId = 11352,
        submitRewardStr = {"R1338"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10339] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,1",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11355,
        autoDoNextTask = 10340,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "명계에 살아있는 자가 들어가는 것을 금지합니다.",
        goalDesc = {"마면면"},
        id = 10339,
        missiondone = {"NT10340"},
        name = "금지 명령",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11354", "NC11355", "E11354:10339", "E11355:10339"},
        submitNpcId = 11355,
        submitRewardStr = {"R1339"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10340] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11357,
        autoDoNextTask = 10341,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "명계에 온 목적을 알리세요.",
        goalDesc = {"목적"},
        id = 10340,
        missiondone = {"NT10341"},
        name = "문제의 원인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11356", "NC11357", "E11356:10340", "E11357:10340"},
        submitNpcId = 11357,
        submitRewardStr = {"R1340"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10341] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11358,
        autoDoNextTask = 10342,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "아방과 마면면에게 당신의 말이 진실이라는 것을 증명하세요.",
        goalDesc = {"혼석"},
        id = 10341,
        missiondone = {"NT10342"},
        name = "증인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11358", "E11358:10341"},
        submitNpcId = 11358,
        submitRewardStr = {"R1341"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10342] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11359,
        autoDoNextTask = 10343,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "혼석은 아방과 마면면에게 흑과 백의 일에 대해 설명합니다.",
        goalDesc = {"증언하다"},
        id = 10342,
        missiondone = {"NT10343"},
        name = "흑과 백",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11359", "NC11360", "E11359:10342", "E11360:10342"},
        submitNpcId = 11359,
        submitRewardStr = {"R1342"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10343] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11363,
        autoDoNextTask = 10344,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "눈앞의 귀족이 도울 수 없다면 도망갈 방법을 찾아야 합니다.",
        goalDesc = {"떠날 방법을 찾아"},
        id = 10343,
        missiondone = {"NT10344"},
        name = "도망치다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"TTRACE11363:206000:15.8:12.2", "NC11361", "NC11362", "NC11363", "E11361:10343", "E11363:10343"},
        submitNpcId = 11361,
        submitRewardStr = {"R1343"},
        taskWalkingTips = "",
        tasktype = 10,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10344] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11364,
        autoDoNextTask = 10345,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "다른 사람의 제약을 받아,아방과 마면이 당신을 어디로 데려갈 것 같습니다.",
        goalDesc = {"다루다"},
        id = 10344,
        missiondone = {"NT10345"},
        name = "강한 말",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11364", "NC11365", "E11364:10344", "E11365:10344"},
        submitNpcId = 11364,
        submitRewardStr = {"R1344"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10345] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11366,
        autoDoNextTask = 10346,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "앞에서 북소리가 간간이 들려오는데,누가 북을 치고 있는 걸까요?",
        goalDesc = {"마면을 따라가기"},
        id = 10345,
        missiondone = {"NT10346"},
        name = "박수소리 중",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11366", "NC11367", "E11366:10345", "E11367:10345", "TTRACE11366:206000:11:6"},
        submitNpcId = 11367,
        submitRewardStr = {"R1345"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10346] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11368,
        autoDoNextTask = 10347,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "망천강의 편무가 당신에게 특히 관심을 보이는 것 같습니다.",
        goalDesc = {"편무"},
        id = 10346,
        missiondone = {"NT10347"},
        name = "만천의 반",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11368", "E11368:10346"},
        submitNpcId = 11368,
        submitRewardStr = {"R1346"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10347] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11369,
        autoDoNextTask = 10348,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "편무의 추격을 피해 흑과 백을 계속 찾으세요.",
        goalDesc = {"편무 해결"},
        id = 10347,
        missiondone = {"NT10348"},
        name = "막다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11369", "E11369:10347"},
        submitNpcId = 11369,
        submitRewardStr = {"R1347"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10348] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11371,
        autoDoNextTask = 10349,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE206000:22:5"},
        description = "모두를 피해 망천을 떠나세요.",
        goalDesc = {"흑백을 찾아서"},
        id = 10348,
        missiondone = {"NT10349"},
        name = "다시 탈출",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11370", "NC11371", "E11370:10348", "E11371:10348", "E11372:10348"},
        submitNpcId = 11371,
        submitRewardStr = {"R1348"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10349] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11373,
        autoDoNextTask = 10350,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "앞에 있는 사람이 바로 월견도에서 만났던 그 귀족이 아닌가?",
        goalDesc = {"사화와 재회"},
        id = 10349,
        missiondone = {"NT10350"},
        name = "기억 실종",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11373", "E11373:10349"},
        submitNpcId = 11373,
        submitRewardStr = {"R1349"},
        taskWalkingTips = "그건!;none;none",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10350] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11374,
        autoDoNextTask = 10351,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "만주사화와 교류를 통해 아무런 결과를 획득하지 못했고,서글퍼할 때 혼석이 쫓아왔습니다.",
        goalDesc = {"혼석이 따라오다"},
        id = 10350,
        missiondone = {"NT10351"},
        name = "이상한 곳",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11374", "E11374:10350", "E11375:10350"},
        submitNpcId = 11374,
        submitRewardStr = {"R1350"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10351] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11377,
        autoDoNextTask = 10352,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "드디어 도움을 받아 흑과 백을 찾아갈 수 있습니다.",
        goalDesc = {"혼석과 대화"},
        id = 10351,
        missiondone = {"NT10352"},
        name = "돕다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11377", "NC11378", "E11377:10351", "E11378:10351"},
        submitNpcId = 11377,
        submitRewardStr = {"R1351"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10352] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11380,
        autoDoNextTask = 10353,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "막 길을 떠나려는데,서지의 출현으로 모두 멈추었습니다.",
        goalDesc = {"무지 등장"},
        id = 10352,
        missiondone = {"NT10353"},
        name = "길을  막는 귀신",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11379", "NC11380", "E11379:10352", "E11380:10352", "TTRACE11380:206000:22:3"},
        submitNpcId = 11379,
        submitRewardStr = {"R1352"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10353] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11381,
        autoDoNextTask = 10354,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "편무가 나타나다니,이 모든게 시련인건가?",
        goalDesc = {"모든 것은 시험이다"},
        id = 10353,
        missiondone = {"NT10354"},
        name = "멈추기",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11381", "NC11382", "E11381:10353", "E11382:10353"},
        submitNpcId = 11381,
        submitRewardStr = {"R1353"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10354] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11383,
        autoDoNextTask = 10355,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "판관을 찾아가 사정하는 것에 대해 편무는 봉주야학을 직접 찾는 것을 추천하다니?",
        goalDesc = {"봉주 찾기"},
        id = 10354,
        missiondone = {"NT10355"},
        name = "제안",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11383", "NC11384", "E11383:10354", "E11384:10354"},
        submitNpcId = 11383,
        submitRewardStr = {"R1354"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10355] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11385,
        autoDoNextTask = 10356,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "말도 없이 시험을 진행한 것에 혼석은 모두를 대표해서 사과를 하고 싶어합니다.",
        goalDesc = {"혼석의 말"},
        id = 10355,
        missiondone = {"NT10356"},
        name = "사과",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11385", "E11385:10355"},
        submitNpcId = 11385,
        submitRewardStr = {"R1355"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10356] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11386,
        autoDoNextTask = 10357,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "42",
        goalDesc = {"탄천 달래기"},
        id = 10356,
        missiondone = {"NT10357"},
        name = "오요는 어떡하지",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11386", "NC11387", "E11386:10356", "E11387:10356"},
        submitNpcId = 11387,
        submitRewardStr = {"R1356"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10357] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11388,
        autoDoNextTask = 10358,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "마침내 탄천을 따라 판관의 앞에 이르렀습니다.",
        goalDesc = {"탄천 따라가기"},
        id = 10357,
        missiondone = {"NT10358"},
        name = "결책자",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11388", "NC11389", "E11388:10357", "E11389:10357", "TTRACE11388:206000:19.6:15.3"},
        submitNpcId = 11389,
        submitRewardStr = {"R1357"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10358] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11391,
        autoDoNextTask = 10359,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉주가 아니라 판관을 먼저 만나게 될 줄이야!",
        goalDesc = {"판관과 대화"},
        id = 10358,
        missiondone = {"NT10359"},
        name = "오만",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11391", "NC11390", "E11390:10358", "E11391:10358"},
        submitNpcId = 11391,
        submitRewardStr = {"R1358"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10359] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11392,
        autoDoNextTask = 10360,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "흑과 백을 구한 목적은 무엇인가요?판관은 의혹을 제기합니다.",
        goalDesc = {"판관의 질문"},
        id = 10359,
        missiondone = {"NT10360"},
        name = "목적",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11392", "NC11393", "NC11394", "E11392:10359", "E11393:10359", "E11394:10359"},
        submitNpcId = 11392,
        submitRewardStr = {"R1359"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10360] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11396,
        autoDoNextTask = 10361,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉주야학은 원래 이런 성격이었는데......",
        goalDesc = {"봉주야학"},
        id = 10360,
        missiondone = {"NT10361"},
        name = "명계의 마스터",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11395", "NC11396", "NC11397", "NC11398", "NC11398", "E11398:10360"},
        submitNpcId = 11397,
        submitRewardStr = {"R1360"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10361] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11400,
        autoDoNextTask = 10362,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉주야학은 원래 이런 성격이었는데......",
        goalDesc = {"형언할 수 없는"},
        id = 10361,
        missiondone = {"NT10362"},
        name = "예외",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11399", "NC11400", "E11400:10361"},
        submitNpcId = 11400,
        submitRewardStr = {"R1361"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10362] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11402,
        autoDoNextTask = 10363,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉주는 당신에게 아주 오래 전의 이야기를 들려줍니다......",
        goalDesc = {"봉주의 이야기"},
        id = 10362,
        missiondone = {"NT10363"},
        name = "힌트",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11401", "NC11402", "E11402:10362"},
        submitNpcId = 11402,
        submitRewardStr = {"R1362"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10363] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11403,
        autoDoNextTask = 10364,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "운명의 아이에 대해 봉주야학의 속셈은 따로 있는 것 같습니다.",
        goalDesc = {"좋아하는 사람"},
        id = 10363,
        missiondone = {"NT10364"},
        name = "진기함",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11403", "NC11404", "E11403:10363", "E11404:10363"},
        submitNpcId = 11403,
        submitRewardStr = {"R1363"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10364] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11405,
        autoDoNextTask = 10365,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "흑과 백이 갇혀있지 않다니,그럼 모든 것이 거짓이란 말인가?",
        goalDesc = {"오해가 풀렸다"},
        id = 10364,
        missiondone = {"NT10365"},
        name = "압도하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11405", "NC11406", "NC11407", "NC11408", "E11405:10364", "E11406:10364"},
        submitNpcId = 11405,
        submitRewardStr = {"R1364"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10365] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11409,
        autoDoNextTask = 10366,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "봉주께서 너를 한쪽으로 끌어다 놓고 말 못할 사정이 있는 것 같은데?",
        goalDesc = {"흑백 판결"},
        id = 10365,
        missiondone = {"NT10366"},
        name = "명령하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11409", "NC11410", "E11409:10365", "E11410:10365"},
        submitNpcId = 11409,
        submitRewardStr = {"R1365"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10366] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11411,
        autoDoNextTask = 10367,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "명계가 너그럽게 대하는 이유가 부도 때문인걸까?",
        goalDesc = {"봉주의 요청"},
        id = 10366,
        missiondone = {"NT10367"},
        name = "대가",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11411", "E11411:10366"},
        submitNpcId = 11411,
        submitRewardStr = {"R1366"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10367] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11412,
        autoDoNextTask = 10368,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "흑이 고맙다고 했다니,놀랍지 않아,뜻밖이지,감동한 거야?",
        goalDesc = {"흑백과 대화"},
        id = 10367,
        missiondone = {"NT10368"},
        name = "고마움",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11412", "NC11413", "E11412:10367", "E11413:10367"},
        submitNpcId = 11413,
        submitRewardStr = {"R1367"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10368] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11415,
        autoDoNextTask = 10369,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "흑과 백의 인연이 정말 이렇게 깊을 줄이야!",
        goalDesc = {"이것이 파트너다"},
        id = 10368,
        missiondone = {"NT10369"},
        name = "명계중귀",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11414", "NC11415", "NC11416", "NC11417", "NC11418", "E11414:10368", "E11415:10368", "E11416:10368", "E11417:10368", "E11418:10368"},
        submitNpcId = 11415,
        submitRewardStr = {"R1368"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10369] = {
        AcceptCallPlot = 0,
        ChapterFb = "9,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11420,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE204000:16:6"},
        description = "흑과 백을 따라 명계를 떠났습니다.",
        goalDesc = {"명계 떠나기"},
        id = 10369,
        missiondone = {"NT10370"},
        name = "목표 달성",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11419", "NC11420", "E11419:10369", "E11420:10369"},
        submitNpcId = 11420,
        submitRewardStr = {"R1369"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10370] = {
        AcceptCallPlot = 0,
        ChapterFb = "15,8",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11421,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "중급팀이 마주친 큰 사건은 마음을 불안하게 만듭니다!",
        goalDesc = {"신병 삼인조"},
        id = 10370,
        missiondone = {},
        name = "분열",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11421", "NC11422", "NC11423", "E11421:10370", "E11422:10370", "E11423:10370"},
        submitNpcId = 11421,
        submitRewardStr = {"R1370"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10371] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,2",
        acceptConditionStr = {"AI:11603:1:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11426,
        autoDoNextTask = 10372,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"TI:11603:1:0"},
        description = "무도대회와 관련해 중요한 정보를 빠트렸다니?",
        goalDesc = {"무언가가 빠졌어"},
        id = 10371,
        missiondone = {"NT10372"},
        name = "편차",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11426", "E11426:10371"},
        submitNpcId = 11426,
        submitRewardStr = {"R1371"},
        taskWalkingTips = "",
        tasktype = 7,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10372] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,2",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11428,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "무도대회에 참가하려면 특별한 조건을 충족해야 하는 거였다니?",
        goalDesc = {"자격 신청"},
        id = 10372,
        missiondone = {"NT10010"},
        name = "추천서",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11427", "NC11428", "E11427:10372", "E11428:10372"},
        submitNpcId = 11428,
        submitRewardStr = {"R1372"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10373] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5003,
        autoDoNextTask = 10374,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:17:15"},
        description = "행인에게 제두 무관의 위치를 물어보세요.",
        goalDesc = {"제두 무관"},
        id = 10373,
        missiondone = {"NT10374"},
        name = "길을 묻다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5003:10373"},
        submitNpcId = 5003,
        submitRewardStr = {"R1373"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10374] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,3",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11429,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "기본 테스트를 클리어해 형명을 찾으세요.",
        goalDesc = {"기본 테스트"},
        id = 10374,
        missiondone = {"NT10012"},
        name = "막다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11429", "E11429:10374"},
        submitNpcId = 11429,
        submitRewardStr = {"R1374"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10375] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5048,
        autoDoNextTask = 10376,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "게시판 옆에 있는 이상한 아저씨는 무슨 말을 하고 있는 건 가요?",
        goalDesc = {"마스코트"},
        id = 10375,
        missiondone = {"NT10376"},
        name = "이상한 사람",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5048:10375"},
        submitNpcId = 5048,
        submitRewardStr = {"R1375"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10376] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5048,
        autoDoNextTask = 10377,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"PLACE101000:13:20.8"},
        description = "제두의 길에는 늘 기억을 잃은 신비로운 소녀가 등장한다면서요?",
        goalDesc = {"아저씨의 명령"},
        id = 10376,
        missiondone = {"NT10377"},
        name = "좋은 사람",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5048:10376"},
        submitNpcId = 5048,
        submitRewardStr = {"R1376"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10377] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,4",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11430,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "공교롭게도 그 신비로운 소녀를 만나게 되었는데?",
        goalDesc = {"우연이거나 필연이거나"},
        id = 10377,
        missiondone = {"NT10015"},
        name = "기억 상실 소녀",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11430", "E11430:10377"},
        submitNpcId = 11430,
        submitRewardStr = {"R1377"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10378] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,5",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5007,
        autoDoNextTask = 10018,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "결국 도망치게 할 줄은 몰랐습니다.",
        goalDesc = {"탈출한 뚜루"},
        id = 10378,
        missiondone = {"NT10018"},
        name = "체포 실패",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11431", "E11431:10378", "E5007:10378"},
        submitNpcId = 5007,
        submitRewardStr = {"R1378"},
        taskWalkingTips = "",
        tasktype = 3,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10379] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11432,
        autoDoNextTask = 10380,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "무도대회 출전을 앞둔 선수로서 뚜루새 한 마리도 잡지 못할 리 없습니다.",
        goalDesc = {"뚜뚜루를 따라가다"},
        id = 10379,
        missiondone = {"NT10380"},
        name = "계속 체포하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11432", "E11432:10379", "E5004:10379", "TTRACE11432:101000:44.7:26.5"},
        submitNpcId = 5004,
        submitRewardStr = {"R1379"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10380] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11433,
        autoDoNextTask = 10381,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "뚜루루를 잡은 이유를 리스 수녀에게 설명하세요.",
        goalDesc = {"수녀의 설득"},
        id = 10380,
        missiondone = {"NT10381"},
        name = "예상치 못한 상황",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11433", "E11433:10380", "E5004:10380"},
        submitNpcId = 5004,
        submitRewardStr = {"R1380"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10381] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5004,
        autoDoNextTask = 10021,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "속도를 내서 뚜루루를 잡으세요.",
        goalDesc = {"속전속결"},
        id = 10381,
        missiondone = {"NT10021"},
        name = "엉망",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11434", "E11434:10381", "E5004:10381"},
        submitNpcId = 5004,
        submitRewardStr = {"R1381"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10382] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5005,
        autoDoNextTask = 10382,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "여기저기 쏘다니고 있는 뚜루루를 잡았습니다.",
        goalDesc = {"뚜뚜루를 따라가다"},
        id = 10382,
        missiondone = {"NT10383"},
        name = "횡포",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11435", "E5004:10382", "E5005:10382", "E11435:10382", "TTRACE11435:101000:44.6:26.4"},
        submitNpcId = 5004,
        submitRewardStr = {"R1382"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10383] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,6",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11436,
        autoDoNextTask = 0,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "드디어 뚜루루를 잡았습니다!",
        goalDesc = {"저항을 포기하다"},
        id = 10383,
        missiondone = {"NT10022"},
        name = "항복하도록 설득하다",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11436", "E11436:10383"},
        submitNpcId = 11436,
        submitRewardStr = {"R1383"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10384] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 5005,
        autoDoNextTask = 10023,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "성당 사람들이 통수부에 대해 묘사할 수 없는 적의를 갖고 있는 것 같습니다.",
        goalDesc = {"성당과 통수부"},
        id = 10384,
        missiondone = {"NT10023"},
        name = "야만인",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"E5005:10384"},
        submitNpcId = 5005,
        submitRewardStr = {"R1384"},
        taskWalkingTips = "",
        tasktype = 1,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

    [10385] = {
        AcceptCallPlot = 0,
        ChapterFb = "1,7",
        acceptConditionStr = {"grade:0"},
        acceptDialogConfig = {},
        acceptNpcId = 11437,
        autoDoNextTask = 10025,
        autoSubmit = 0,
        autotype = 1,
        battleConfirm = 0,
        clientExtStr = "",
        config = {"Time30"},
        description = "여기저기 쏘다니고 있는 뚜루루를 잡았습니다.",
        goalDesc = {"이루 따라가기"},
        id = 10385,
        missiondone = {"NT10025"},
        name = "수사 협조",
        playid = 0,
        quickFindWay = 0,
        repeatType = 1,
        submitCallPlot = 0,
        submitConditionStr = {"NC11437", "NC11438", "E11437:10385", "E11438:10385", "TTRACE11437:101000:5.6:23.7"},
        submitNpcId = 11438,
        submitRewardStr = {"R1385"},
        taskWalkingTips = "",
        tasktype = 9,
        teamwork = 0,
        tip_type = 0,
        tips = 0,
        type = 2,
    },

}
