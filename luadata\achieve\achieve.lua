-- ./excel/achieve/achieve.xlsx
return {

    [10101] = {
        belong = 1,
        condition = "캐릭터 레벨=10",
        degreetype = 1,
        desc = "캐릭터 10레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10101,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 0,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=3000)"}},
        sub_direction = 1,
    },

    [10102] = {
        belong = 1,
        condition = "캐릭터 레벨=20",
        degreetype = 1,
        desc = "캐릭터 20레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10102,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10101,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=3000)"}},
        sub_direction = 1,
    },

    [10103] = {
        belong = 1,
        condition = "캐릭터 레벨=30",
        degreetype = 1,
        desc = "캐릭터 30레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10103,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10102,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 1,
    },

    [10104] = {
        belong = 1,
        condition = "캐릭터 레벨=40",
        degreetype = 1,
        desc = "캐릭터 40레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10104,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10103,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 1,
    },

    [10105] = {
        belong = 1,
        condition = "캐릭터 레벨=50",
        degreetype = 1,
        desc = "캐릭터 50레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10105,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10104,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 1,
    },

    [10106] = {
        belong = 1,
        condition = "캐릭터 레벨=60",
        degreetype = 1,
        desc = "캐릭터 60레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10106,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10105,
        point = 22,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=20)"}},
        sub_direction = 1,
    },

    [10107] = {
        belong = 1,
        condition = "캐릭터 레벨=70",
        degreetype = 1,
        desc = "캐릭터 70레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10107,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10106,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=30)"}},
        sub_direction = 1,
    },

    [10108] = {
        belong = 1,
        condition = "캐릭터 레벨=80",
        degreetype = 1,
        desc = "캐릭터 80레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10108,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10107,
        point = 40,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 1,
    },

    [10109] = {
        belong = 1,
        condition = "캐릭터 레벨=90",
        degreetype = 1,
        desc = "캐릭터 90레벨 달성",
        direction = 1,
        doneshow = 0,
        id = 10109,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10108,
        point = 53,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=55)"}},
        sub_direction = 1,
    },

    [10110] = {
        belong = 1,
        condition = "캐릭터 레벨=100",
        degreetype = 1,
        desc = "캐릭터 100레벨 달성",
        direction = 1,
        doneshow = 1,
        id = 10110,
        maxtype = 0,
        name = "꿈이자 목표!",
        needdone = 10109,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 1,
    },

    [10201] = {
        belong = 1,
        condition = "유저 전투력=5000",
        degreetype = 1,
        desc = "5000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10201,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 0,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=3000)"}},
        sub_direction = 2,
    },

    [10202] = {
        belong = 1,
        condition = "유저 전투력=10000",
        degreetype = 1,
        desc = "10000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10202,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10201,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=3000)"}},
        sub_direction = 2,
    },

    [10203] = {
        belong = 1,
        condition = "유저 전투력=20000",
        degreetype = 1,
        desc = "20000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10203,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10202,
        point = 9,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=7000)"}},
        sub_direction = 2,
    },

    [10204] = {
        belong = 1,
        condition = "유저 전투력=30000",
        degreetype = 1,
        desc = "30000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10204,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10203,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 2,
    },

    [10205] = {
        belong = 1,
        condition = "유저 전투력=40000",
        degreetype = 1,
        desc = "40000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10205,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10204,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 2,
    },

    [10206] = {
        belong = 1,
        condition = "유저 전투력=50000",
        degreetype = 1,
        desc = "50000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10206,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10205,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=20)"}},
        sub_direction = 2,
    },

    [10207] = {
        belong = 1,
        condition = "유저 전투력=60000",
        degreetype = 1,
        desc = "60000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10207,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10206,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=30)"}},
        sub_direction = 2,
    },

    [10208] = {
        belong = 1,
        condition = "유저 전투력=70000",
        degreetype = 1,
        desc = "70000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10208,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10207,
        point = 40,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 2,
    },

    [10209] = {
        belong = 1,
        condition = "유저 전투력=80000",
        degreetype = 1,
        desc = "80000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10209,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10208,
        point = 53,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=55)"}},
        sub_direction = 2,
    },

    [10210] = {
        belong = 1,
        condition = "유저 전투력=90000",
        degreetype = 1,
        desc = "90000 전투력 달성",
        direction = 1,
        doneshow = 0,
        id = 10210,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10209,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 2,
    },

    [10211] = {
        belong = 1,
        condition = "유저 전투력=100000",
        degreetype = 1,
        desc = "100000 전투력 달성",
        direction = 1,
        doneshow = 1,
        id = 10211,
        maxtype = 0,
        name = "길이 함께 가자!",
        needdone = 10210,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 2,
    },

    [10301] = {
        belong = 2,
        condition = "골드 누적 획득=100000",
        degreetype = 2,
        desc = "100000 골드 획득",
        direction = 1,
        doneshow = 0,
        id = 10301,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 0,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 3,
    },

    [10302] = {
        belong = 2,
        condition = "골드 누적 획득=500000",
        degreetype = 2,
        desc = "50만 골드 획득",
        direction = 1,
        doneshow = 0,
        id = 10302,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10301,
        point = 9,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=7000)"}},
        sub_direction = 3,
    },

    [10303] = {
        belong = 2,
        condition = "골드 누적 획득=5000000",
        degreetype = 2,
        desc = "500만 골드 획득",
        direction = 1,
        doneshow = 0,
        id = 10303,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10302,
        point = 28,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=21000)"}},
        sub_direction = 3,
    },

    [10304] = {
        belong = 2,
        condition = "골드 누적 획득=10000000",
        degreetype = 2,
        desc = "1000만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10304,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10303,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10305] = {
        belong = 2,
        condition = "골드 누적 획득=15000000",
        degreetype = 2,
        desc = "1500만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10305,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10304,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10306] = {
        belong = 2,
        condition = "골드 누적 획득=20000000",
        degreetype = 2,
        desc = "2000만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10306,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10305,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10307] = {
        belong = 2,
        condition = "골드 누적 획득=25000000",
        degreetype = 2,
        desc = "2500만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10307,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10306,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10308] = {
        belong = 2,
        condition = "골드 누적 획득=30000000",
        degreetype = 2,
        desc = "3000만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10308,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10307,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10309] = {
        belong = 2,
        condition = "골드 누적 획득=35000000",
        degreetype = 2,
        desc = "3500만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10309,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10308,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10310] = {
        belong = 2,
        condition = "골드 누적 획득=40000000",
        degreetype = 2,
        desc = "4000만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10310,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10309,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10311] = {
        belong = 2,
        condition = "골드 누적 획득=45000000",
        degreetype = 2,
        desc = "4500만 골드 회득",
        direction = 1,
        doneshow = 0,
        id = 10311,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10310,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10312] = {
        belong = 2,
        condition = "골드 누적 획득=50000000",
        degreetype = 2,
        desc = "5000만 골드 회득",
        direction = 1,
        doneshow = 1,
        id = 10312,
        maxtype = 0,
        name = "부자의 탄생!",
        needdone = 10311,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 3,
    },

    [10401] = {
        belong = 2,
        condition = "골드 누적 소모=50000",
        degreetype = 2,
        desc = "50000 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10401,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 0,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=3000)"}},
        sub_direction = 4,
    },

    [10402] = {
        belong = 2,
        condition = "골드 누적 소모=250000",
        degreetype = 2,
        desc = "25만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10402,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10401,
        point = 7,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        sub_direction = 4,
    },

    [10403] = {
        belong = 2,
        condition = "골드 누적 소모=2500000",
        degreetype = 2,
        desc = "250만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10403,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10402,
        point = 22,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10404] = {
        belong = 2,
        condition = "골드 누적 소모=5000000",
        degreetype = 2,
        desc = "500만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10404,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10403,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10405] = {
        belong = 2,
        condition = "골드 누적 소모=7500000",
        degreetype = 2,
        desc = "750만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10405,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10404,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10406] = {
        belong = 2,
        condition = "골드 누적 소모=10000000",
        degreetype = 2,
        desc = "1000만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10406,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10405,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10407] = {
        belong = 2,
        condition = "골드 누적 소모=12500000",
        degreetype = 2,
        desc = "1250만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10407,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10406,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10408] = {
        belong = 2,
        condition = "골드 누적 소모=15000000",
        degreetype = 2,
        desc = "1500만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10408,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10407,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10409] = {
        belong = 2,
        condition = "골드 누적 소모=17500000",
        degreetype = 2,
        desc = "1750만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10409,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10408,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10410] = {
        belong = 2,
        condition = "골드 누적 소모=20000000",
        degreetype = 2,
        desc = "2000만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10410,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10409,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10411] = {
        belong = 2,
        condition = "골드 누적 소모=22500000",
        degreetype = 2,
        desc = "2250만 골드 소비",
        direction = 1,
        doneshow = 0,
        id = 10411,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10410,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10412] = {
        belong = 2,
        condition = "골드 누적 소모=25000000",
        degreetype = 2,
        desc = "2500만 골드 소비",
        direction = 1,
        doneshow = 1,
        id = 10412,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 10411,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 4,
    },

    [10501] = {
        belong = 2,
        condition = "수정 누적 소모=2000",
        degreetype = 2,
        desc = "2000 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10501,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 0,
        point = 12,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=60)"}},
        sub_direction = 5,
    },

    [10502] = {
        belong = 2,
        condition = "수정 누적 소모=5000",
        degreetype = 2,
        desc = "5000 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10502,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10501,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 5,
    },

    [10503] = {
        belong = 2,
        condition = "수정 누적 소모=20000",
        degreetype = 2,
        desc = "20000 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10503,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10502,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=160)"}},
        sub_direction = 5,
    },

    [10504] = {
        belong = 2,
        condition = "수정 누적 소모=40000",
        degreetype = 2,
        desc = "40000 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10504,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10503,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10505] = {
        belong = 2,
        condition = "수정 누적 소모=60000",
        degreetype = 2,
        desc = "60000 수정 소비",
        direction = 1,
        doneshow = 1,
        id = 10505,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10504,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10506] = {
        belong = 2,
        condition = "수정 누적 소모=80000",
        degreetype = 2,
        desc = "80000 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10506,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10505,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10507] = {
        belong = 2,
        condition = "수정 누적 소모=100000",
        degreetype = 2,
        desc = "10만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10507,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10506,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10508] = {
        belong = 2,
        condition = "수정 누적 소모=120000",
        degreetype = 2,
        desc = "12만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10508,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10507,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10509] = {
        belong = 2,
        condition = "수정 누적 소모=140000",
        degreetype = 2,
        desc = "14만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10509,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10508,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10510] = {
        belong = 2,
        condition = "수정 누적 소모=160000",
        degreetype = 2,
        desc = "16만 수정 소비",
        direction = 1,
        doneshow = 1,
        id = 10510,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10509,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10511] = {
        belong = 2,
        condition = "수정 누적 소모=180000",
        degreetype = 2,
        desc = "18만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10511,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10510,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10512] = {
        belong = 2,
        condition = "수정 누적 소모=200000",
        degreetype = 2,
        desc = "20만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10512,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10511,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10513] = {
        belong = 2,
        condition = "수정 누적 소모=220000",
        degreetype = 2,
        desc = "22만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10513,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10512,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10514] = {
        belong = 2,
        condition = "수정 누적 소모=240000",
        degreetype = 2,
        desc = "24만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10514,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10513,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10515] = {
        belong = 2,
        condition = "수정 누적 소모=260000",
        degreetype = 2,
        desc = "26만 수정 소비",
        direction = 1,
        doneshow = 1,
        id = 10515,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10514,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10516] = {
        belong = 2,
        condition = "수정 누적 소모=280000",
        degreetype = 2,
        desc = "28만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10516,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10515,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10517] = {
        belong = 2,
        condition = "수정 누적 소모=300000",
        degreetype = 2,
        desc = "30만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10517,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10516,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10518] = {
        belong = 2,
        condition = "수정 누적 소모=320000",
        degreetype = 2,
        desc = "32만 수정 소비",
        direction = 1,
        doneshow = 0,
        id = 10518,
        maxtype = 0,
        name = "손해없는 비용!",
        needdone = 10517,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=190)"}},
        sub_direction = 5,
    },

    [10601] = {
        belong = 2,
        condition = "누적 충전=60",
        degreetype = 2,
        desc = "누적충전 60다이아",
        direction = 1,
        doneshow = 0,
        id = 10601,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 6,
    },

    [10602] = {
        belong = 2,
        condition = "누적 충전=300",
        degreetype = 2,
        desc = "누적충전 300다이아",
        direction = 1,
        doneshow = 0,
        id = 10602,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10601,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 6,
    },

    [10603] = {
        belong = 2,
        condition = "누적 충전=980",
        degreetype = 2,
        desc = "누적충전 980다이아",
        direction = 1,
        doneshow = 0,
        id = 10603,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10602,
        point = 27,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=140)"}},
        sub_direction = 6,
    },

    [10604] = {
        belong = 2,
        condition = "누적 충전=3280",
        degreetype = 2,
        desc = "누적충전 3280다이아",
        direction = 1,
        doneshow = 0,
        id = 10604,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10603,
        point = 48,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=240)"}},
        sub_direction = 6,
    },

    [10605] = {
        belong = 2,
        condition = "누적 충전=6480",
        degreetype = 2,
        desc = "누적충전 6480다이아",
        direction = 1,
        doneshow = 0,
        id = 10605,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10604,
        point = 57,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=290)"}},
        sub_direction = 6,
    },

    [10606] = {
        belong = 2,
        condition = "누적 충전=10000",
        degreetype = 2,
        desc = "누적충전 10000다이아",
        direction = 1,
        doneshow = 0,
        id = 10606,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10605,
        point = 60,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=300)"}},
        sub_direction = 6,
    },

    [10607] = {
        belong = 2,
        condition = "누적 충전=20000",
        degreetype = 2,
        desc = "누적충전 20000다이아",
        direction = 1,
        doneshow = 0,
        id = 10607,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10606,
        point = 100,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=500)"}},
        sub_direction = 6,
    },

    [10608] = {
        belong = 2,
        condition = "누적 충전=50000",
        degreetype = 2,
        desc = "누적충전 50000다이아",
        direction = 1,
        doneshow = 0,
        id = 10608,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10607,
        point = 174,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=870)"}},
        sub_direction = 6,
    },

    [10609] = {
        belong = 2,
        condition = "누적 충전=100000",
        degreetype = 2,
        desc = "누적충전 100000다이아",
        direction = 1,
        doneshow = 0,
        id = 10609,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10608,
        point = 224,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=1120)"}},
        sub_direction = 6,
    },

    [10610] = {
        belong = 2,
        condition = "누적 충전=200000",
        degreetype = 2,
        desc = "누적충전 200000다이아",
        direction = 1,
        doneshow = 1,
        id = 10610,
        maxtype = 0,
        name = "이것이 돈의 힘!",
        needdone = 10609,
        point = 317,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=1590)"}},
        sub_direction = 6,
    },

    [10702] = {
        belong = 1,
        condition = "친구 수=10",
        degreetype = 1,
        desc = "친구 10명 보유",
        direction = 1,
        doneshow = 0,
        id = 10702,
        maxtype = 0,
        name = "우정을 위해 건배!",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 7,
    },

    [10703] = {
        belong = 1,
        condition = "친구 수=20",
        degreetype = 1,
        desc = "친구 20명 보유",
        direction = 1,
        doneshow = 0,
        id = 10703,
        maxtype = 0,
        name = "우정을 위해 건배!",
        needdone = 10702,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 7,
    },

    [10704] = {
        belong = 1,
        condition = "친구 수=30",
        degreetype = 1,
        desc = "친구 30명 보유",
        direction = 1,
        doneshow = 0,
        id = 10704,
        maxtype = 0,
        name = "우정을 위해 건배!",
        needdone = 10703,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 7,
    },

    [10705] = {
        belong = 1,
        condition = "친구 수=40",
        degreetype = 1,
        desc = "친구 40명 보유",
        direction = 1,
        doneshow = 0,
        id = 10705,
        maxtype = 0,
        name = "우정을 위해 건배!",
        needdone = 10704,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 7,
    },

    [10706] = {
        belong = 1,
        condition = "친구 수=50",
        degreetype = 1,
        desc = "친구 50명 보유",
        direction = 1,
        doneshow = 1,
        id = 10706,
        maxtype = 0,
        name = "우정을 위해 건배!",
        needdone = 10705,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 7,
    },

    [10801] = {
        belong = 3,
        condition = "전설 파트너 수=1",
        degreetype = 2,
        desc = "전설 파트너1개 보유",
        direction = 1,
        doneshow = 0,
        id = 10801,
        maxtype = 0,
        name = "전설의 파트너",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 8,
    },

    [10802] = {
        belong = 3,
        condition = "전설 파트너 수=3",
        degreetype = 2,
        desc = "전설 파트너3개 보유",
        direction = 1,
        doneshow = 0,
        id = 10802,
        maxtype = 0,
        name = "전설의 파트너",
        needdone = 10801,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 8,
    },

    [10803] = {
        belong = 3,
        condition = "전설 파트너 수=5",
        degreetype = 2,
        desc = "전설 파트너5개 보유",
        direction = 1,
        doneshow = 0,
        id = 10803,
        maxtype = 0,
        name = "전설의 파트너",
        needdone = 10802,
        point = 55,
        rewarditem = {{["num"] = 2, ["sid"] = "14002"}},
        sub_direction = 8,
    },

    [10804] = {
        belong = 3,
        condition = "전설 파트너 수=9",
        degreetype = 2,
        desc = "전설 파트너9개 보유",
        direction = 1,
        doneshow = 1,
        id = 10804,
        maxtype = 0,
        name = "전설의 파트너",
        needdone = 10803,
        point = 95,
        rewarditem = {{["num"] = 20, ["sid"] = "14002"}},
        sub_direction = 8,
    },

    [10901] = {
        belong = 3,
        condition = "파트너 스킨 수=1",
        degreetype = 2,
        desc = "1종 파트너 스킨 보유",
        direction = 1,
        doneshow = 0,
        id = 10901,
        maxtype = 0,
        name = "별처럼 빛나는",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 9,
    },

    [10902] = {
        belong = 3,
        condition = "파트너 스킨 수=5",
        degreetype = 2,
        desc = "5종 파트너 스킨 보유",
        direction = 1,
        doneshow = 0,
        id = 10902,
        maxtype = 0,
        name = "별처럼 빛나는",
        needdone = 10901,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 9,
    },

    [10903] = {
        belong = 3,
        condition = "파트너 스킨 수=15",
        degreetype = 2,
        desc = "15종 파트너 스킨 보유",
        direction = 1,
        doneshow = 0,
        id = 10903,
        maxtype = 0,
        name = "별처럼 빛나는",
        needdone = 10902,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=75)"}},
        sub_direction = 9,
    },

    [10904] = {
        belong = 3,
        condition = "파트너 스킨 수=60",
        degreetype = 2,
        desc = "60종 파트너 스킨 보유",
        direction = 1,
        doneshow = 1,
        id = 10904,
        maxtype = 0,
        name = "별처럼 빛나는",
        needdone = 10903,
        point = 78,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=155)"}},
        sub_direction = 9,
    },

    [11001] = {
        belong = 3,
        condition = "어혼=20",
        degreetype = 2,
        desc = "어혼 20개 보유",
        direction = 1,
        doneshow = 0,
        id = 11001,
        maxtype = 0,
        name = "와~이것이 어령이군요",
        needdone = 0,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 10,
    },

    [11002] = {
        belong = 3,
        condition = "어혼=100",
        degreetype = 2,
        desc = "어혼 100개 보유",
        direction = 1,
        doneshow = 0,
        id = 11002,
        maxtype = 0,
        name = "와~이것이 어령이군요",
        needdone = 11001,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 10,
    },

    [11003] = {
        belong = 3,
        condition = "어혼=1000",
        degreetype = 2,
        desc = "어혼 1000개 보유",
        direction = 1,
        doneshow = 0,
        id = 11003,
        maxtype = 0,
        name = "와~이것이 어령이군요",
        needdone = 11002,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=155)"}},
        sub_direction = 10,
    },

    [11004] = {
        belong = 3,
        condition = "어혼=5000",
        degreetype = 2,
        desc = "어혼 5000개 보유",
        direction = 1,
        doneshow = 1,
        id = 11004,
        maxtype = 0,
        name = "와~이것이 어령이군요",
        needdone = 11003,
        point = 123,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=245)"}},
        sub_direction = 10,
    },

    [11101] = {
        belong = 3,
        condition = "전설급 어혼=10",
        degreetype = 2,
        desc = "전설급 어혼 10개 보유",
        direction = 1,
        doneshow = 0,
        id = 11101,
        maxtype = 0,
        name = "전설급×어혼×10개",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 11,
    },

    [11102] = {
        belong = 3,
        condition = "전설급 어혼=20",
        degreetype = 2,
        desc = "전설급 어혼 20개 보유",
        direction = 1,
        doneshow = 0,
        id = 11102,
        maxtype = 0,
        name = "전설급×어혼×20개",
        needdone = 11101,
        point = 28,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=21000)"}},
        sub_direction = 11,
    },

    [11103] = {
        belong = 3,
        condition = "전설급 어혼=50",
        degreetype = 2,
        desc = "전설급 어혼 50개 보유",
        direction = 1,
        doneshow = 0,
        id = 11103,
        maxtype = 0,
        name = "전설급×어혼×50개",
        needdone = 11102,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=29000)"}},
        sub_direction = 11,
    },

    [11104] = {
        belong = 3,
        condition = "전설급 어혼=100",
        degreetype = 2,
        desc = "전설급 어혼 100개 보유",
        direction = 1,
        doneshow = 0,
        id = 11104,
        maxtype = 0,
        name = "전설급×어혼×100개",
        needdone = 11103,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=38000)"}},
        sub_direction = 11,
    },

    [11105] = {
        belong = 3,
        condition = "전설급 어혼=500",
        degreetype = 2,
        desc = "전설급 어혼 500개 보유",
        direction = 1,
        doneshow = 0,
        id = 11105,
        maxtype = 0,
        name = "전설급×어혼×500개",
        needdone = 11104,
        point = 87,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=225)"}},
        sub_direction = 11,
    },

    [11106] = {
        belong = 3,
        condition = "전설급 어혼=1000",
        degreetype = 2,
        desc = "전설급 어혼 1000개 보유",
        direction = 1,
        doneshow = 1,
        id = 11106,
        maxtype = 0,
        name = "전설급×어혼×1000개",
        needdone = 11105,
        point = 112,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 11,
    },

    [11201] = {
        belong = 3,
        condition = "신화급 어혼=1",
        degreetype = 2,
        desc = "신화급 어혼 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 11201,
        maxtype = 0,
        name = "신화급×어혼×1개",
        needdone = 0,
        point = 19,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 12,
    },

    [11202] = {
        belong = 3,
        condition = "신화급 어혼=8",
        degreetype = 2,
        desc = "신화급 어혼 8개 보유",
        direction = 1,
        doneshow = 0,
        id = 11202,
        maxtype = 0,
        name = "신화급×어혼×8개",
        needdone = 11201,
        point = 26,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=31000)"}},
        sub_direction = 12,
    },

    [11203] = {
        belong = 3,
        condition = "신화급 어혼=30",
        degreetype = 2,
        desc = "신화급 어혼 30개 보유",
        direction = 1,
        doneshow = 0,
        id = 11203,
        maxtype = 0,
        name = "신화급×어혼×30개",
        needdone = 11202,
        point = 41,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=53000)"}},
        sub_direction = 12,
    },

    [11204] = {
        belong = 3,
        condition = "신화급 어혼=70",
        degreetype = 2,
        desc = "신화급 어혼 70개 보유",
        direction = 1,
        doneshow = 1,
        id = 11204,
        maxtype = 0,
        name = "신화급×어혼×70개",
        needdone = 11203,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=230)"}},
        sub_direction = 12,
    },

    [11205] = {
        belong = 3,
        condition = "신화급 어혼=120",
        degreetype = 2,
        desc = "신화급 어혼 120개 보유",
        direction = 1,
        doneshow = 0,
        id = 11205,
        maxtype = 0,
        name = "신화급×어혼×120개",
        needdone = 11204,
        point = 116,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=260)"}},
        sub_direction = 12,
    },

    [11206] = {
        belong = 3,
        condition = "신화급 어혼=200",
        degreetype = 2,
        desc = "신화급 어혼 200개 보유",
        direction = 1,
        doneshow = 1,
        id = 11206,
        maxtype = 0,
        name = "신화급×어혼×200개",
        needdone = 11205,
        point = 164,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 12,
    },

    [11301] = {
        belong = 3,
        condition = "15레벨 어혼=1",
        degreetype = 2,
        desc = "15레벨 어혼 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 11301,
        maxtype = 0,
        name = "와~어혼이 15레벨입니다",
        needdone = 0,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 13,
    },

    [11302] = {
        belong = 3,
        condition = "15레벨 어혼=6",
        degreetype = 2,
        desc = "15레벨 어혼 6개 보유",
        direction = 1,
        doneshow = 0,
        id = 11302,
        maxtype = 0,
        name = "와~어혼이 15레벨입니다",
        needdone = 11301,
        point = 27,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        sub_direction = 13,
    },

    [11303] = {
        belong = 3,
        condition = "15레벨 어혼=12",
        degreetype = 2,
        desc = "15레벨 어혼 12개 보유",
        direction = 1,
        doneshow = 0,
        id = 11303,
        maxtype = 0,
        name = "와~어혼이 15레벨입니다",
        needdone = 11302,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=34000)"}},
        sub_direction = 13,
    },

    [11304] = {
        belong = 3,
        condition = "15레벨 어혼=20",
        degreetype = 2,
        desc = "15레벨 어혼 20개 보유",
        direction = 1,
        doneshow = 1,
        id = 11304,
        maxtype = 0,
        name = "와~어혼이 15레벨입니다",
        needdone = 11303,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=48000)"}},
        sub_direction = 13,
    },

    [11401] = {
        belong = 3,
        condition = "룬석 강화 횟수=1",
        degreetype = 2,
        desc = "룬석 강화 1회 진행",
        direction = 1,
        doneshow = 0,
        id = 11401,
        maxtype = 0,
        name = "룬석 강화 1회!",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 14,
    },

    [11402] = {
        belong = 3,
        condition = "룬석 강화 횟수=20",
        degreetype = 2,
        desc = "룬석 강화 20회 진행",
        direction = 1,
        doneshow = 0,
        id = 11402,
        maxtype = 0,
        name = "룬석 강화 20회!",
        needdone = 11401,
        point = 43,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=32000)"}},
        sub_direction = 14,
    },

    [11403] = {
        belong = 3,
        condition = "룬석 강화 횟수=50",
        degreetype = 2,
        desc = "룬석 강화 50회 진행",
        direction = 1,
        doneshow = 0,
        id = 11403,
        maxtype = 0,
        name = "룬석 강화 50회!",
        needdone = 11402,
        point = 60,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=45000)"}},
        sub_direction = 14,
    },

    [11404] = {
        belong = 3,
        condition = "룬석 강화 횟수=200",
        degreetype = 2,
        desc = "룬석 강화 200회 진행",
        direction = 1,
        doneshow = 1,
        id = 11404,
        maxtype = 0,
        name = "룬석 강화 200회!",
        needdone = 11403,
        point = 110,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=220)"}},
        sub_direction = 14,
    },

    [11701] = {
        belong = 3,
        condition = "파트너 각성 횟수=1",
        degreetype = 2,
        desc = "파트너 각성 1회 진행됨",
        direction = 1,
        doneshow = 0,
        id = 11701,
        maxtype = 0,
        name = "폭발하라,우주야!",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 17,
    },

    [11702] = {
        belong = 3,
        condition = "파트너 각성 횟수=4",
        degreetype = 2,
        desc = "파트너 각성 4회 진행됨",
        direction = 1,
        doneshow = 1,
        id = 11702,
        maxtype = 0,
        name = "폭발하라,우주야!",
        needdone = 11701,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 17,
    },

    [11801] = {
        belong = 1,
        condition = "1레벨 이상인 길드 수=1",
        degreetype = 1,
        desc = "1레벨 길드 1개 가입",
        direction = 1,
        doneshow = 0,
        id = 11801,
        maxtype = 1,
        name = "레이븐소울 3대 길드원",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 18,
    },

    [11802] = {
        belong = 1,
        condition = "2레벨 이상인 길드 수=1",
        degreetype = 1,
        desc = "2벨 길드 1개 가입",
        direction = 1,
        doneshow = 0,
        id = 11802,
        maxtype = 1,
        name = "레이븐소울 3대 길드원",
        needdone = 11801,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 18,
    },

    [11803] = {
        belong = 1,
        condition = "3레벨 이상인 길드 수=1",
        degreetype = 1,
        desc = "3레벨 길드 1개 가입",
        direction = 1,
        doneshow = 0,
        id = 11803,
        maxtype = 1,
        name = "레이븐소울 3대 길드원",
        needdone = 11802,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=35)"}},
        sub_direction = 18,
    },

    [11804] = {
        belong = 1,
        condition = "4레벨 이상인 길드 수=1",
        degreetype = 1,
        desc = "4레벨 길드 1개 가입",
        direction = 1,
        doneshow = 0,
        id = 11804,
        maxtype = 1,
        name = "레이븐소울 3대 길드원",
        needdone = 11803,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 18,
    },

    [11805] = {
        belong = 1,
        condition = "5레벨 이상인 길드 수=1",
        degreetype = 1,
        desc = "5레벨 길드 1개 가입",
        direction = 1,
        doneshow = 1,
        id = 11805,
        maxtype = 1,
        name = "레이븐소울 3대 길드원",
        needdone = 11804,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 18,
    },

    [11901] = {
        belong = 3,
        condition = "2성 파트너 보유 수=1",
        degreetype = 2,
        desc = "2성 파트너 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 11901,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 0,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 19,
    },

    [11902] = {
        belong = 3,
        condition = "2성 파트너 보유 수=5",
        degreetype = 2,
        desc = "2성 파트너 5개 보유",
        direction = 1,
        doneshow = 0,
        id = 11902,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 11901,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 19,
    },

    [11903] = {
        belong = 3,
        condition = "2성 파트너 보유 수=20",
        degreetype = 2,
        desc = "2성 파트너 20개 보유",
        direction = 1,
        doneshow = 0,
        id = 11903,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 11902,
        point = 68,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 19,
    },

    [11904] = {
        belong = 3,
        condition = "2성 파트너 보유 수=40",
        degreetype = 2,
        desc = "2성 파트너 40개 보유",
        direction = 1,
        doneshow = 1,
        id = 11904,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 11903,
        point = 78,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 19,
    },

    [12001] = {
        belong = 3,
        condition = "3성 파트너 보유 수=1",
        degreetype = 2,
        desc = "3성 파트너 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 12001,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 0,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 20,
    },

    [12002] = {
        belong = 3,
        condition = "3성 파트너 보유 수=5",
        degreetype = 2,
        desc = "3성 파트너 5개 보유",
        direction = 1,
        doneshow = 0,
        id = 12002,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12001,
        point = 60,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 20,
    },

    [12003] = {
        belong = 3,
        condition = "3성 파트너 보유 수=20",
        degreetype = 2,
        desc = "3성 파트너 20개 보유",
        direction = 1,
        doneshow = 0,
        id = 12003,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12002,
        point = 117,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 20,
    },

    [12004] = {
        belong = 3,
        condition = "3성 파트너 보유 수=40",
        degreetype = 2,
        desc = "3성 파트너 40개 보유",
        direction = 1,
        doneshow = 1,
        id = 12004,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12003,
        point = 135,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 20,
    },

    [12101] = {
        belong = 3,
        condition = "4성 파트너 보유 수=1",
        degreetype = 2,
        desc = "4성 파트너 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 12101,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 0,
        point = 43,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 21,
    },

    [12102] = {
        belong = 3,
        condition = "4성 파트너 보유 수=5",
        degreetype = 2,
        desc = "4성 파트너 5개 보유",
        direction = 1,
        doneshow = 0,
        id = 12102,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12101,
        point = 85,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 21,
    },

    [12103] = {
        belong = 3,
        condition = "4성 파트너 보유 수=20",
        degreetype = 2,
        desc = "4성 파트너 20개 보유",
        direction = 1,
        doneshow = 0,
        id = 12103,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12102,
        point = 165,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 21,
    },

    [12104] = {
        belong = 3,
        condition = "4성 파트너 보유 수=40",
        degreetype = 2,
        desc = "4성 파트너 40개 보유",
        direction = 1,
        doneshow = 1,
        id = 12104,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12103,
        point = 190,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 21,
    },

    [12201] = {
        belong = 3,
        condition = "5성 파트너 보유 수=1",
        degreetype = 2,
        desc = "5성 파트너 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 12201,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 0,
        point = 60,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 22,
    },

    [12202] = {
        belong = 3,
        condition = "5성 파트너 보유 수=5",
        degreetype = 2,
        desc = "5성 파트너 5개 보유",
        direction = 1,
        doneshow = 0,
        id = 12202,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12201,
        point = 120,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 22,
    },

    [12203] = {
        belong = 3,
        condition = "5성 파트너 보유 수=20",
        degreetype = 2,
        desc = "5성 파트너 20개 보유",
        direction = 1,
        doneshow = 0,
        id = 12203,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12202,
        point = 233,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 22,
    },

    [12204] = {
        belong = 3,
        condition = "5성 파트너 보유 수=40",
        degreetype = 2,
        desc = "5성 파트너 40개 보유",
        direction = 1,
        doneshow = 1,
        id = 12204,
        maxtype = 0,
        name = "승성이 재밌어요~",
        needdone = 12203,
        point = 269,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 22,
    },

    [12301] = {
        belong = 2,
        condition = "신격 사용 횟수=1",
        degreetype = 2,
        desc = "신격 1회 사용됨",
        direction = 1,
        doneshow = 0,
        id = 12301,
        maxtype = 0,
        name = "신격계시록",
        needdone = 0,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 23,
    },

    [12302] = {
        belong = 2,
        condition = "신격 사용 횟수=2",
        degreetype = 2,
        desc = "신격 2회 사용됨",
        direction = 1,
        doneshow = 0,
        id = 12302,
        maxtype = 0,
        name = "신격계시록",
        needdone = 12301,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 23,
    },

    [12303] = {
        belong = 2,
        condition = "신격 사용 횟수=5",
        degreetype = 2,
        desc = "신격 5회 사용됨",
        direction = 1,
        doneshow = 0,
        id = 12303,
        maxtype = 0,
        name = "신격계시록",
        needdone = 12302,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 23,
    },

    [12304] = {
        belong = 2,
        condition = "신격 사용 횟수=10",
        degreetype = 2,
        desc = "신격 10회 사용됨",
        direction = 1,
        doneshow = 0,
        id = 12304,
        maxtype = 0,
        name = "신격계시록",
        needdone = 12303,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 23,
    },

    [12305] = {
        belong = 2,
        condition = "신격 사용 횟수=20",
        degreetype = 2,
        desc = "신격 20회 사용됨",
        direction = 1,
        doneshow = 0,
        id = 12305,
        maxtype = 0,
        name = "신격계시록",
        needdone = 12304,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 23,
    },

    [12306] = {
        belong = 2,
        condition = "신격 사용 횟수=50",
        degreetype = 2,
        desc = "신격 50회 사용됨",
        direction = 1,
        doneshow = 0,
        id = 12306,
        maxtype = 0,
        name = "신격계시록",
        needdone = 12305,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 23,
    },

    [12307] = {
        belong = 2,
        condition = "신격 사용 횟수=100",
        degreetype = 2,
        desc = "신격 100회 사용됨",
        direction = 1,
        doneshow = 0,
        id = 12307,
        maxtype = 0,
        name = "신격계시록",
        needdone = 12306,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 23,
    },

    [12308] = {
        belong = 2,
        condition = "신격 사용 횟수=200",
        degreetype = 2,
        desc = "신격 200회 사용됨",
        direction = 1,
        doneshow = 1,
        id = 12308,
        maxtype = 0,
        name = "신격계시록",
        needdone = 12307,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=90)"}},
        sub_direction = 23,
    },

    [12401] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=10",
        degreetype = 2,
        desc = "전투에서 스테이지 10회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12401,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 24,
    },

    [12402] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=20",
        degreetype = 2,
        desc = "전투에서 스테이지 20회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12402,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12401,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 24,
    },

    [12403] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=40",
        degreetype = 2,
        desc = "전투에서 스테이지 40회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12403,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12402,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 24,
    },

    [12404] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=80",
        degreetype = 2,
        desc = "전투에서 스테이지 80회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12404,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12403,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 24,
    },

    [12405] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=160",
        degreetype = 2,
        desc = "전투에서 스테이지 160회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12405,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12404,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 24,
    },

    [12406] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=300",
        degreetype = 2,
        desc = "전투에서 스테이지 300회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12406,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12405,
        point = 27,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=55)"}},
        sub_direction = 24,
    },

    [12407] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=600",
        degreetype = 2,
        desc = "전투에서 스테이지 600회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12407,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12406,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 24,
    },

    [12408] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=1000",
        degreetype = 2,
        desc = "전투에서 스테이지 1000회 클리어",
        direction = 1,
        doneshow = 0,
        id = 12408,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12407,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=90)"}},
        sub_direction = 24,
    },

    [12409] = {
        belong = 1,
        condition = "전투 스테이지 클리어 횟수=2000",
        degreetype = 2,
        desc = "전투에서 스테이지 2000회 클리어",
        direction = 1,
        doneshow = 1,
        id = 12409,
        maxtype = 0,
        name = "멈추지 않는 발걸음",
        needdone = 12408,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=140)"}},
        sub_direction = 24,
    },

    [12501] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=50",
        degreetype = 2,
        desc = "전투에서 별 5개 획득",
        direction = 1,
        doneshow = 0,
        id = 12501,
        maxtype = 0,
        name = "별의 울림",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 25,
    },

    [12502] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=100",
        degreetype = 2,
        desc = "전투에서 별 100개 획득",
        direction = 1,
        doneshow = 0,
        id = 12502,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12501,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 25,
    },

    [12503] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=150",
        degreetype = 2,
        desc = "전투에서 별 150개 획득",
        direction = 1,
        doneshow = 0,
        id = 12503,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12502,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 25,
    },

    [12504] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=200",
        degreetype = 2,
        desc = "전투에서 별 200개 획득",
        direction = 1,
        doneshow = 0,
        id = 12504,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12503,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 25,
    },

    [12505] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=400",
        degreetype = 2,
        desc = "전투에서 별 400개 획득",
        direction = 1,
        doneshow = 0,
        id = 12505,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12504,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 25,
    },

    [12506] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=600",
        degreetype = 2,
        desc = "전투에서 별 600개 획득",
        direction = 1,
        doneshow = 0,
        id = 12506,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12505,
        point = 40,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 25,
    },

    [12507] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=800",
        degreetype = 2,
        desc = "전투에서 별 800개 획득",
        direction = 1,
        doneshow = 0,
        id = 12507,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12506,
        point = 57,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=115)"}},
        sub_direction = 25,
    },

    [12508] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수==1000",
        degreetype = 2,
        desc = "전투에서 별 1000개 획득",
        direction = 1,
        doneshow = 0,
        id = 12508,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12507,
        point = 80,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=160)"}},
        sub_direction = 25,
    },

    [12509] = {
        belong = 1,
        condition = "전투에서 획득한 별 개수=1200",
        degreetype = 2,
        desc = "전투에서 별 1200개 획득",
        direction = 1,
        doneshow = 1,
        id = 12509,
        maxtype = 0,
        name = "별의 울림",
        needdone = 12508,
        point = 114,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=230)"}},
        sub_direction = 25,
    },

    [12601] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=1",
        degreetype = 2,
        desc = "전투 제1장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12601,
        maxtype = 0,
        name = "전투·제1장",
        needdone = 0,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 26,
    },

    [12602] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=2",
        degreetype = 2,
        desc = "전투 제2장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12602,
        maxtype = 0,
        name = "전투·제2장",
        needdone = 12601,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 26,
    },

    [12603] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=3",
        degreetype = 2,
        desc = "전투 제3장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12603,
        maxtype = 0,
        name = "전투·제3장",
        needdone = 12602,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 26,
    },

    [12604] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=4",
        degreetype = 2,
        desc = "전투 제4장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12604,
        maxtype = 0,
        name = "전투·제4장",
        needdone = 12603,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12605] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=5",
        degreetype = 2,
        desc = "전투 제5장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12605,
        maxtype = 0,
        name = "전투·제5장",
        needdone = 12604,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12606] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=6",
        degreetype = 2,
        desc = "전투 제6장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12606,
        maxtype = 0,
        name = "전투·제6장",
        needdone = 12605,
        point = 11,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12607] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=7",
        degreetype = 2,
        desc = "전투 제7장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12607,
        maxtype = 0,
        name = "전투·제7장",
        needdone = 12606,
        point = 12,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12608] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=8",
        degreetype = 2,
        desc = "전투 제8장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12608,
        maxtype = 0,
        name = "전투·제8장",
        needdone = 12607,
        point = 14,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12609] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=9",
        degreetype = 2,
        desc = "전투 제8장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12609,
        maxtype = 0,
        name = "전투·제9장",
        needdone = 12608,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12610] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=10",
        degreetype = 2,
        desc = "전투 제9장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12610,
        maxtype = 0,
        name = "전투·제10장",
        needdone = 12609,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12611] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=11",
        degreetype = 2,
        desc = "전투 제11장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12611,
        maxtype = 0,
        name = "전투·제11장",
        needdone = 12610,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12612] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=12",
        degreetype = 2,
        desc = "전투 제12장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12612,
        maxtype = 0,
        name = "전투·제12장",
        needdone = 12611,
        point = 19,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12613] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=13",
        degreetype = 2,
        desc = "전투 제13장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12613,
        maxtype = 0,
        name = "전투·제13장",
        needdone = 12612,
        point = 21,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12614] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=14",
        degreetype = 2,
        desc = "전투 제14장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12614,
        maxtype = 0,
        name = "전투·제14장",
        needdone = 12613,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12615] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=15",
        degreetype = 2,
        desc = "전투 제15장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12615,
        maxtype = 0,
        name = "전투·제15장",
        needdone = 12614,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12616] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=16",
        degreetype = 2,
        desc = "전투 제16장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12616,
        maxtype = 0,
        name = "전투·제16장",
        needdone = 12615,
        point = 28,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12617] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=17",
        degreetype = 2,
        desc = "전투 제17장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12617,
        maxtype = 0,
        name = "전투·제17장",
        needdone = 12616,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12618] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=18",
        degreetype = 2,
        desc = "전투 제18장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12618,
        maxtype = 0,
        name = "전투·제18장",
        needdone = 12617,
        point = 33,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12619] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=19",
        degreetype = 2,
        desc = "전투 제19장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12619,
        maxtype = 0,
        name = "전투·제19장",
        needdone = 12618,
        point = 36,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12620] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=20",
        degreetype = 2,
        desc = "전투 제20장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12620,
        maxtype = 0,
        name = "전투·제20장",
        needdone = 12619,
        point = 40,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12621] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=21",
        degreetype = 2,
        desc = "전투 제21장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12621,
        maxtype = 0,
        name = "전투·제21장",
        needdone = 12620,
        point = 43,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12622] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=22",
        degreetype = 2,
        desc = "전투 제22장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12622,
        maxtype = 0,
        name = "전투·제22장",
        needdone = 12621,
        point = 48,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12623] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=23",
        degreetype = 2,
        desc = "전투 제23장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12623,
        maxtype = 0,
        name = "전투·제23장",
        needdone = 12622,
        point = 52,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12624] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=24",
        degreetype = 2,
        desc = "전투 제24장 클리어",
        direction = 1,
        doneshow = 0,
        id = 12624,
        maxtype = 0,
        name = "전투·제24장",
        needdone = 12623,
        point = 57,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12625] = {
        belong = 1,
        condition = "클리어한 전투 챕터 수=25",
        degreetype = 2,
        desc = "전투 제25장 클리어",
        direction = 1,
        doneshow = 1,
        id = 12625,
        maxtype = 0,
        name = "전투·제25장",
        needdone = 12624,
        point = 62,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 26,
    },

    [12701] = {
        belong = 3,
        condition = "2성 룬=1",
        degreetype = 2,
        desc = "2성 룬 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 12701,
        maxtype = 0,
        name = "룬×2성×1개",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 27,
    },

    [12702] = {
        belong = 3,
        condition = "2성 룬=4",
        degreetype = 2,
        desc = "2성 룬 4개 보유",
        direction = 1,
        doneshow = 0,
        id = 12702,
        maxtype = 0,
        name = "룬×2성×4개",
        needdone = 12701,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 27,
    },

    [12703] = {
        belong = 3,
        condition = "2성 룬=10",
        degreetype = 2,
        desc = "2성 룬 10개 보유",
        direction = 1,
        doneshow = 0,
        id = 12703,
        maxtype = 0,
        name = "룬×2성×10개",
        needdone = 12702,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 27,
    },

    [12704] = {
        belong = 3,
        condition = "2성 룬=16",
        degreetype = 2,
        desc = "2성 룬 16개 보유",
        direction = 1,
        doneshow = 1,
        id = 12704,
        maxtype = 0,
        name = "룬×2성×16개",
        needdone = 12703,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 27,
    },

    [12801] = {
        belong = 3,
        condition = "3성 룬=1",
        degreetype = 2,
        desc = "3성 룬 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 12801,
        maxtype = 0,
        name = "룬×3성×1개",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 28,
    },

    [12802] = {
        belong = 3,
        condition = "3성 룬=4",
        degreetype = 2,
        desc = "3성 룬 4개 보유",
        direction = 1,
        doneshow = 0,
        id = 12802,
        maxtype = 0,
        name = "룬×2성×4개",
        needdone = 12801,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 28,
    },

    [12803] = {
        belong = 3,
        condition = "3성 룬=10",
        degreetype = 2,
        desc = "3성 룬 10개 보유",
        direction = 1,
        doneshow = 0,
        id = 12803,
        maxtype = 0,
        name = "룬×3성×10개",
        needdone = 12802,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 28,
    },

    [12804] = {
        belong = 3,
        condition = "3성 룬=16",
        degreetype = 2,
        desc = "3성 룬 16개 보유",
        direction = 1,
        doneshow = 1,
        id = 12804,
        maxtype = 0,
        name = "룬×3성×16개",
        needdone = 12803,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=26000)"}},
        sub_direction = 28,
    },

    [12901] = {
        belong = 3,
        condition = "4성 룬=1",
        degreetype = 2,
        desc = "4성 룬 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 12901,
        maxtype = 0,
        name = "룬×4성×1개",
        needdone = 0,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 29,
    },

    [12902] = {
        belong = 3,
        condition = "4성 룬=4",
        degreetype = 2,
        desc = "4성 룬 4개 보유",
        direction = 1,
        doneshow = 0,
        id = 12902,
        maxtype = 0,
        name = "룬×4성×4개",
        needdone = 12901,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 29,
    },

    [12903] = {
        belong = 3,
        condition = "4성 룬=10",
        degreetype = 2,
        desc = "4성 룬 10개 보유",
        direction = 1,
        doneshow = 0,
        id = 12903,
        maxtype = 0,
        name = "룬×4성×10개",
        needdone = 12902,
        point = 40,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 29,
    },

    [12904] = {
        belong = 3,
        condition = "4성 룬=16",
        degreetype = 2,
        desc = "4성 룬 16개 보유",
        direction = 1,
        doneshow = 1,
        id = 12904,
        maxtype = 0,
        name = "룬×4성×16개",
        needdone = 12903,
        point = 49,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 29,
    },

    [13001] = {
        belong = 3,
        condition = "5성 룬=1",
        degreetype = 2,
        desc = "5성 룬 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 13001,
        maxtype = 0,
        name = "룬×5성×1개",
        needdone = 0,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 30,
    },

    [13002] = {
        belong = 3,
        condition = "5성 룬=4",
        degreetype = 2,
        desc = "5성 룬 4개 보유",
        direction = 1,
        doneshow = 0,
        id = 13002,
        maxtype = 0,
        name = "룬×5성×4개",
        needdone = 13001,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 30,
    },

    [13003] = {
        belong = 3,
        condition = "5성 룬=10",
        degreetype = 2,
        desc = "5성 룬 10개 보유",
        direction = 1,
        doneshow = 0,
        id = 13003,
        maxtype = 0,
        name = "룬×5성×10개",
        needdone = 13002,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=130)"}},
        sub_direction = 30,
    },

    [13004] = {
        belong = 3,
        condition = "5성 룬=16",
        degreetype = 2,
        desc = "5성 룬 16개 보유",
        direction = 1,
        doneshow = 1,
        id = 13004,
        maxtype = 0,
        name = "룬×5성×16개",
        needdone = 13003,
        point = 78,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=155)"}},
        sub_direction = 30,
    },

    [13101] = {
        belong = 3,
        condition = "6성 룬=1",
        degreetype = 2,
        desc = "6성 룬 1개 보유",
        direction = 1,
        doneshow = 0,
        id = 13101,
        maxtype = 0,
        name = "룬×6성×1개",
        needdone = 0,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=90)"}},
        sub_direction = 31,
    },

    [13102] = {
        belong = 3,
        condition = "6성 룬=4",
        degreetype = 2,
        desc = "6성 룬 4개 보유",
        direction = 1,
        doneshow = 0,
        id = 13102,
        maxtype = 0,
        name = "룬×6성×4개",
        needdone = 13101,
        point = 78,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=155)"}},
        sub_direction = 31,
    },

    [13103] = {
        belong = 3,
        condition = "6성 룬=10",
        degreetype = 2,
        desc = "6성 룬 10개 보유",
        direction = 1,
        doneshow = 0,
        id = 13103,
        maxtype = 0,
        name = "룬×6성×10개",
        needdone = 13102,
        point = 90,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=180)"}},
        sub_direction = 31,
    },

    [13104] = {
        belong = 3,
        condition = "6성 룬=16",
        degreetype = 2,
        desc = "6성 룬 16개 보유",
        direction = 1,
        doneshow = 1,
        id = 13104,
        maxtype = 0,
        name = "룬×6성×16개",
        needdone = 13103,
        point = 110,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=220)"}},
        sub_direction = 31,
    },

    [20101] = {
        belong = 4,
        condition = "보물찾기 횟수=1",
        degreetype = 2,
        desc = "보물찾기 1회 진행",
        direction = 2,
        doneshow = 0,
        id = 20101,
        maxtype = 0,
        name = "누가 파간 거야!",
        needdone = 0,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=10)"}},
        sub_direction = 1,
    },

    [20102] = {
        belong = 4,
        condition = "보물찾기 횟수=10",
        degreetype = 2,
        desc = "보물찾기 10회 진행",
        direction = 2,
        doneshow = 0,
        id = 20102,
        maxtype = 0,
        name = "누가 파간 거야!",
        needdone = 20101,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=25)"}},
        sub_direction = 1,
    },

    [20103] = {
        belong = 4,
        condition = "보물찾기 횟수=20",
        degreetype = 2,
        desc = "보물찾기 20회 진행",
        direction = 2,
        doneshow = 0,
        id = 20103,
        maxtype = 0,
        name = "누가 파간 거야!",
        needdone = 20102,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=25)"}},
        sub_direction = 1,
    },

    [20104] = {
        belong = 4,
        condition = "보물찾기 횟수=50",
        degreetype = 2,
        desc = "보물찾기 50회 진행",
        direction = 2,
        doneshow = 0,
        id = 20104,
        maxtype = 0,
        name = "누가 파간 거야!",
        needdone = 20103,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [20105] = {
        belong = 4,
        condition = "보물찾기 횟수=100",
        degreetype = 2,
        desc = "보물찾기 100회 진행",
        direction = 2,
        doneshow = 0,
        id = 20105,
        maxtype = 0,
        name = "누가 파간 거야!",
        needdone = 20104,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [20106] = {
        belong = 4,
        condition = "보물찾기 횟수=200",
        degreetype = 2,
        desc = "보물찾기 200회 진행",
        direction = 2,
        doneshow = 0,
        id = 20106,
        maxtype = 0,
        name = "누가 파간 거야!",
        needdone = 20105,
        point = 41,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 1,
    },

    [20107] = {
        belong = 4,
        condition = "보물찾기 횟수=500",
        degreetype = 2,
        desc = "보물찾기 500회 진행",
        direction = 2,
        doneshow = 1,
        id = 20107,
        maxtype = 0,
        name = "누가 파간 거야!",
        needdone = 20106,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 1,
    },

    [20201] = {
        belong = 5,
        condition = "보물찾기에서 수잎비무 퀘스트 발동 횟수=1",
        degreetype = 2,
        desc = "보물찾기에서 수잎비무 기적 1회 발동",
        direction = 2,
        doneshow = 0,
        id = 20201,
        maxtype = 0,
        name = "나뭇잎 줍기~",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 2,
    },

    [20301] = {
        belong = 5,
        condition = "보물찾기에서 일권승부 퀘스트 발동 횟수=1",
        degreetype = 2,
        desc = "보물찾기에서 일권승부 기적 1회 발동",
        direction = 2,
        doneshow = 0,
        id = 20301,
        maxtype = 0,
        name = "가위바위보",
        needdone = 0,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 3,
    },

    [20401] = {
        belong = 4,
        condition = "일일 수행 횟수=60",
        degreetype = 2,
        desc = "매일 수행 60회 진행",
        direction = 2,
        doneshow = 0,
        id = 20401,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 4,
    },

    [20402] = {
        belong = 4,
        condition = "일일 수행 횟수=300",
        degreetype = 2,
        desc = "매일 수행 300회 진행",
        direction = 2,
        doneshow = 0,
        id = 20402,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 20401,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 4,
    },

    [20403] = {
        belong = 4,
        condition = "일일 수행 횟수=900",
        degreetype = 2,
        desc = "매일 수행 900회 진행",
        direction = 2,
        doneshow = 0,
        id = 20403,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 20402,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=24000)"}},
        sub_direction = 4,
    },

    [20404] = {
        belong = 4,
        condition = "일일 수행 횟수=1500",
        degreetype = 2,
        desc = "매일 수행 1500회 진행",
        direction = 2,
        doneshow = 0,
        id = 20404,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 20403,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 4,
    },

    [20405] = {
        belong = 4,
        condition = "일일 수행 횟수=3000",
        degreetype = 2,
        desc = "매일 수행 3000회 진행",
        direction = 2,
        doneshow = 0,
        id = 20405,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 20404,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 4,
    },

    [20406] = {
        belong = 4,
        condition = "일일 수행 횟수=6000",
        degreetype = 2,
        desc = "매일 수행 6000회 진행",
        direction = 2,
        doneshow = 1,
        id = 20406,
        maxtype = 0,
        name = "몸이 텅 빈 느낌",
        needdone = 20405,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=140)"}},
        sub_direction = 4,
    },

    [20501] = {
        belong = 5,
        condition = "보물 사냥꾼 처치=1",
        degreetype = 2,
        desc = "보물 사냥꾼 1회 처치",
        direction = 2,
        doneshow = 0,
        id = 20501,
        maxtype = 0,
        name = "F급 헌터",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 5,
    },

    [20502] = {
        belong = 5,
        condition = "보물 사냥꾼 처치=10",
        degreetype = 2,
        desc = "보물 사냥꾼 10회 처치",
        direction = 2,
        doneshow = 0,
        id = 20502,
        maxtype = 0,
        name = "E급 헌터",
        needdone = 20501,
        point = 22,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 5,
    },

    [20503] = {
        belong = 5,
        condition = "보물 사냥꾼 처치=20",
        degreetype = 2,
        desc = "보물 사냥꾼 20회 처치",
        direction = 2,
        doneshow = 0,
        id = 20503,
        maxtype = 0,
        name = "D급 헌터",
        needdone = 20502,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 5,
    },

    [20504] = {
        belong = 5,
        condition = "보물 사냥꾼 처치=50",
        degreetype = 2,
        desc = "보물 사냥꾼 50회 처치",
        direction = 2,
        doneshow = 1,
        id = 20504,
        maxtype = 0,
        name = "C급 헌터",
        needdone = 20503,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 5,
    },

    [20601] = {
        belong = 5,
        condition = "야외 두목 처치=1",
        degreetype = 2,
        desc = "정예몹 1회 처치",
        direction = 2,
        doneshow = 0,
        id = 20601,
        maxtype = 0,
        name = "정예몹 처치!",
        needdone = 0,
        point = 6,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        sub_direction = 6,
    },

    [20602] = {
        belong = 5,
        condition = "야외 두목 처치=10",
        degreetype = 2,
        desc = "정예몹 10회 처치",
        direction = 2,
        doneshow = 0,
        id = 20602,
        maxtype = 0,
        name = "정예몹 처치!",
        needdone = 20601,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 6,
    },

    [20603] = {
        belong = 5,
        condition = "야외 두목 처치=20",
        degreetype = 2,
        desc = "정예몹 20회 처치",
        direction = 2,
        doneshow = 0,
        id = 20603,
        maxtype = 0,
        name = "정예몹 처치!",
        needdone = 20602,
        point = 19,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 6,
    },

    [20604] = {
        belong = 5,
        condition = "야외 두목 처치=50",
        degreetype = 2,
        desc = "정예몹 50회 처치",
        direction = 2,
        doneshow = 1,
        id = 20604,
        maxtype = 0,
        name = "정예몹 처치!",
        needdone = 20603,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 6,
    },

    [20701] = {
        belong = 5,
        condition = "다과회 몬스터 처치 횟수=1",
        degreetype = 2,
        desc = "다과회 손님 1회 처치",
        direction = 2,
        doneshow = 0,
        id = 20701,
        maxtype = 0,
        name = "다과회 비밀친구",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 7,
    },

    [20702] = {
        belong = 5,
        condition = "다과회 몬스터 처치 횟수=10",
        degreetype = 2,
        desc = "다과회 손님 10회 처치",
        direction = 2,
        doneshow = 0,
        id = 20702,
        maxtype = 0,
        name = "다과회 비밀친구",
        needdone = 20701,
        point = 22,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 7,
    },

    [20703] = {
        belong = 5,
        condition = "다과회 몬스터 처치 횟수=20",
        degreetype = 2,
        desc = "다과회 손님 20회 처치",
        direction = 2,
        doneshow = 0,
        id = 20703,
        maxtype = 0,
        name = "다과회 비밀친구",
        needdone = 20702,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 7,
    },

    [20704] = {
        belong = 5,
        condition = "다과회 몬스터 처치 횟수=50",
        degreetype = 2,
        desc = "다과회 손님 50회 처치",
        direction = 2,
        doneshow = 1,
        id = 20704,
        maxtype = 0,
        name = "다과회 비밀친구",
        needdone = 20703,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 7,
    },

    [20801] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=10",
        degreetype = 1,
        desc = "지하 감옥 10층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20801,
        maxtype = 0,
        name = "어둠속",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 8,
    },

    [20802] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=20",
        degreetype = 1,
        desc = "지하 감옥 20층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20802,
        maxtype = 0,
        name = "어둠속",
        needdone = 20801,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 8,
    },

    [20803] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=30",
        degreetype = 1,
        desc = "지하 감옥 30층 통과",
        direction = 2,
        doneshow = 0,
        id = 20803,
        maxtype = 0,
        name = "어둠속",
        needdone = 20802,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 8,
    },

    [20804] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=40",
        degreetype = 1,
        desc = "지하 감옥 40층 통과",
        direction = 2,
        doneshow = 0,
        id = 20804,
        maxtype = 0,
        name = "어둠속",
        needdone = 20803,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 8,
    },

    [20805] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=50",
        degreetype = 1,
        desc = "지하 감옥 50층 통과",
        direction = 2,
        doneshow = 0,
        id = 20805,
        maxtype = 0,
        name = "어둠속",
        needdone = 20804,
        point = 27,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        sub_direction = 8,
    },

    [20806] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=60",
        degreetype = 1,
        desc = "지하 감옥 60층 통과",
        direction = 2,
        doneshow = 0,
        id = 20806,
        maxtype = 0,
        name = "어둠속",
        needdone = 20805,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=60)"}},
        sub_direction = 8,
    },

    [20807] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=70",
        degreetype = 1,
        desc = "지하 감옥 70층 통과",
        direction = 2,
        doneshow = 0,
        id = 20807,
        maxtype = 0,
        name = "어둠속",
        needdone = 20806,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=75)"}},
        sub_direction = 8,
    },

    [20808] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=80",
        degreetype = 1,
        desc = "지하 감옥 80층 통과",
        direction = 2,
        doneshow = 0,
        id = 20808,
        maxtype = 0,
        name = "어둠속",
        needdone = 20807,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 8,
    },

    [20809] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=90",
        degreetype = 1,
        desc = "지하 감옥 90층 통과",
        direction = 2,
        doneshow = 0,
        id = 20809,
        maxtype = 0,
        name = "어둠속",
        needdone = 20808,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 8,
    },

    [20810] = {
        belong = 4,
        condition = "지하 감옥 클리어 층수=100",
        degreetype = 1,
        desc = "지하 감옥 100층 통과",
        direction = 2,
        doneshow = 1,
        id = 20810,
        maxtype = 0,
        name = "어둠속",
        needdone = 20809,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=130)"}},
        sub_direction = 8,
    },

    [20901] = {
        belong = 4,
        condition = "이공유배 제2층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 2층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20901,
        maxtype = 0,
        name = "어령본×2층",
        needdone = 0,
        point = 4,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=3000)"}},
        sub_direction = 9,
    },

    [20902] = {
        belong = 4,
        condition = "이공유배 제3층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 3층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20902,
        maxtype = 0,
        name = "어령본×3층",
        needdone = 20901,
        point = 6,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        sub_direction = 9,
    },

    [20903] = {
        belong = 4,
        condition = "이공유배 제4층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 4층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20903,
        maxtype = 0,
        name = "어령본×4층",
        needdone = 20902,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 9,
    },

    [20904] = {
        belong = 4,
        condition = "이공유배 제5층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 5층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20904,
        maxtype = 0,
        name = "어령본×5층",
        needdone = 20903,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 9,
    },

    [20905] = {
        belong = 4,
        condition = "이공유배 제6층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 6층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20905,
        maxtype = 0,
        name = "어령본×6층",
        needdone = 20904,
        point = 22,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 9,
    },

    [20906] = {
        belong = 4,
        condition = "이공유배 제7층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 7층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20906,
        maxtype = 0,
        name = "어령본×7층",
        needdone = 20905,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=60)"}},
        sub_direction = 9,
    },

    [20907] = {
        belong = 4,
        condition = "이공유배 제8층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 8층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20907,
        maxtype = 0,
        name = "어령본×8층",
        needdone = 20906,
        point = 40,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 9,
    },

    [20908] = {
        belong = 4,
        condition = "이공유배 제9층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 9층 클리어",
        direction = 2,
        doneshow = 0,
        id = 20908,
        maxtype = 0,
        name = "어령본×9층",
        needdone = 20907,
        point = 53,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=105)"}},
        sub_direction = 9,
    },

    [20909] = {
        belong = 4,
        condition = "이공유배 제10층 클리어 횟수=1",
        degreetype = 1,
        desc = "어령 던전 10층 클리어",
        direction = 2,
        doneshow = 1,
        id = 20909,
        maxtype = 0,
        name = "어령본×10층",
        needdone = 20908,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=130)"}},
        sub_direction = 9,
    },

    [21001] = {
        belong = 4,
        condition = "이공유배 클리어 횟수=5",
        degreetype = 2,
        desc = "어령 던전 5회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21001,
        maxtype = 0,
        name = "어령본×5회",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 10,
    },

    [21002] = {
        belong = 4,
        condition = "이공유배 클리어 횟수=20",
        degreetype = 2,
        desc = "어령 던전 20회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21002,
        maxtype = 0,
        name = "어령본×20회",
        needdone = 21001,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 10,
    },

    [21003] = {
        belong = 4,
        condition = "이공유배 클리어 횟수=50",
        degreetype = 2,
        desc = "어령 던전 50회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21003,
        maxtype = 0,
        name = "어령본×50회",
        needdone = 21002,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 10,
    },

    [21004] = {
        belong = 4,
        condition = "이공유배 클리어 횟수=100",
        degreetype = 2,
        desc = "어령 던전 100회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21004,
        maxtype = 0,
        name = "어령본×100회",
        needdone = 21003,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 10,
    },

    [21005] = {
        belong = 4,
        condition = "이공유배 클리어 횟수=200",
        degreetype = 2,
        desc = "어령 던전 200회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21005,
        maxtype = 0,
        name = "어령본×200회",
        needdone = 21004,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 10,
    },

    [21006] = {
        belong = 4,
        condition = "이공유배 클리어 횟수=500",
        degreetype = 2,
        desc = "어령 던전 500회 클리어",
        direction = 2,
        doneshow = 1,
        id = 21006,
        maxtype = 0,
        name = "어령본×500회",
        needdone = 21005,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 10,
    },

    [21101] = {
        belong = 4,
        condition = "매골지 클리어 횟수=5",
        degreetype = 2,
        desc = "장비 던전 5회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21101,
        maxtype = 0,
        name = "장비던전×5회",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 11,
    },

    [21102] = {
        belong = 4,
        condition = "매골지 클리어 횟수=20",
        degreetype = 2,
        desc = "장비 던전 20회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21102,
        maxtype = 0,
        name = "장비던전×20회",
        needdone = 21101,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 11,
    },

    [21103] = {
        belong = 4,
        condition = "매골지 클리어 횟수=50",
        degreetype = 2,
        desc = "장비 던전 50회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21103,
        maxtype = 0,
        name = "장비던전×50회",
        needdone = 21102,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 11,
    },

    [21104] = {
        belong = 4,
        condition = "매골지 클리어 횟수=100",
        degreetype = 2,
        desc = "장비 던전 100회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21104,
        maxtype = 0,
        name = "장비던전×100회",
        needdone = 21103,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 11,
    },

    [21105] = {
        belong = 4,
        condition = "매골지 클리어 횟수=200",
        degreetype = 2,
        desc = "장비 던전 200회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21105,
        maxtype = 0,
        name = "장비던전×200회",
        needdone = 21104,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 11,
    },

    [21106] = {
        belong = 4,
        condition = "매골지 클리어 횟수=500",
        degreetype = 2,
        desc = "장비 던전 500회 클리어",
        direction = 2,
        doneshow = 1,
        id = 21106,
        maxtype = 0,
        name = "장비던전×500회",
        needdone = 21105,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 11,
    },

    [21201] = {
        belong = 4,
        condition = "길드 제1층 보스 처치 횟수=1",
        degreetype = 1,
        desc = "길드 제1차 현상금 보스 처치",
        direction = 2,
        doneshow = 0,
        id = 21201,
        maxtype = 0,
        name = "자태가 양호하네",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 12,
    },

    [21202] = {
        belong = 4,
        condition = "길드 제2층 보스 처치 횟수=1",
        degreetype = 1,
        desc = "길드 제2차 현상금 보스 처치",
        direction = 2,
        doneshow = 0,
        id = 21202,
        maxtype = 0,
        name = "자태가 양호하네",
        needdone = 21201,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 12,
    },

    [21203] = {
        belong = 4,
        condition = "길드 제3층 보스 처치 횟수=1",
        degreetype = 1,
        desc = "길드 제3차 현상금 보스 처치",
        direction = 2,
        doneshow = 0,
        id = 21203,
        maxtype = 0,
        name = "자태가 양호하네",
        needdone = 21202,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 12,
    },

    [21204] = {
        belong = 4,
        condition = "길드 제4층 보스 처치 횟수=1",
        degreetype = 1,
        desc = "길드 제4차 현상금 보스 처치",
        direction = 2,
        doneshow = 1,
        id = 21204,
        maxtype = 0,
        name = "자태가 양호하네",
        needdone = 21203,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=29000)"}},
        sub_direction = 12,
    },

    [21301] = {
        belong = 4,
        condition = "월견에서 제2차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제2차 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21301,
        maxtype = 0,
        name = "월견·신월",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=30)"}},
        sub_direction = 13,
    },

    [21302] = {
        belong = 4,
        condition = "월견에서 제3차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제3차 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21302,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21301,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=35)"}},
        sub_direction = 13,
    },

    [21303] = {
        belong = 4,
        condition = "월견에서 제4차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제4차 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21303,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21302,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 13,
    },

    [21304] = {
        belong = 4,
        condition = "월견에서 제5차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제5치 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21304,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21303,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 13,
    },

    [21305] = {
        belong = 4,
        condition = "월견에서 제6차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제6차 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21305,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21304,
        point = 27,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=55)"}},
        sub_direction = 13,
    },

    [21306] = {
        belong = 4,
        condition = "월견에서 제7차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제7차 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21306,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21305,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=60)"}},
        sub_direction = 13,
    },

    [21307] = {
        belong = 4,
        condition = "월견에서 제8차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제8차 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21307,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21305,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=75)"}},
        sub_direction = 13,
    },

    [21308] = {
        belong = 4,
        condition = "월견에서 제9차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제9차 몬스터 처치",
        direction = 2,
        doneshow = 0,
        id = 21308,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21307,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 13,
    },

    [21309] = {
        belong = 4,
        condition = "월견에서 제10차 몬스터 처치=1",
        degreetype = 1,
        desc = "월견에서 제10차 몬스터 처치",
        direction = 2,
        doneshow = 1,
        id = 21309,
        maxtype = 0,
        name = "월견·신월",
        needdone = 21308,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 13,
    },

    [21401] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=1",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 1회 양보",
        direction = 2,
        doneshow = 0,
        id = 21401,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 0,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 14,
    },

    [21402] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=2",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 2회 양보",
        direction = 2,
        doneshow = 0,
        id = 21402,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21401,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 14,
    },

    [21403] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=5",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 5회 양보",
        direction = 2,
        doneshow = 0,
        id = 21403,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21402,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 14,
    },

    [21404] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=10",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 10회 양보",
        direction = 2,
        doneshow = 0,
        id = 21404,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21403,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 14,
    },

    [21405] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=20",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 20회 양보",
        direction = 2,
        doneshow = 0,
        id = 21405,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21404,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=11000)"}},
        sub_direction = 14,
    },

    [21406] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=40",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 40회 양보",
        direction = 2,
        doneshow = 0,
        id = 21406,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21405,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 14,
    },

    [21407] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=80",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 80회 양보",
        direction = 2,
        doneshow = 0,
        id = 21407,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21406,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=22000)"}},
        sub_direction = 14,
    },

    [21408] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=150",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 150회 양보",
        direction = 2,
        doneshow = 0,
        id = 21408,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21407,
        point = 38,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=75)"}},
        sub_direction = 14,
    },

    [21409] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=300",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 300회 양보",
        direction = 2,
        doneshow = 0,
        id = 21409,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21408,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 14,
    },

    [21410] = {
        belong = 4,
        condition = "다른 유저에게 소원내용 양보=500",
        degreetype = 2,
        desc = "다른 유저에게 소원 내용 500회 양보",
        direction = 2,
        doneshow = 1,
        id = 21410,
        maxtype = 0,
        name = "아름답지 않아?",
        needdone = 21409,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=130)"}},
        sub_direction = 14,
    },

    [21501] = {
        belong = 4,
        condition = "악몽 던전 클리어 횟수=1",
        degreetype = 2,
        desc = "악몽 사냥 던전 최초 클리어",
        direction = 2,
        doneshow = 0,
        id = 21501,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=20)"}},
        sub_direction = 15,
    },

    [21502] = {
        belong = 4,
        condition = "악몽 던전 클리어 횟수=5",
        degreetype = 2,
        desc = "악몽 사냥 던전 5회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21502,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 21501,
        point = 19,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 15,
    },

    [21503] = {
        belong = 4,
        condition = "악몽 던전 클리어 횟수=10",
        degreetype = 2,
        desc = "악몽 사냥 던전 10회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21503,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 21502,
        point = 21,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 15,
    },

    [21504] = {
        belong = 4,
        condition = "악몽 던전 클리어 횟수=20",
        degreetype = 2,
        desc = "악몽 사냥 던전 20회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21504,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 21503,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 15,
    },

    [21505] = {
        belong = 4,
        condition = "악몽 던전 클리어 횟수=50",
        degreetype = 2,
        desc = "악몽 사냥 던전 50회 클리어",
        direction = 2,
        doneshow = 0,
        id = 21505,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 21504,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 15,
    },

    [21506] = {
        belong = 4,
        condition = "악몽 던전 클리어 횟수=100",
        degreetype = 2,
        desc = "악몽 사냥 던전 100회 클리어",
        direction = 2,
        doneshow = 1,
        id = 21506,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 21505,
        point = 65,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 15,
    },

    [21601] = {
        belong = 4,
        condition = "악몽 사냥에서 모든 몬스터 소멸 횟수=1",
        degreetype = 2,
        desc = "악몽 사냥에서 모든 종류의 몬스터 처치",
        direction = 2,
        doneshow = 1,
        id = 21601,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 0,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 16,
    },

    [21701] = {
        belong = 4,
        condition = "악몽 소멸 라운드 10회 이하=1",
        degreetype = 1,
        desc = "10라운드 내 임의로 1개 악몽 소멸",
        direction = 2,
        doneshow = 1,
        id = 21701,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 0,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 17,
    },

    [21801] = {
        belong = 4,
        condition = "악몽 사냥 정산 시 랭킹 10회 미만=1",
        degreetype = 1,
        desc = "악몽 사냥에서 상위 10위 보상 획득",
        direction = 2,
        doneshow = 1,
        id = 21801,
        maxtype = 0,
        name = "악몽의 형상",
        needdone = 0,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 18,
    },

    [21901] = {
        belong = 4,
        condition = "원정 정상적 완료 횟수=5",
        degreetype = 2,
        desc = "원정 5회 정상 완료",
        direction = 2,
        doneshow = 0,
        id = 21901,
        maxtype = 0,
        name = "원정대",
        needdone = 0,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 19,
    },

    [21902] = {
        belong = 4,
        condition = "원정 정상적 완료 횟수=10",
        degreetype = 2,
        desc = "원정 10회 정상 완료",
        direction = 2,
        doneshow = 0,
        id = 21902,
        maxtype = 0,
        name = "원정대",
        needdone = 21901,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 19,
    },

    [21903] = {
        belong = 4,
        condition = "원정 정상적 완료 횟수=20",
        degreetype = 2,
        desc = "원정 20회 정상 완료",
        direction = 2,
        doneshow = 0,
        id = 21903,
        maxtype = 0,
        name = "원정대",
        needdone = 21902,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 19,
    },

    [21904] = {
        belong = 4,
        condition = "원정 정상적 완료 횟수=50",
        degreetype = 2,
        desc = "원정 50회 정상 완료",
        direction = 2,
        doneshow = 0,
        id = 21904,
        maxtype = 0,
        name = "원정대",
        needdone = 21903,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=29000)"}},
        sub_direction = 19,
    },

    [21905] = {
        belong = 4,
        condition = "원정 정상적 완료 횟수=100",
        degreetype = 2,
        desc = "원정 100회 정상 완료",
        direction = 2,
        doneshow = 0,
        id = 21905,
        maxtype = 0,
        name = "원정대",
        needdone = 21904,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=38000)"}},
        sub_direction = 19,
    },

    [21906] = {
        belong = 4,
        condition = "원정 정상적 완료 횟수=200",
        degreetype = 2,
        desc = "원정 200회 정상 완료",
        direction = 2,
        doneshow = 1,
        id = 21906,
        maxtype = 0,
        name = "원정대",
        needdone = 21905,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=53000)"}},
        sub_direction = 19,
    },

    [22001] = {
        belong = 5,
        condition = "원정에서 우연한 소통 횟수=10",
        degreetype = 2,
        desc = "기이한 플레이 원정 10회 진행",
        direction = 2,
        doneshow = 0,
        id = 22001,
        maxtype = 0,
        name = "돌발!돌발!",
        needdone = 0,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 20,
    },

    [22002] = {
        belong = 5,
        condition = "원정에서 우연한 소통 횟수=20",
        degreetype = 2,
        desc = "기이한 플레이 원정 20회 진행",
        direction = 2,
        doneshow = 0,
        id = 22002,
        maxtype = 0,
        name = "돌발!돌발!",
        needdone = 22001,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 20,
    },

    [22003] = {
        belong = 5,
        condition = "원정에서 우연한 소통 횟수=40",
        degreetype = 2,
        desc = "기이한 플레이 원정 40회 진행",
        direction = 2,
        doneshow = 0,
        id = 22003,
        maxtype = 0,
        name = "돌발!돌발!",
        needdone = 22002,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 20,
    },

    [22004] = {
        belong = 5,
        condition = "원정에서 우연한 소통 횟수=100",
        degreetype = 2,
        desc = "기이한 플레이 원정 100회 진행",
        direction = 2,
        doneshow = 0,
        id = 22004,
        maxtype = 0,
        name = "돌발!돌발!",
        needdone = 22003,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=29000)"}},
        sub_direction = 20,
    },

    [22005] = {
        belong = 5,
        condition = "원정에서 우연한 소통 횟수=200",
        degreetype = 2,
        desc = "기이한 플레이 원정 200회 진행",
        direction = 2,
        doneshow = 0,
        id = 22005,
        maxtype = 0,
        name = "돌발!돌발!",
        needdone = 22004,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=38000)"}},
        sub_direction = 20,
    },

    [22006] = {
        belong = 5,
        condition = "원정에서 우연한 소통 횟수=400",
        degreetype = 2,
        desc = "기이한 플레이 원정 400회 진행",
        direction = 2,
        doneshow = 1,
        id = 22006,
        maxtype = 0,
        name = "돌발!돌발!",
        needdone = 22005,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=53000)"}},
        sub_direction = 20,
    },

    [22101] = {
        belong = 4,
        condition = "판다 할아버지 처치 횟수=2",
        degreetype = 2,
        desc = "암살·판다 할아버지 2회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22101,
        maxtype = 0,
        name = "암살·판다할범",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 21,
    },

    [22102] = {
        belong = 4,
        condition = "판다 할아버지 처치 횟수=5",
        degreetype = 2,
        desc = "암살·판다 할아버지 5회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22102,
        maxtype = 0,
        name = "암살·판다할범",
        needdone = 22101,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 21,
    },

    [22103] = {
        belong = 4,
        condition = "판다 할아버지 처치 횟수=10",
        degreetype = 2,
        desc = "암살·판다 할아버지 10회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22103,
        maxtype = 0,
        name = "암살·판다할범",
        needdone = 22102,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 21,
    },

    [22104] = {
        belong = 4,
        condition = "판다 할아버지 처치 횟수=20",
        degreetype = 2,
        desc = "암살·판다 할아버지 20회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22104,
        maxtype = 0,
        name = "암살·판다할범",
        needdone = 22103,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 21,
    },

    [22105] = {
        belong = 4,
        condition = "판다 할아버지 처치 횟수=40",
        degreetype = 2,
        desc = "암살·판다 할아버지 40회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 1,
        id = 22105,
        maxtype = 0,
        name = "암살·판다할범",
        needdone = 22104,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 21,
    },

    [22201] = {
        belong = 4,
        condition = "추무 처치 횟수=2",
        degreetype = 2,
        desc = "암살·추무 2회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22201,
        maxtype = 0,
        name = "암살·추무",
        needdone = 0,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 22,
    },

    [22202] = {
        belong = 4,
        condition = "추무 처치 횟수=5",
        degreetype = 2,
        desc = "암살·추무 5회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22202,
        maxtype = 0,
        name = "암살·추무",
        needdone = 22201,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 22,
    },

    [22203] = {
        belong = 4,
        condition = "추무 처치 횟수=10",
        degreetype = 2,
        desc = "암살·추무 10회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22203,
        maxtype = 0,
        name = "암살·추무",
        needdone = 22202,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 22,
    },

    [22204] = {
        belong = 4,
        condition = "추무 처치 횟수=20",
        degreetype = 2,
        desc = "암살·추무 20회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22204,
        maxtype = 0,
        name = "암살·추무",
        needdone = 22203,
        point = 36,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 22,
    },

    [22205] = {
        belong = 4,
        condition = "추무 처치 횟수=40",
        degreetype = 2,
        desc = "암살·추무 40회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 1,
        id = 22205,
        maxtype = 0,
        name = "암살·추무",
        needdone = 22204,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 22,
    },

    [22301] = {
        belong = 4,
        condition = "비익 처치 횟수=2",
        degreetype = 2,
        desc = "암살·비익 2회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22301,
        maxtype = 0,
        name = "암살·비익",
        needdone = 0,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 23,
    },

    [22302] = {
        belong = 4,
        condition = "비익 처치 횟수=5",
        degreetype = 2,
        desc = "암살·비익 5회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22302,
        maxtype = 0,
        name = "암살·비익",
        needdone = 22301,
        point = 28,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=21000)"}},
        sub_direction = 23,
    },

    [22303] = {
        belong = 4,
        condition = "비익 처치 횟수=10",
        degreetype = 2,
        desc = "암살·비익 10회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22303,
        maxtype = 0,
        name = "암살·비익",
        needdone = 22302,
        point = 36,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=27000)"}},
        sub_direction = 23,
    },

    [22304] = {
        belong = 4,
        condition = "비익 처치 횟수=20",
        degreetype = 2,
        desc = "암살·비익 20회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 0,
        id = 22304,
        maxtype = 0,
        name = "암살·비익",
        needdone = 22303,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 23,
    },

    [22305] = {
        belong = 4,
        condition = "비익 처치 횟수=40",
        degreetype = 2,
        desc = "암살·비익 40회(몬스터 사망 시 유저 현장에 존재)",
        direction = 2,
        doneshow = 1,
        id = 22305,
        maxtype = 0,
        name = "암살·비익",
        needdone = 22304,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=140)"}},
        sub_direction = 23,
    },

    [22401] = {
        belong = 4,
        condition = "인간의 형상 토벌에서 보물 상자 주운 횟수=2",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 보물 상자 줍기 누적 2회",
        direction = 2,
        doneshow = 0,
        id = 22401,
        maxtype = 0,
        name = "인형·보물",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 24,
    },

    [22402] = {
        belong = 4,
        condition = "인간의 형상 토벌에서 보물 상자 주운 횟수=10",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 보물 상자 줍기 누적 10회",
        direction = 2,
        doneshow = 0,
        id = 22402,
        maxtype = 0,
        name = "인형·보물",
        needdone = 22401,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 24,
    },

    [22403] = {
        belong = 4,
        condition = "인간의 형상 토벌에서 보물 상자 주운 횟수=25",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 보물 상자 줍기 누적 25회",
        direction = 2,
        doneshow = 0,
        id = 22403,
        maxtype = 0,
        name = "인형·보물",
        needdone = 22402,
        point = 28,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=21000)"}},
        sub_direction = 24,
    },

    [22404] = {
        belong = 4,
        condition = "인간의 형상 토벌에서 보물 상자 주운 횟수=50",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 보물 상자 줍기 누적 50회",
        direction = 2,
        doneshow = 0,
        id = 22404,
        maxtype = 0,
        name = "인형·보물",
        needdone = 22403,
        point = 36,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 24,
    },

    [22405] = {
        belong = 4,
        condition = "인간의 형상 토벌에서 보물 상자 주운 횟수=100",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 보물 상자 줍기 누적 100회",
        direction = 2,
        doneshow = 1,
        id = 22405,
        maxtype = 0,
        name = "인형·보물",
        needdone = 22404,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 24,
    },

    [22501] = {
        belong = 4,
        condition = "인간의 형상 토벌 유저 처치 누적 수=2",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 2명 유저",
        direction = 2,
        doneshow = 0,
        id = 22501,
        maxtype = 0,
        name = "인형토벌",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 25,
    },

    [22502] = {
        belong = 4,
        condition = "인간의 형상 토벌 유저 처치 누적 수=5",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 5명 유저",
        direction = 2,
        doneshow = 0,
        id = 22502,
        maxtype = 0,
        name = "인형토벌",
        needdone = 22501,
        point = 9,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=7000)"}},
        sub_direction = 25,
    },

    [22503] = {
        belong = 4,
        condition = "인간의 형상 토벌 유저 처치 누적 수=10",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 10명 유저",
        direction = 2,
        doneshow = 0,
        id = 22503,
        maxtype = 0,
        name = "인형토벌",
        needdone = 22502,
        point = 12,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=9000)"}},
        sub_direction = 25,
    },

    [22504] = {
        belong = 4,
        condition = "인간의 형상 토벌 유저 처치 누적 수=30",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 30명 유저",
        direction = 2,
        doneshow = 0,
        id = 22504,
        maxtype = 0,
        name = "인형토벌",
        needdone = 22503,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 25,
    },

    [22505] = {
        belong = 4,
        condition = "인간의 형상 토벌 유저 처치 누적 수=80",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 80명 유저",
        direction = 2,
        doneshow = 0,
        id = 22505,
        maxtype = 0,
        name = "인형토벌",
        needdone = 22504,
        point = 36,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 25,
    },

    [22506] = {
        belong = 4,
        condition = "인간의 형상 토벌 유저 처치 누적 수=200",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 200명 유저",
        direction = 2,
        doneshow = 0,
        id = 22506,
        maxtype = 0,
        name = "인형토벌",
        needdone = 22505,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 25,
    },

    [22507] = {
        belong = 4,
        condition = "인간의 형상 토벌 유저 처치 누적 수=400",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 400명 유저",
        direction = 2,
        doneshow = 1,
        id = 22507,
        maxtype = 0,
        name = "인형토벌",
        needdone = 22506,
        point = 71,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=140)"}},
        sub_direction = 25,
    },

    [22601] = {
        belong = 4,
        condition = "인간의 형상 토벌 누적 패배 횟수=2",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 2명 유저",
        direction = 2,
        doneshow = 0,
        id = 22601,
        maxtype = 0,
        name = "다시 한 번 때려봐",
        needdone = 0,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 26,
    },

    [22602] = {
        belong = 4,
        condition = "인간의 형상 토벌 누적 패배 횟수=5",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 5회 당함",
        direction = 2,
        doneshow = 0,
        id = 22602,
        maxtype = 0,
        name = "다시 한 번 때려봐",
        needdone = 22601,
        point = 7,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        sub_direction = 26,
    },

    [22603] = {
        belong = 4,
        condition = "인간의 형상 토벌 누적 패배 횟수=10",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 10회 당함",
        direction = 2,
        doneshow = 0,
        id = 22603,
        maxtype = 0,
        name = "다시 한 번 때려봐",
        needdone = 22602,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 26,
    },

    [22604] = {
        belong = 4,
        condition = "인간의 형상 토벌 누적 패배 횟수=30",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 30회 당함",
        direction = 2,
        doneshow = 0,
        id = 22604,
        maxtype = 0,
        name = "다시 한 번 때려봐",
        needdone = 22603,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 26,
    },

    [22605] = {
        belong = 4,
        condition = "인간의 형상 토벌 누적 패배 횟수=80",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 80회 당함",
        direction = 2,
        doneshow = 0,
        id = 22605,
        maxtype = 0,
        name = "다시 한 번 때려봐",
        needdone = 22604,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 26,
    },

    [22606] = {
        belong = 4,
        condition = "인간의 형상 토벌 누적 패배 횟수=200",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 200회 당함",
        direction = 2,
        doneshow = 0,
        id = 22606,
        maxtype = 0,
        name = "다시 한 번 때려봐",
        needdone = 22605,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 26,
    },

    [22607] = {
        belong = 4,
        condition = "인간의 형상 토벌 누적 패배 횟수=400",
        degreetype = 2,
        desc = "인간의 형상 토벌에서 누적 처치 400회 당함",
        direction = 2,
        doneshow = 1,
        id = 22607,
        maxtype = 0,
        name = "다시 한 번 때려봐",
        needdone = 22606,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 26,
    },

    [22701] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=2",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 2회",
        direction = 2,
        doneshow = 0,
        id = 22701,
        maxtype = 0,
        name = "호송·호송원",
        needdone = 0,
        point = 9,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=7000)"}},
        sub_direction = 27,
    },

    [22702] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=5",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 5회",
        direction = 2,
        doneshow = 0,
        id = 22702,
        maxtype = 0,
        name = "호송·G레벨",
        needdone = 22701,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 27,
    },

    [22703] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=10",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 10회",
        direction = 2,
        doneshow = 0,
        id = 22703,
        maxtype = 0,
        name = "호송·F레벨",
        needdone = 22702,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 27,
    },

    [22704] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=30",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 30회",
        direction = 2,
        doneshow = 0,
        id = 22704,
        maxtype = 0,
        name = "호송·E레벨",
        needdone = 22703,
        point = 26,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        sub_direction = 27,
    },

    [22705] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=60",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 60회",
        direction = 2,
        doneshow = 0,
        id = 22705,
        maxtype = 0,
        name = "호송·D레벨",
        needdone = 22704,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=24000)"}},
        sub_direction = 27,
    },

    [22706] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=100",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 100회",
        direction = 2,
        doneshow = 0,
        id = 22706,
        maxtype = 0,
        name = "호송·C레벨",
        needdone = 22705,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=28000)"}},
        sub_direction = 27,
    },

    [22707] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=200",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 200회",
        direction = 2,
        doneshow = 0,
        id = 22707,
        maxtype = 0,
        name = "호송·B레벨",
        needdone = 22706,
        point = 58,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=44000)"}},
        sub_direction = 27,
    },

    [22708] = {
        belong = 4,
        condition = "제두 택배의 퀘스트 성공적 완료 횟수=450",
        degreetype = 2,
        desc = "제두 택배의 퀘스트 성공적 완료 450회",
        direction = 2,
        doneshow = 1,
        id = 22708,
        maxtype = 0,
        name = "호송·A레벨",
        needdone = 22707,
        point = 92,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=69000)"}},
        sub_direction = 27,
    },

    [22801] = {
        belong = 4,
        condition = "제두 택배 새로고침 횟수=2",
        degreetype = 2,
        desc = "제두 택배 새로고침 2회 진행됨",
        direction = 2,
        doneshow = 0,
        id = 22801,
        maxtype = 0,
        name = "치카치카치카치카",
        needdone = 0,
        point = 9,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=7000)"}},
        sub_direction = 28,
    },

    [22802] = {
        belong = 4,
        condition = "제두 택배 새로고침 횟수=5",
        degreetype = 2,
        desc = "제두 택배 새로고침 5회 진행됨",
        direction = 2,
        doneshow = 0,
        id = 22802,
        maxtype = 0,
        name = "치카치카치카치카",
        needdone = 22801,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 28,
    },

    [22803] = {
        belong = 4,
        condition = "제두 택배 새로고침 횟수=10",
        degreetype = 2,
        desc = "제두 택배 새로고침 10회 진행됨",
        direction = 2,
        doneshow = 0,
        id = 22803,
        maxtype = 0,
        name = "치카치카치카치카",
        needdone = 22802,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 28,
    },

    [22804] = {
        belong = 4,
        condition = "제두 택배 새로고침 횟수=20",
        degreetype = 2,
        desc = "제두 택배 새로고침 20회 진행됨",
        direction = 2,
        doneshow = 0,
        id = 22804,
        maxtype = 0,
        name = "치카치카치카치카",
        needdone = 22803,
        point = 19,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 28,
    },

    [22805] = {
        belong = 4,
        condition = "제두 택배 새로고침 횟수=40",
        degreetype = 2,
        desc = "제두 택배 새로고침 40회 진행됨",
        direction = 2,
        doneshow = 0,
        id = 22805,
        maxtype = 0,
        name = "치카치카치카치카",
        needdone = 22804,
        point = 26,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=20000)"}},
        sub_direction = 28,
    },

    [22806] = {
        belong = 4,
        condition = "제두 택배 새로고침 횟수=80",
        degreetype = 2,
        desc = "제두 택배 새로고침 80회 진행됨",
        direction = 2,
        doneshow = 1,
        id = 22806,
        maxtype = 0,
        name = "치카치카치카치카",
        needdone = 22805,
        point = 37,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=75)"}},
        sub_direction = 28,
    },

    [22901] = {
        belong = 4,
        condition = "꼴지의 역습 참여 횟수=1",
        degreetype = 2,
        desc = "꼴지의 역습 이벤트 1회 참여",
        direction = 2,
        doneshow = 0,
        id = 22901,
        maxtype = 0,
        name = "내 지능는 겨우 10입니다",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 29,
    },

    [22902] = {
        belong = 4,
        condition = "꼴지의 역습 참여 횟수=2",
        degreetype = 2,
        desc = "꼴지의 역습 이벤트 2회 참여",
        direction = 2,
        doneshow = 0,
        id = 22902,
        maxtype = 0,
        name = "내 지능는 겨우 20입니다",
        needdone = 22901,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 29,
    },

    [22903] = {
        belong = 4,
        condition = "꼴지의 역습 참여 횟수=5",
        degreetype = 2,
        desc = "꼴지의 역습 이벤트 5회 참여",
        direction = 2,
        doneshow = 0,
        id = 22903,
        maxtype = 0,
        name = "내 지능는 겨우 50입니다",
        needdone = 22902,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 29,
    },

    [22904] = {
        belong = 4,
        condition = "꼴지의 역습 참여 횟수=10",
        degreetype = 2,
        desc = "꼴지의 역습 이벤트 10회 참여",
        direction = 2,
        doneshow = 0,
        id = 22904,
        maxtype = 0,
        name = "내 지능는 겨우 100입니다",
        needdone = 22903,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 29,
    },

    [22905] = {
        belong = 4,
        condition = "꼴지의 역습 참여 횟수=20",
        degreetype = 2,
        desc = "꼴지의 역습 이벤트 20회 참여",
        direction = 2,
        doneshow = 0,
        id = 22905,
        maxtype = 0,
        name = "내 지능는 겨우 200입니다",
        needdone = 22904,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 29,
    },

    [22906] = {
        belong = 4,
        condition = "꼴지의 역습 참여 횟수=50",
        degreetype = 2,
        desc = "꼴지의 역습 이벤트 50회 참여",
        direction = 2,
        doneshow = 1,
        id = 22906,
        maxtype = 0,
        name = "내 지능는 겨우 500입니다",
        needdone = 22905,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 29,
    },

    [23001] = {
        belong = 4,
        condition = "모범생 어디가 참여 횟수=1",
        degreetype = 2,
        desc = "모범생 어디가 이벤트 1회 참여",
        direction = 2,
        doneshow = 0,
        id = 23001,
        maxtype = 0,
        name = "모범생의 클립",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 30,
    },

    [23002] = {
        belong = 4,
        condition = "모범생 어디가 참여 횟수=2",
        degreetype = 2,
        desc = "모범생 어디가 이벤트 2회 참여",
        direction = 2,
        doneshow = 0,
        id = 23002,
        maxtype = 0,
        name = "모범생의 지우개",
        needdone = 23001,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=6000)"}},
        sub_direction = 30,
    },

    [23003] = {
        belong = 4,
        condition = "모범생 어디가 참여 횟수=5",
        degreetype = 2,
        desc = "모범생 어디가 이벤트 5회 참여",
        direction = 2,
        doneshow = 0,
        id = 23003,
        maxtype = 0,
        name = "모범생의 삼각자",
        needdone = 23002,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 30,
    },

    [23004] = {
        belong = 4,
        condition = "모범생 어디가 참여 횟수=10",
        degreetype = 2,
        desc = "모범생 어디가 이벤트에 10회 참여",
        direction = 2,
        doneshow = 0,
        id = 23004,
        maxtype = 0,
        name = "모범생의 볼펜",
        needdone = 23003,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 30,
    },

    [23005] = {
        belong = 4,
        condition = "모범생 어디가 참여 횟수=20",
        degreetype = 2,
        desc = "모범생 어디가 이벤트 20회 참여",
        direction = 2,
        doneshow = 0,
        id = 23005,
        maxtype = 0,
        name = "모범생의 숙제노트",
        needdone = 23004,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=17000)"}},
        sub_direction = 30,
    },

    [23006] = {
        belong = 4,
        condition = "모범생 어디가 참여 횟수=50",
        degreetype = 2,
        desc = "모범생 어디가 이벤트 50회 참여",
        direction = 2,
        doneshow = 1,
        id = 23006,
        maxtype = 0,
        name = "모범생의 필기노트",
        needdone = 23005,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=80)"}},
        sub_direction = 30,
    },

    [23101] = {
        belong = 4,
        condition = "환령 횟수=30",
        degreetype = 2,
        desc = "환령 30회 진행",
        direction = 2,
        doneshow = 0,
        id = 23101,
        maxtype = 0,
        name = "정령 사냥을 위해",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 31,
    },

    [23102] = {
        belong = 4,
        condition = "환령 횟수=500",
        degreetype = 2,
        desc = "환령 500회 진행",
        direction = 2,
        doneshow = 0,
        id = 23102,
        maxtype = 0,
        name = "정령 사냥을 위해",
        needdone = 23101,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 31,
    },

    [23103] = {
        belong = 4,
        condition = "환령 횟수=2000",
        degreetype = 2,
        desc = "환령 2000회 진행",
        direction = 2,
        doneshow = 0,
        id = 23103,
        maxtype = 0,
        name = "정령 사냥을 위해",
        needdone = 23102,
        point = 58,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=115)"}},
        sub_direction = 31,
    },

    [23104] = {
        belong = 4,
        condition = "환령 횟수=6000",
        degreetype = 2,
        desc = "환령 6000회 진행",
        direction = 2,
        doneshow = 1,
        id = 23104,
        maxtype = 0,
        name = "정령 사냥을 위해",
        needdone = 23103,
        point = 92,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 31,
    },

    [23201] = {
        belong = 4,
        condition = "까마귀 활성화 횟수=1",
        degreetype = 2,
        desc = "까마귀 1회 모집 성공",
        direction = 2,
        doneshow = 0,
        id = 23201,
        maxtype = 0,
        name = "레이븐소울",
        needdone = 0,
        point = 7,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=5000)"}},
        sub_direction = 32,
    },

    [23202] = {
        belong = 4,
        condition = "까마귀 활성화 횟수=20",
        degreetype = 2,
        desc = "까마귀 20회 모집 성공",
        direction = 2,
        doneshow = 0,
        id = 23202,
        maxtype = 0,
        name = "레이븐소울",
        needdone = 23201,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 32,
    },

    [23203] = {
        belong = 4,
        condition = "까마귀 활성화 횟수=100",
        degreetype = 2,
        desc = "까마귀 100회 모집 성공",
        direction = 2,
        doneshow = 0,
        id = 23203,
        maxtype = 0,
        name = "레이븐소울",
        needdone = 23202,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=90)"}},
        sub_direction = 32,
    },

    [23204] = {
        belong = 4,
        condition = "까마귀 활성화 횟수=500",
        degreetype = 2,
        desc = "까마귀 500회 모집 성공",
        direction = 2,
        doneshow = 1,
        id = 23204,
        maxtype = 0,
        name = "레이븐소울",
        needdone = 23203,
        point = 110,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=220)"}},
        sub_direction = 32,
    },

    [23301] = {
        belong = 4,
        condition = "추무 활성화 횟수=1",
        degreetype = 2,
        desc = "추무 직접 모집 1회(무료 또는 수정 소모)",
        direction = 2,
        doneshow = 0,
        id = 23301,
        maxtype = 0,
        name = "만남 가면 쓴 남자",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 33,
    },

    [23302] = {
        belong = 4,
        condition = "추무 활성화 횟수=10",
        degreetype = 2,
        desc = "추무 직접 모집 10회(무료 또는 수정 소모)",
        direction = 2,
        doneshow = 0,
        id = 23302,
        maxtype = 0,
        name = "만남 가면 쓴 남자",
        needdone = 23301,
        point = 21,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=16000)"}},
        sub_direction = 33,
    },

    [23303] = {
        belong = 4,
        condition = "추무 활성화 횟수=80",
        degreetype = 2,
        desc = "추무 직접 모집 80회(무료 또는 수정 소모)",
        direction = 2,
        doneshow = 0,
        id = 23303,
        maxtype = 0,
        name = "만남 가면 쓴 남자",
        needdone = 23302,
        point = 58,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=44000)"}},
        sub_direction = 33,
    },

    [23304] = {
        belong = 4,
        condition = "추무 활성화 횟수=300",
        degreetype = 2,
        desc = "추무 직접 모집 300회(무료 또는 수정 소모)",
        direction = 2,
        doneshow = 1,
        id = 23304,
        maxtype = 0,
        name = "만남 가면 쓴 남자",
        needdone = 23303,
        point = 112,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=84000)"}},
        sub_direction = 33,
    },

    [23401] = {
        belong = 4,
        condition = "환령에서 획득한 전설 어령 수=1",
        degreetype = 2,
        desc = "환령에서 전설 어령 1개 획득",
        direction = 2,
        doneshow = 0,
        id = 23401,
        maxtype = 0,
        name = "정령 사냥×1개×전설 어령",
        needdone = 0,
        point = 6,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 34,
    },

    [23402] = {
        belong = 4,
        condition = "환령에서 획득한 전설 어령 수=2",
        degreetype = 2,
        desc = "환령에서 전설 어령 2개 획득",
        direction = 2,
        doneshow = 0,
        id = 23402,
        maxtype = 0,
        name = "정령 사냥×2개×전설 어령",
        needdone = 23401,
        point = 6,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 34,
    },

    [23403] = {
        belong = 4,
        condition = "환령에서 획득한 전설 어령 수=5",
        degreetype = 2,
        desc = "환령에서 전설 어령 5개 획득",
        direction = 2,
        doneshow = 0,
        id = 23403,
        maxtype = 0,
        name = "정령 사냥×5개×전설 어령",
        needdone = 23402,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 34,
    },

    [23404] = {
        belong = 4,
        condition = "환령에서 획득한 전설 어령 수=10",
        degreetype = 2,
        desc = "환령에서 전설 어령 10개 획득",
        direction = 2,
        doneshow = 0,
        id = 23404,
        maxtype = 0,
        name = "정령 사냥×10개×전설 어령",
        needdone = 23403,
        point = 12,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 34,
    },

    [23405] = {
        belong = 4,
        condition = "환령에서 획득한 전설 어령 수=20",
        degreetype = 2,
        desc = "환령에서 전설 어령 20개 획득",
        direction = 2,
        doneshow = 0,
        id = 23405,
        maxtype = 0,
        name = "정령 사냥×20개×전설 어령",
        needdone = 23404,
        point = 17,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 34,
    },

    [23406] = {
        belong = 4,
        condition = "환령에서 획득한 전설 어령 수=50",
        degreetype = 2,
        desc = "환령에서 전설 어령 50개 획득",
        direction = 2,
        doneshow = 0,
        id = 23406,
        maxtype = 0,
        name = "정령 사냥×50개×전설 어령",
        needdone = 23405,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 34,
    },

    [23407] = {
        belong = 4,
        condition = "환령에서 획득한 전설 어령 수=200",
        degreetype = 2,
        desc = "환령에서 전설 어령 200개 획득",
        direction = 2,
        doneshow = 1,
        id = 23407,
        maxtype = 0,
        name = "정령 사냥×200개×전설 어령",
        needdone = 23406,
        point = 76,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 34,
    },

    [23501] = {
        belong = 4,
        condition = "영혼 보물 상자 오픈 성공 횟수=1",
        degreetype = 2,
        desc = "영혼 보물 상자 1개 오픈 성공",
        direction = 2,
        doneshow = 0,
        id = 23501,
        maxtype = 0,
        name = "하늘에서 내려온 보물",
        needdone = 0,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 35,
    },

    [23502] = {
        belong = 4,
        condition = "영혼 보물 상자 오픈 성공 횟수=30",
        degreetype = 2,
        desc = "영혼 보물 상자 30개 오픈 성공",
        direction = 2,
        doneshow = 0,
        id = 23502,
        maxtype = 0,
        name = "하늘에서 내려온 보물",
        needdone = 23501,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=15000)"}},
        sub_direction = 35,
    },

    [23503] = {
        belong = 4,
        condition = "영혼 보물 상자 오픈 성공 횟수=200",
        degreetype = 2,
        desc = "영혼 보물 상자 200개 오픈 성공",
        direction = 2,
        doneshow = 0,
        id = 23503,
        maxtype = 0,
        name = "하늘에서 내려온 보물",
        needdone = 23502,
        point = 43,
        rewarditem = {{["num"] = 2, ["sid"] = "10040"}},
        sub_direction = 35,
    },

    [23504] = {
        belong = 4,
        condition = "영혼 보물 상자 오픈 성공 횟수=800",
        degreetype = 2,
        desc = "영혼 보물 상자 800개 오픈 성공",
        direction = 2,
        doneshow = 1,
        id = 23504,
        maxtype = 0,
        name = "하늘에서 내려온 보물",
        needdone = 23503,
        point = 86,
        rewarditem = {{["num"] = 10, ["sid"] = "10040"}},
        sub_direction = 35,
    },

    [23601] = {
        belong = 4,
        condition = "파트너 모집 횟수=5",
        degreetype = 2,
        desc = "파트너 모집 5회 진행",
        direction = 2,
        doneshow = 0,
        id = 23601,
        maxtype = 0,
        name = "우리와 함께 해요!",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=30000)"}},
        sub_direction = 36,
    },

    [23602] = {
        belong = 4,
        condition = "파트너 모집 횟수=10",
        degreetype = 2,
        desc = "파트너 모집 10회 진행",
        direction = 2,
        doneshow = 0,
        id = 23602,
        maxtype = 0,
        name = "우리와 함께 해요!",
        needdone = 23601,
        point = 27,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 36,
    },

    [23603] = {
        belong = 4,
        condition = "파트너 모집 횟수=15",
        degreetype = 2,
        desc = "파트너 모집 15회 진행",
        direction = 2,
        doneshow = 0,
        id = 23603,
        maxtype = 0,
        name = "우리와 함께 해요!",
        needdone = 23602,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 36,
    },

    [23604] = {
        belong = 4,
        condition = "파트너 모집 횟수=21",
        degreetype = 2,
        desc = "파트너 모집 21회 진행",
        direction = 2,
        doneshow = 1,
        id = 23604,
        maxtype = 0,
        name = "우리와 함께 해요!",
        needdone = 23603,
        point = 78,
        rewarditem = {{["num"] = 3, ["sid"] = "14002"}},
        sub_direction = 36,
    },

    [30101] = {
        belong = 6,
        condition = "무술 대련장 포인트=1200",
        degreetype = 1,
        desc = "티어 무술 대련장 1200포인트 도달",
        direction = 3,
        doneshow = 0,
        id = 30101,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=20)"}},
        sub_direction = 1,
    },

    [30102] = {
        belong = 6,
        condition = "무술 대련장 포인트=1400",
        degreetype = 1,
        desc = "티어 무술 대련장 1400포인트 도달",
        direction = 3,
        doneshow = 0,
        id = 30102,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30101,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=20)"}},
        sub_direction = 1,
    },

    [30103] = {
        belong = 6,
        condition = "무술 대련장 포인트=1600",
        degreetype = 1,
        desc = "티어 무술 대련장 1600포인트 도달",
        direction = 3,
        doneshow = 0,
        id = 30103,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30102,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=30)"}},
        sub_direction = 1,
    },

    [30104] = {
        belong = 6,
        condition = "무술 대련장 포인트=1800",
        degreetype = 1,
        desc = "티어 무술 대련장 1800포인트 도달",
        direction = 3,
        doneshow = 0,
        id = 30104,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30103,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=35)"}},
        sub_direction = 1,
    },

    [30105] = {
        belong = 6,
        condition = "무술 대련장 포인트=2000",
        degreetype = 1,
        desc = "티어 무술 대련장 2000포인트 도달",
        direction = 3,
        doneshow = 0,
        id = 30105,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30104,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 1,
    },

    [30106] = {
        belong = 6,
        condition = "무술 대련장 포인트=2200",
        degreetype = 1,
        desc = "티어 무술 대련장 2200포인트 도달",
        direction = 3,
        doneshow = 0,
        id = 30106,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30105,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [30107] = {
        belong = 6,
        condition = "무술 대련장 포인트=2400",
        degreetype = 1,
        desc = "티어 무술 대련장 2400포인트 도달",
        direction = 3,
        doneshow = 1,
        id = 30107,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30106,
        point = 39,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [30201] = {
        belong = 6,
        condition = "무술 대련장 연승 횟수=1",
        degreetype = 2,
        desc = "티어 무술 대련장 1연승",
        direction = 3,
        doneshow = 0,
        id = 30201,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=15)"}},
        sub_direction = 2,
    },

    [30202] = {
        belong = 6,
        condition = "무술 대련장 연승 횟수=3",
        degreetype = 2,
        desc = "티어 무술 대련장 3연승",
        direction = 3,
        doneshow = 0,
        id = 30202,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30201,
        point = 13,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=25)"}},
        sub_direction = 2,
    },

    [30203] = {
        belong = 6,
        condition = "무술 대련장 연승 횟수=5",
        degreetype = 2,
        desc = "티어 무술 대련장 5연승",
        direction = 3,
        doneshow = 0,
        id = 30203,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30202,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 2,
    },

    [30204] = {
        belong = 6,
        condition = "무술 대련장 연승 횟수=10",
        degreetype = 2,
        desc = "티어 무술 대련장 10연승",
        direction = 3,
        doneshow = 1,
        id = 30204,
        maxtype = 0,
        name = "티어무장·무적",
        needdone = 30203,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 2,
    },

    [30301] = {
        belong = 6,
        condition = "공평 무술 대련장 연승 횟수=5",
        degreetype = 2,
        desc = "공평 무술 대련장 5연승",
        direction = 3,
        doneshow = 0,
        id = 30301,
        maxtype = 0,
        name = "공평전투·무적",
        needdone = 0,
        point = 23,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=45)"}},
        sub_direction = 3,
    },

    [30302] = {
        belong = 6,
        condition = "공평 무술 대련장 연승 횟수=10",
        degreetype = 2,
        desc = "공평 무술 대련장 10연승",
        direction = 3,
        doneshow = 0,
        id = 30302,
        maxtype = 0,
        name = "공평전투·무적",
        needdone = 30301,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 3,
    },

    [30303] = {
        belong = 6,
        condition = "공평 무술 대련장 연승 횟수=20",
        degreetype = 2,
        desc = "공평 무술 대련장 20연승",
        direction = 3,
        doneshow = 1,
        id = 30303,
        maxtype = 0,
        name = "공평전투·무적",
        needdone = 30302,
        point = 50,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 3,
    },

    [30401] = {
        belong = 6,
        condition = "무술 대련장 승리 수=5",
        degreetype = 2,
        desc = "티어 무술 대련장 누적 5승",
        direction = 3,
        doneshow = 0,
        id = 30401,
        maxtype = 0,
        name = "티어무장·토벌",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 4,
    },

    [30402] = {
        belong = 6,
        condition = "무술 대련장 승리 수=20",
        degreetype = 2,
        desc = "티어 무술 대련장 누적 20승",
        direction = 3,
        doneshow = 0,
        id = 30402,
        maxtype = 0,
        name = "티어무장·토벌",
        needdone = 30401,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 4,
    },

    [30403] = {
        belong = 6,
        condition = "무술 대련장 승리 수=50",
        degreetype = 2,
        desc = "티어 무술 대련장 누적 50승",
        direction = 3,
        doneshow = 0,
        id = 30403,
        maxtype = 0,
        name = "티어무장·토벌",
        needdone = 30402,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 4,
    },

    [30404] = {
        belong = 6,
        condition = "무술 대련장 승리 수=100",
        degreetype = 2,
        desc = "티어 무술 대련장 누적 100승",
        direction = 3,
        doneshow = 0,
        id = 30404,
        maxtype = 0,
        name = "티어무장·토벌",
        needdone = 30403,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 4,
    },

    [30405] = {
        belong = 6,
        condition = "무술 대련장 승리 수=200",
        degreetype = 2,
        desc = "티어 무술 대련장 누적 200승",
        direction = 3,
        doneshow = 0,
        id = 30405,
        maxtype = 0,
        name = "티어무장·토벌",
        needdone = 30404,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=90)"}},
        sub_direction = 4,
    },

    [30406] = {
        belong = 6,
        condition = "무술 대련장 승리 수=500",
        degreetype = 2,
        desc = "티어 무술 대련장 누적 500승",
        direction = 3,
        doneshow = 1,
        id = 30406,
        maxtype = 0,
        name = "티어무장·토벌",
        needdone = 30405,
        point = 78,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=155)"}},
        sub_direction = 4,
    },

    [30501] = {
        belong = 6,
        condition = "공평 무술 대련장 승리 수=5",
        degreetype = 2,
        desc = "공평 무술 대련장 누적 5승",
        direction = 3,
        doneshow = 0,
        id = 30501,
        maxtype = 0,
        name = "공평전투·토벌",
        needdone = 0,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 4,
    },

    [30502] = {
        belong = 6,
        condition = "공평 무술 대련장 승리 수=20",
        degreetype = 2,
        desc = "공평 무술 대련장 누적 20승",
        direction = 3,
        doneshow = 0,
        id = 30502,
        maxtype = 0,
        name = "공평전투·토벌",
        needdone = 30501,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 4,
    },

    [30503] = {
        belong = 6,
        condition = "공평 무술 대련장 승리 수=50",
        degreetype = 2,
        desc = "공평 무술 대련장 누적 50승",
        direction = 3,
        doneshow = 0,
        id = 30503,
        maxtype = 0,
        name = "공평전투·토벌",
        needdone = 30502,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 4,
    },

    [30504] = {
        belong = 6,
        condition = "공평 무술 대련장 승리 수=100",
        degreetype = 2,
        desc = "공평 무술 대련장 누적 100승",
        direction = 3,
        doneshow = 0,
        id = 30504,
        maxtype = 0,
        name = "공평전투·토벌",
        needdone = 30503,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 4,
    },

    [30505] = {
        belong = 6,
        condition = "공평 무술 대련장 승리 수=200",
        degreetype = 2,
        desc = "공평 무술 대련장 누적 200승",
        direction = 3,
        doneshow = 0,
        id = 30505,
        maxtype = 0,
        name = "공평전투·토벌",
        needdone = 30504,
        point = 45,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=90)"}},
        sub_direction = 4,
    },

    [30506] = {
        belong = 6,
        condition = "공평 무술 대련장 승리 수=500",
        degreetype = 2,
        desc = "공평 무술 대련장 누적 500승",
        direction = 3,
        doneshow = 1,
        id = 30506,
        maxtype = 0,
        name = "공평전투·토벌",
        needdone = 30505,
        point = 78,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=155)"}},
        sub_direction = 4,
    },

    [30601] = {
        belong = 6,
        condition = "협동 무술 대련장 승리 수=1",
        degreetype = 2,
        desc = "협동 무술 대련장 누적 1승",
        direction = 3,
        doneshow = 0,
        id = 30601,
        maxtype = 0,
        name = "협동전투·토벌",
        needdone = 0,
        point = 5,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=4000)"}},
        sub_direction = 6,
    },

    [30602] = {
        belong = 6,
        condition = "협동 무술 대련장 승리 수=5",
        degreetype = 2,
        desc = "협동 무술 대련장 누적 5승",
        direction = 3,
        doneshow = 0,
        id = 30602,
        maxtype = 0,
        name = "협동전투·토벌",
        needdone = 30601,
        point = 10,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=8000)"}},
        sub_direction = 6,
    },

    [30603] = {
        belong = 6,
        condition = "협동 무술 대련장 승리 수=15",
        degreetype = 2,
        desc = "협동 무술 대련장 누적 15승",
        direction = 3,
        doneshow = 0,
        id = 30603,
        maxtype = 0,
        name = "협동전투·토벌",
        needdone = 30602,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=12000)"}},
        sub_direction = 6,
    },

    [30604] = {
        belong = 6,
        condition = "협동 무술 대련장 승리 수=30",
        degreetype = 2,
        desc = "협동 무술 대련장 누적 30승",
        direction = 3,
        doneshow = 0,
        id = 30604,
        maxtype = 0,
        name = "협동전투·토벌",
        needdone = 30603,
        point = 20,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=40)"}},
        sub_direction = 6,
    },

    [30605] = {
        belong = 6,
        condition = "협동 무술 대련장 승리 수=60",
        degreetype = 2,
        desc = "협동 무술 대련장 누적 60승",
        direction = 3,
        doneshow = 0,
        id = 30605,
        maxtype = 0,
        name = "협동전투·토벌",
        needdone = 30604,
        point = 28,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=55)"}},
        sub_direction = 6,
    },

    [30606] = {
        belong = 6,
        condition = "협동 무술 대련장 승리 수=100",
        degreetype = 2,
        desc = "협동 무술 대련장 누적 100승",
        direction = 3,
        doneshow = 1,
        id = 30606,
        maxtype = 0,
        name = "협동전투·토벌",
        needdone = 30605,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 6,
    },

    [30801] = {
        belong = 6,
        condition = "영지전 참여 횟수=1",
        degreetype = 2,
        desc = "영지전 1회 참여",
        direction = 3,
        doneshow = 0,
        id = 30801,
        maxtype = 0,
        name = "짜리한 영지전",
        needdone = 0,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 8,
    },

    [30802] = {
        belong = 6,
        condition = "영지전 참여 횟수=2",
        degreetype = 2,
        desc = "영지전 2회 참여",
        direction = 3,
        doneshow = 0,
        id = 30802,
        maxtype = 0,
        name = "짜리한 영지전",
        needdone = 30801,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 8,
    },

    [30803] = {
        belong = 6,
        condition = "영지전 참여 횟수=5",
        degreetype = 2,
        desc = "영지전 5회 참여",
        direction = 3,
        doneshow = 0,
        id = 30803,
        maxtype = 0,
        name = "짜리한 영지전",
        needdone = 30802,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=23000)"}},
        sub_direction = 8,
    },

    [30804] = {
        belong = 6,
        condition = "영지전 참여 횟수=8",
        degreetype = 2,
        desc = "영지전 8회 참여",
        direction = 3,
        doneshow = 0,
        id = 30804,
        maxtype = 0,
        name = "짜리한 영지전",
        needdone = 30803,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=23000)"}},
        sub_direction = 8,
    },

    [30805] = {
        belong = 6,
        condition = "영지전 참여 횟수=12",
        degreetype = 2,
        desc = "영지전 12회 참여",
        direction = 3,
        doneshow = 0,
        id = 30805,
        maxtype = 0,
        name = "짜리한 영지전",
        needdone = 30804,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 8,
    },

    [30806] = {
        belong = 6,
        condition = "영지전 참여 횟수=20",
        degreetype = 2,
        desc = "영지전 20회 참여",
        direction = 3,
        doneshow = 0,
        id = 30806,
        maxtype = 0,
        name = "짜리한 영지전",
        needdone = 30805,
        point = 49,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 8,
    },

    [30807] = {
        belong = 6,
        condition = "영지전 참여 횟수=30",
        degreetype = 2,
        desc = "영지전 30회 참여",
        direction = 3,
        doneshow = 1,
        id = 30807,
        maxtype = 0,
        name = "짜리한 영지전",
        needdone = 30806,
        point = 55,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=110)"}},
        sub_direction = 8,
    },

    [30901] = {
        belong = 6,
        condition = "무관 도전 횟수=5",
        degreetype = 2,
        desc = "무관 도전 5회 진행",
        direction = 3,
        doneshow = 0,
        id = 30901,
        maxtype = 0,
        name = "무관도전·토벌",
        needdone = 0,
        point = 9,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=7000)"}},
        sub_direction = 9,
    },

    [30902] = {
        belong = 6,
        condition = "무관 도전 횟수=20",
        degreetype = 2,
        desc = "무관 도전 20회 진행",
        direction = 3,
        doneshow = 0,
        id = 30902,
        maxtype = 0,
        name = "무관도전·토벌",
        needdone = 30901,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=19000)"}},
        sub_direction = 9,
    },

    [30903] = {
        belong = 6,
        condition = "무관 도전 횟수=100",
        degreetype = 2,
        desc = "무관 도전 100회 진행",
        direction = 3,
        doneshow = 0,
        id = 30903,
        maxtype = 0,
        name = "무관도전·토벌",
        needdone = 30902,
        point = 32,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=65)"}},
        sub_direction = 9,
    },

    [30904] = {
        belong = 6,
        condition = "무관 도전 횟수=500",
        degreetype = 2,
        desc = "무관 도전 500회 진행",
        direction = 3,
        doneshow = 0,
        id = 30904,
        maxtype = 0,
        name = "무관도전·토벌",
        needdone = 30903,
        point = 78,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=155)"}},
        sub_direction = 9,
    },

    [31001] = {
        belong = 6,
        condition = "청동관 관주로 승격 횟수=1",
        degreetype = 1,
        desc = "청동관 관주로 승격",
        direction = 3,
        doneshow = 0,
        id = 31001,
        maxtype = 0,
        name = "청동관주",
        needdone = 0,
        point = 15,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=50)"}},
        sub_direction = 10,
    },

    [31101] = {
        belong = 6,
        condition = "실버관 관주로 승격 횟수=1",
        degreetype = 1,
        desc = "실버관 관주로 승격",
        direction = 3,
        doneshow = 0,
        id = 31101,
        maxtype = 0,
        name = "실버관주",
        needdone = 0,
        point = 25,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 11,
    },

    [31201] = {
        belong = 6,
        condition = "골드관 관주로 승격 횟수=1",
        degreetype = 1,
        desc = "골드관 관주로 승격",
        direction = 3,
        doneshow = 0,
        id = 31201,
        maxtype = 0,
        name = "골드관주",
        needdone = 0,
        point = 43,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=150)"}},
        sub_direction = 12,
    },

    [31301] = {
        belong = 6,
        condition = "다이아관 관주로 승격 횟수=1",
        degreetype = 1,
        desc = "다이아관 관주로 승격",
        direction = 3,
        doneshow = 0,
        id = 31301,
        maxtype = 0,
        name = "수정관주",
        needdone = 0,
        point = 74,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=200)"}},
        sub_direction = 13,
    },

    [31401] = {
        belong = 6,
        condition = "마스터관 관주로 승격 횟수=1",
        degreetype = 1,
        desc = "마스터관 관주로 승격",
        direction = 3,
        doneshow = 0,
        id = 31401,
        maxtype = 0,
        name = "제왕관주",
        needdone = 0,
        point = 127,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=300)"}},
        sub_direction = 14,
    },

    [31501] = {
        belong = 6,
        condition = "길드전 참여 횟수=1",
        degreetype = 2,
        desc = "길드전 1회 참여",
        direction = 3,
        doneshow = 0,
        id = 31501,
        maxtype = 0,
        name = "길드전!",
        needdone = 0,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 15,
    },

    [31502] = {
        belong = 6,
        condition = "길드전 참여 횟수=5",
        degreetype = 2,
        desc = "길드전 5회 참여",
        direction = 3,
        doneshow = 0,
        id = 31502,
        maxtype = 0,
        name = "길드전!",
        needdone = 31501,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=23000)"}},
        sub_direction = 15,
    },

    [31503] = {
        belong = 6,
        condition = "길드전 참여 횟수=10",
        degreetype = 2,
        desc = "길드전 10회 참여",
        direction = 3,
        doneshow = 0,
        id = 31503,
        maxtype = 0,
        name = "길드전!",
        needdone = 31502,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 15,
    },

    [31504] = {
        belong = 6,
        condition = "길드전 참여 횟수=20",
        degreetype = 2,
        desc = "길드전 20회 참여",
        direction = 3,
        doneshow = 1,
        id = 31504,
        maxtype = 0,
        name = "길드전!",
        needdone = 31503,
        point = 49,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 15,
    },

    [31601] = {
        belong = 6,
        condition = "길드전 상대 파티 처치 횟수=5",
        degreetype = 2,
        desc = "길드전 상대 파티 5개 처치",
        direction = 3,
        doneshow = 0,
        id = 31601,
        maxtype = 0,
        name = "운명의 길드전!",
        needdone = 0,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=14000)"}},
        sub_direction = 16,
    },

    [31602] = {
        belong = 6,
        condition = "길드전 상대 파티 처치 횟수=25",
        degreetype = 2,
        desc = "길드전 상대 파티 25개 처치",
        direction = 3,
        doneshow = 0,
        id = 31602,
        maxtype = 0,
        name = "운명의 길드전!",
        needdone = 31601,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=23000)"}},
        sub_direction = 16,
    },

    [31603] = {
        belong = 6,
        condition = "길드전 상대 파티 처치 횟수=50",
        degreetype = 2,
        desc = "길드전 상대 파티 50개 처치",
        direction = 3,
        doneshow = 0,
        id = 31603,
        maxtype = 0,
        name = "운명의 길드전!",
        needdone = 31602,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=70)"}},
        sub_direction = 16,
    },

    [31604] = {
        belong = 6,
        condition = "길드전 상대 파티 처치 횟수=100",
        degreetype = 2,
        desc = "길드전 상대 파티 100개 처치",
        direction = 3,
        doneshow = 1,
        id = 31604,
        maxtype = 0,
        name = "운명의 길드전!",
        needdone = 31603,
        point = 49,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 16,
    },

    [31701] = {
        belong = 6,
        condition = "길드전 승리 횟수=1",
        degreetype = 2,
        desc = "길드전 1회 승리",
        direction = 3,
        doneshow = 0,
        id = 31701,
        maxtype = 0,
        name = "전설의 길드전!",
        needdone = 0,
        point = 18,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=25000)"}},
        sub_direction = 17,
    },

    [31702] = {
        belong = 6,
        condition = "길드전 승리 횟수=5",
        degreetype = 2,
        desc = "길드전 5회 승리",
        direction = 3,
        doneshow = 0,
        id = 31702,
        maxtype = 0,
        name = "전설의 길드전!",
        needdone = 31701,
        point = 30,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=40000)"}},
        sub_direction = 17,
    },

    [31703] = {
        belong = 6,
        condition = "길드전 승리 횟수=10",
        degreetype = 2,
        desc = "길드전 10회 승리",
        direction = 3,
        doneshow = 0,
        id = 31703,
        maxtype = 0,
        name = "전설의 길드전!",
        needdone = 31702,
        point = 35,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=100)"}},
        sub_direction = 17,
    },

    [31704] = {
        belong = 6,
        condition = "길드전 승리 횟수=20",
        degreetype = 2,
        desc = "길드전 20회 승리",
        direction = 3,
        doneshow = 1,
        id = 31704,
        maxtype = 0,
        name = "전설의 길드전!",
        needdone = 31703,
        point = 49,
        rewarditem = {{["num"] = 1, ["sid"] = "1003(value=150)"}},
        sub_direction = 17,
    },

    [40301] = {
        belong = 8,
        condition = "파트너 해제 수=1",
        degreetype = 2,
        desc = "파트너 1개 해제",
        direction = 4,
        doneshow = 0,
        id = 40301,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 0,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "1002(value=10000)"}},
        sub_direction = 1,
    },

    [40302] = {
        belong = 8,
        condition = "파트너 해제 수=3",
        degreetype = 2,
        desc = "파트너 3개 해제",
        direction = 4,
        doneshow = 0,
        id = 40302,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 40301,
        point = 8,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [40303] = {
        belong = 8,
        condition = "파트너 해제 수=6",
        degreetype = 2,
        desc = "파트너 6개 해제",
        direction = 4,
        doneshow = 0,
        id = 40303,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 40302,
        point = 16,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [40304] = {
        belong = 8,
        condition = "파트너 해제 수=9",
        degreetype = 2,
        desc = "파트너 9개 해제",
        direction = 4,
        doneshow = 0,
        id = 40304,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 40303,
        point = 22,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [40305] = {
        belong = 8,
        condition = "파트너 해제 수=15",
        degreetype = 2,
        desc = "파트너 15개 해제",
        direction = 4,
        doneshow = 0,
        id = 40305,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 40304,
        point = 29,
        rewarditem = {{["num"] = 1, ["sid"] = "10040"}},
        sub_direction = 1,
    },

    [40306] = {
        belong = 8,
        condition = "파트너 해제 수=20",
        degreetype = 2,
        desc = "파트너 20개 해제",
        direction = 4,
        doneshow = 0,
        id = 40306,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 40305,
        point = 40,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 1,
    },

    [40307] = {
        belong = 8,
        condition = "파트너 해제 수=30",
        degreetype = 2,
        desc = "파트너 30개 해제",
        direction = 4,
        doneshow = 0,
        id = 40307,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 40306,
        point = 53,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 1,
    },

    [40308] = {
        belong = 8,
        condition = "파트너 해제 수=40",
        degreetype = 2,
        desc = "파트너 40개 해제",
        direction = 4,
        doneshow = 0,
        id = 40308,
        maxtype = 0,
        name = "운명의 반짝임을 느끼며!",
        needdone = 40307,
        point = 64,
        rewarditem = {{["num"] = 1, ["sid"] = "14002"}},
        sub_direction = 1,
    },

}
