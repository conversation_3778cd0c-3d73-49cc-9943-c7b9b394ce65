-- ./excel/item/partner_travel.xlsx
return {

    [27501] = {
        add_time = 10800,
        bag_show_type = 4,
        bat_use = 0,
        buy_price = 0,
        circulation_type = 0,
        coin_rate = 0,
        compose_item = {},
        description = "파트너 경험치+3%",
        exp_rate = 300,
        gain_way_id = {168},
        gift_price = 0,
        giftable = 0,
        icon = 27501,
        id = 27501,
        introduction = "월견도의 열주는 3시간 안에 원정하는 파트너의 경험치를 증가한다",
        max_grade = 200,
        max_overlay = 99,
        min_grade = 0,
        name = "월견 경험치 열주",
        quality = 2,
        quickable = 0,
        sale_price = 1000,
        shape = 1003,
        stallable = 0,
        taskid = 0,
        type = 10,
        use_type = "travel_item",
    },

    [27502] = {
        add_time = 10800,
        bag_show_type = 4,
        bat_use = 0,
        buy_price = 0,
        circulation_type = 0,
        coin_rate = 300,
        compose_item = {},
        description = "골드 수익+3%",
        exp_rate = 0,
        gain_way_id = {168},
        gift_price = 0,
        giftable = 0,
        icon = 27502,
        id = 27502,
        introduction = "월견도의 통조림은 3시간 안에 원정하는 파트너의 골드 수익을 증가한다",
        max_grade = 200,
        max_overlay = 99,
        min_grade = 0,
        name = "삼월도 통조림",
        quality = 2,
        quickable = 0,
        sale_price = 1000,
        shape = 1003,
        stallable = 0,
        taskid = 0,
        type = 10,
        use_type = "travel_item",
    },

    [27503] = {
        add_time = 21600,
        bag_show_type = 4,
        bat_use = 0,
        buy_price = 0,
        circulation_type = 0,
        coin_rate = 0,
        compose_item = {},
        description = "파트너 경험치+6%",
        exp_rate = 600,
        gain_way_id = {168},
        gift_price = 0,
        giftable = 0,
        icon = 27503,
        id = 27503,
        introduction = "팔문촌의 열주는 6시간 안에 원정하는 파트너의 경험치를 증가한다",
        max_grade = 200,
        max_overlay = 99,
        min_grade = 0,
        name = "팔문 경험치 열주",
        quality = 3,
        quickable = 0,
        sale_price = 2000,
        shape = 1003,
        stallable = 0,
        taskid = 0,
        type = 10,
        use_type = "travel_item",
    },

    [27504] = {
        add_time = 21600,
        bag_show_type = 4,
        bat_use = 0,
        buy_price = 0,
        circulation_type = 0,
        coin_rate = 600,
        compose_item = {},
        description = "골드 수익+6%",
        exp_rate = 0,
        gain_way_id = {168},
        gift_price = 0,
        giftable = 0,
        icon = 27504,
        id = 27504,
        introduction = "팔문촌의 통조림은 6시간 안에 원정하는 파트너의 골드 수익을 증가한다",
        max_grade = 200,
        max_overlay = 99,
        min_grade = 0,
        name = "팔낙낙 통조림",
        quality = 3,
        quickable = 0,
        sale_price = 2000,
        shape = 1003,
        stallable = 0,
        taskid = 0,
        type = 10,
        use_type = "travel_item",
    },

    [27505] = {
        add_time = 43200,
        bag_show_type = 4,
        bat_use = 0,
        buy_price = 0,
        circulation_type = 0,
        coin_rate = 0,
        compose_item = {},
        description = "파트너 경험치+12%",
        exp_rate = 1200,
        gain_way_id = {168},
        gift_price = 0,
        giftable = 0,
        icon = 27505,
        id = 27505,
        introduction = "제두의 열주는 12시간 안에 원정하는 파트너의 경험치를 증가한다",
        max_grade = 200,
        max_overlay = 99,
        min_grade = 0,
        name = "제두 경험치 열주",
        quality = 4,
        quickable = 0,
        sale_price = 4000,
        shape = 1003,
        stallable = 0,
        taskid = 0,
        type = 10,
        use_type = "travel_item",
    },

    [27506] = {
        add_time = 43200,
        bag_show_type = 4,
        bat_use = 0,
        buy_price = 0,
        circulation_type = 0,
        coin_rate = 1200,
        compose_item = {},
        description = "골드 수익+12%",
        exp_rate = 0,
        gain_way_id = {168},
        gift_price = 0,
        giftable = 0,
        icon = 27506,
        id = 27506,
        introduction = "제두의 통조림은 12시간 안에 원정하는 파트너의 골드 수익을 증가한다",
        max_grade = 200,
        max_overlay = 99,
        min_grade = 0,
        name = "제왕감 통조림",
        quality = 4,
        quickable = 0,
        sale_price = 4000,
        shape = 1003,
        stallable = 0,
        taskid = 0,
        type = 10,
        use_type = "travel_item",
    },

}
