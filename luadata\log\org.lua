-- ./excel/log/org.xlsx
return {

    ["create"] = {
        desc = "길드 설립",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "create",
    },

    ["join"] = {
        desc = "길드 가입",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "join",
    },

    ["apply"] = {
        desc = "길드 가입 신청",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "apply",
    },

    ["dealapply"] = {
        desc = "신청 처리",
        log_format = {["deal"] = {["id"] = "deal", ["desc"] = "처리"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["target"] = {["id"] = "target", ["desc"] = "목표"}},
        subtype = "dealapply",
    },

    ["dealallapply"] = {
        desc = "모든 신청 거부",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "dealallapply",
    },

    ["dealinvite"] = {
        desc = "요청처리",
        log_format = {["flag"] = {["id"] = "flag", ["desc"] = "동의 혹은 거절"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "dealinvite",
    },

    ["invite"] = {
        desc = "길드 가입 요청",
        log_format = {["inviteid"] = {["id"] = "inviteid", ["desc"] = "요청id"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "invite",
    },

    ["kick"] = {
        desc = "길드 방출",
        log_format = {["kickid"] = {["id"] = "kickid", ["desc"] = "방출id"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "kick",
    },

    ["leave"] = {
        desc = "길드 나가기",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "leave",
    },

    ["setpos"] = {
        desc = "직위 설정",
        log_format = {["newp"] = {["id"] = "newp", ["desc"] = "현재 직위"}, ["oldp"] = {["id"] = "oldp", ["desc"] = "전 직위"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["target"] = {["id"] = "target", ["desc"] = "목표"}},
        subtype = "setpos",
    },

    ["setlimit"] = {
        desc = "设置길드现在",
        log_format = {["allow"] = {["id"] = "allow", ["desc"] = "허락 여부"}, ["limit"] = {["id"] = "limit", ["desc"] = "전투력 제한"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}},
        subtype = "setlimit",
    },

    ["updateflag"] = {
        desc = "휘장 업데이트",
        log_format = {["flagbgid"] = {["id"] = "flagbgid", ["desc"] = "배경휘장"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저id"}, ["sflag"] = {["id"] = "sflag", ["desc"] = "휘장"}},
        subtype = "updateflag",
    },

    ["orgbuild"] = {
        desc = "길드건설",
        log_format = {["build_name"] = {["id"] = "build_name", ["desc"] = "건물 명"}, ["build_type"] = {["id"] = "build_type", ["desc"] = "건물유형"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}},
        subtype = "orgbuild",
    },

    ["speedbuild"] = {
        desc = "길드 건설 가속",
        log_format = {["goldcoin"] = {["id"] = "goldcoin", ["desc"] = "크리스탈"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}},
        subtype = "speedbuild",
    },

    ["build_done"] = {
        desc = "길드 건설 완료",
        log_format = {["build_type"] = {["id"] = "build_type", ["desc"] = "건물 유형"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}},
        subtype = "build_done",
    },

    ["orgsignreward"] = {
        desc = "출석 보상 수령",
        log_format = {["idx"] = {["id"] = "idx", ["desc"] = "출석 보상항"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}},
        subtype = "orgsignreward",
    },

    ["orgwish"] = {
        desc = "길드 소원",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["partner_chip"] = {["id"] = "partner_chip", ["desc"] = "동료 조각"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}},
        subtype = "orgwish",
    },

    ["givewish"] = {
        desc = "길드 소원 도우기",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["partner_chip"] = {["id"] = "partner_chip", ["desc"] = "동료 조각"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}, ["target"] = {["id"] = "target", ["desc"] = "목표"}},
        subtype = "givewish",
    },

    ["openredpacket"] = {
        desc = "훙빠우 기능 오픈",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}, ["position"] = {["id"] = "position", ["desc"] = "직위"}},
        subtype = "openredpacket",
    },

    ["drawredpacket"] = {
        desc = "훙빠우 수령",
        log_format = {["idx"] = {["id"] = "idx", ["desc"] = "훙빠우아이디"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}},
        subtype = "drawredpacket",
    },

    ["uplevel"] = {
        desc = "길드 레벨업",
        log_format = {["new_level"] = {["id"] = "new_level", ["desc"] = "레벨"}, ["old_level"] = {["id"] = "old_level", ["desc"] = "전 레벨"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}},
        subtype = "uplevel",
    },

    ["downlevel"] = {
        desc = "길드 레벨벨업",
        log_format = {["new_level"] = {["id"] = "new_level", ["desc"] = "레벨"}, ["old_level"] = {["id"] = "old_level", ["desc"] = "전 레벨"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}},
        subtype = "downlevel",
    },

    ["release"] = {
        desc = "길드해산",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}},
        subtype = "release",
    },

    ["resumecash"] = {
        desc = "소모된 길드자금",
        log_format = {["cash"] = {["id"] = "cash", ["desc"] = "길드자금"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["val"] = {["id"] = "val", ["desc"] = "소모된 길드자금 액수"}},
        subtype = "resumecash",
    },

    ["addcash"] = {
        desc = "획득한 길드자금",
        log_format = {["cash"] = {["id"] = "cash", ["desc"] = "길드자금"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["val"] = {["id"] = "val", ["desc"] = "획득한 길드자금 액수"}},
        subtype = "addcash",
    },

    ["resumeexp"] = {
        desc = "소모된 길드경험치",
        log_format = {["exp"] = {["id"] = "exp", ["desc"] = "길드경험치"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["val"] = {["id"] = "val", ["desc"] = "경험치"}},
        subtype = "resumeexp",
    },

    ["addexp"] = {
        desc = "획득한 길드경험치",
        log_format = {["exp"] = {["id"] = "exp", ["desc"] = "길드경험치"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["val"] = {["id"] = "val", ["desc"] = "경험치"}},
        subtype = "addexp",
    },

    ["equipwish"] = {
        desc = "장비 소원",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}, ["sid"] = {["id"] = "sid", ["desc"] = "무기아이디"}},
        subtype = "equipwish",
    },

    ["giveequipwish"] = {
        desc = "장비 소원 도우기",
        log_format = {["orgid"] = {["id"] = "orgid", ["desc"] = "길드id"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저"}, ["sid"] = {["id"] = "sid", ["desc"] = "무기아이디"}, ["target"] = {["id"] = "target", ["desc"] = "목표"}},
        subtype = "giveequipwish",
    },

    ["addprestige"] = {
        desc = "길드명망",
        log_format = {["add"] = {["id"] = "add", ["desc"] = "치"}, ["now"] = {["id"] = "now", ["desc"] = "현재치"}, ["orgid"] = {["id"] = "orgid", ["desc"] = "길드"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}},
        subtype = "addprestige",
    },

}
