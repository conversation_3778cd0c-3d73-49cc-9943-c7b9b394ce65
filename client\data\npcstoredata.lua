module(...)
--auto generate data
DATA={
	[201001]={
		coin_count=150000,
		coin_typ=2,
		cycle_type=[[]],
		grade_limit={max=99,min=40,},
		id=201001,
		iospayid=[[]],
		item_id=10001,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[201002]={
		coin_count=1000,
		coin_typ=1,
		cycle_type=[[day]],
		grade_limit={max=50,min=0,},
		id=201002,
		iospayid=[[]],
		item_id=10001,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[201003]={
		coin_count=4,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장비 돌파에 필요한 기본 재료]],
		id=201003,
		iospayid=[[]],
		item_id=11001,
		mark=0,
		name=[[성몽광]],
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[201004]={
		coin_count=50,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장비 돌파에 필요한 중급 재료]],
		id=201004,
		iospayid=[[]],
		item_id=11002,
		mark=0,
		name=[[월영광]],
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[201005]={
		coin_count=36,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장착: 무기에 장착할 수 있다 공격+46
유명진 유적에서 발견된 상고 보석.]],
		id=201005,
		iospayid=[[]],
		item_id=18002,
		mark=0,
		name=[[3레벨 비홍 보석]],
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[201006]={
		coin_count=36,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장착: 반지에 장착할 수 있다치명타률 +1.3%
유명진 유적에서 발견된 상고 보석.]],
		id=201006,
		iospayid=[[]],
		item_id=18302,
		mark=0,
		name=[[3레벨 골드 보석]],
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[201007]={
		coin_count=36,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장착: 신발에 장착할 수 있다 속도+28
유명진 유적에서 발견된 상고 보석.]],
		id=201007,
		iospayid=[[]],
		item_id=18502,
		mark=0,
		name=[[3레벨 질풍 보석]],
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[201008]={
		coin_count=18,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장착: 목걸이에 장착할 수 있다 치명타저항률+0.6%
유명진 유적에서 발견된 상고 보석.]],
		id=201008,
		iospayid=[[]],
		item_id=18102,
		mark=0,
		name=[[3레벨 팔운 보석]],
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[201009]={
		coin_count=18,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장착: 의상에 장착할 수 있다 방어+9
유명진 유적에서 발견된 상고 보석.]],
		id=201009,
		iospayid=[[]],
		item_id=18202,
		mark=0,
		name=[[3레벨 쌍생 보석]],
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[201010]={
		coin_count=18,
		coin_typ=1,
		cycle_type=[[]],
		description=[[장착: 벨트에 장착할 수 있다 체력+401
유명진 유적에서 발견된 상고 보석.]],
		id=201010,
		iospayid=[[]],
		item_id=18402,
		mark=0,
		name=[[3레벨 취성 보석]],
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[202001]={
		coin_count=10,
		coin_typ=1,
		cycle_type=[[]],
		description=[[파트너 룬 레벨업 경험치를 제공]],
		id=202001,
		iospayid=[[]],
		item_id=14021,
		mark=0,
		name=[[남색 호박]],
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[202002]={
		coin_count=12,
		coin_typ=1,
		cycle_type=[[]],
		description=[[공격 룬 탐식에 사용 가능, 공격력 증가]],
		id=202002,
		iospayid=[[]],
		item_id=310001,
		mark=0,
		name=[[파괴 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[202003]={
		coin_count=36,
		coin_typ=1,
		cycle_type=[[]],
		description=[[공격 룬 탐식에 사용 가능, 공격력 증가]],
		id=202003,
		iospayid=[[]],
		item_id=310002,
		mark=0,
		name=[[파괴 룬석·Ⅱ]],
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[202004]={
		coin_count=108,
		coin_typ=1,
		cycle_type=[[]],
		description=[[공격 룬 탐식에 사용 가능, 공격력 증가]],
		id=202004,
		iospayid=[[]],
		item_id=310003,
		mark=0,
		name=[[파괴 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[202005]={
		coin_count=12,
		coin_typ=1,
		cycle_type=[[]],
		description=[[체력 룬 탐식에 사용 가능, 상태이상 저항 및 치명타 확률 증가]],
		id=202005,
		iospayid=[[]],
		item_id=330001,
		mark=0,
		name=[[광휘 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[202006]={
		coin_count=36,
		coin_typ=1,
		cycle_type=[[]],
		description=[[체력 룬 탐식에 사용 가능, 상태이상 저항 및 치명타 확률 증가]],
		id=202006,
		iospayid=[[]],
		item_id=330002,
		mark=0,
		name=[[광휘 룬석·Ⅱ]],
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[202007]={
		coin_count=108,
		coin_typ=1,
		cycle_type=[[]],
		description=[[체력 룬 탐식에 사용 가능, 상태이상 저항 및 치명타 확률 증가]],
		id=202007,
		iospayid=[[]],
		item_id=330003,
		mark=0,
		name=[[광휘 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[202008]={
		coin_count=12,
		coin_typ=1,
		cycle_type=[[]],
		description=[[방어 룬 탐식에 사용 가능, 체력 증가]],
		id=202008,
		iospayid=[[]],
		item_id=320001,
		mark=0,
		name=[[성령 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[202009]={
		coin_count=36,
		coin_typ=1,
		cycle_type=[[]],
		description=[[방어 룬 탐식에 사용 가능, 체력 증가]],
		id=202009,
		iospayid=[[]],
		item_id=320002,
		mark=0,
		name=[[성령 룬석·Ⅱ]],
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[202010]={
		coin_count=108,
		coin_typ=1,
		cycle_type=[[]],
		description=[[방어 룬 탐식에 사용 가능, 체력 증가]],
		id=202010,
		iospayid=[[]],
		item_id=320003,
		mark=0,
		name=[[성령 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[202011]={
		coin_count=12,
		coin_typ=1,
		cycle_type=[[]],
		description=[[쌍령 룬 탐식에 사용 가능, 상태이상 명중 및 치명타 피해 증가]],
		id=202011,
		iospayid=[[]],
		item_id=340001,
		mark=0,
		name=[[금단 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[202012]={
		coin_count=36,
		coin_typ=1,
		cycle_type=[[]],
		description=[[쌍령 룬 탐식에 사용 가능, 상태이상 명중 및 치명타 피해 증가]],
		id=202012,
		iospayid=[[]],
		item_id=340002,
		mark=0,
		name=[[금단 룬석·Ⅱ]],
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[202013]={
		coin_count=108,
		coin_typ=1,
		cycle_type=[[]],
		description=[[쌍령 룬 탐식에 사용 가능, 상태이상 명중 및 치명타 피해 증가]],
		id=202013,
		iospayid=[[]],
		item_id=340003,
		mark=0,
		name=[[금단 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[203001]={
		coin_count=80000,
		coin_typ=2,
		cycle_type=[[week]],
		description=[[신비한 열쇠, 영혼의 보물상자를 여는 데 사용.]],
		id=203001,
		iospayid=[[]],
		item_id=10040,
		mark=4,
		name=[[영혼열쇠]],
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[203002]={
		coin_count=2000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[장비 돌파에 필요한 기본 재료]],
		id=203002,
		iospayid=[[]],
		item_id=11001,
		mark=0,
		name=[[성몽광]],
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[203003]={
		coin_count=25000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[장비 돌파에 필요한 중급 재료]],
		id=203003,
		iospayid=[[]],
		item_id=11002,
		mark=0,
		name=[[월영광]],
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[203004]={
		coin_count=75000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[장비 돌파에 필요한 고급 재료]],
		id=203004,
		iospayid=[[]],
		item_id=11003,
		mark=0,
		name=[[일면광]],
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[203005]={
		coin_count=150000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[신화 특수 무기 세트를 제작할 수 있다]],
		id=203005,
		iospayid=[[]],
		item_id=11004,
		mark=0,
		name=[[무기 원석]],
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[203006]={
		coin_count=75000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[신화 목걸이 세트를 제작할 수 있다]],
		id=203006,
		iospayid=[[]],
		item_id=11005,
		mark=0,
		name=[[목걸이 원석]],
		payid=[[]],
		recharge=0,
		sortId=14,
		vip=0,
	},
	[203007]={
		coin_count=75000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[신화 코스튬 세트를 제작할 수 있다]],
		id=203007,
		iospayid=[[]],
		item_id=11006,
		mark=0,
		name=[[의상 원석]],
		payid=[[]],
		recharge=0,
		sortId=15,
		vip=0,
	},
	[203008]={
		coin_count=75000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[신화 반지 세트를 제작할 수 있다]],
		id=203008,
		iospayid=[[]],
		item_id=11007,
		mark=0,
		name=[[반지 원석]],
		payid=[[]],
		recharge=0,
		sortId=16,
		vip=0,
	},
	[203009]={
		coin_count=75000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[신화 벨트 세트를 제작할 수 있다]],
		id=203009,
		iospayid=[[]],
		item_id=11008,
		mark=0,
		name=[[벨트 원석]],
		payid=[[]],
		recharge=0,
		sortId=17,
		vip=0,
	},
	[203010]={
		coin_count=75000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[신화 신발 세트를 제작할 수 있다]],
		id=203010,
		iospayid=[[]],
		item_id=11009,
		mark=0,
		name=[[신발 원석]],
		payid=[[]],
		recharge=0,
		sortId=18,
		vip=0,
	},
	[203011]={
		coin_count=50000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[합성된 장비에 랜덤으로 강력한 효과를 추가할 수 있다]],
		id=203011,
		iospayid=[[]],
		item_id=11010,
		mark=0,
		name=[[마법 부여 원석]],
		payid=[[]],
		recharge=0,
		sortId=19,
		vip=0,
	},
	[203012]={
		coin_count=10000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[모든 패키지 합성에 필요한 재료]],
		id=203012,
		iospayid=[[]],
		item_id=11016,
		mark=0,
		name=[[합성가루]],
		payid=[[]],
		recharge=0,
		sortId=20,
		vip=0,
	},
	[203013]={
		coin_count=5000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[그 안에 신비한 의식의 에너지를 가진 결정체가 있다.장비 쉬령에 사용할 수 있다]],
		id=203013,
		iospayid=[[]],
		item_id=11101,
		mark=0,
		name=[[쉬령운정]],
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[203014]={
		coin_count=2500,
		coin_typ=2,
		cycle_type=[[]],
		description=[[파트너 레벨업 경험을 제공할 수 있습니다.]],
		id=203014,
		iospayid=[[]],
		item_id=14001,
		mark=0,
		name=[[고기 찐빵]],
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[203015]={
		coin_count=5000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[파트너 룬 레벨업 경험치를 제공]],
		id=203015,
		iospayid=[[]],
		item_id=14021,
		mark=0,
		name=[[남색 호박]],
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[203016]={
		coin_count=1000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[1 성 룬 성 승급에 필요한 재료.]],
		id=203016,
		iospayid=[[]],
		item_id=14031,
		mark=0,
		name=[[1성 운모]],
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[203017]={
		coin_count=3000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[2 성 룬 성 승급에 필요한 재료.]],
		id=203017,
		iospayid=[[]],
		item_id=14032,
		mark=0,
		name=[[2성 운모]],
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[203018]={
		coin_count=9000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[3 성 룬 성 승급에 필요한 재료.]],
		id=203018,
		iospayid=[[]],
		item_id=14033,
		mark=0,
		name=[[3성 운모]],
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[203019]={
		coin_count=27000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[4 성 룬 성 승급에 필요한 재료.]],
		id=203019,
		iospayid=[[]],
		item_id=14034,
		mark=0,
		name=[[4성 운모]],
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[203020]={
		coin_count=81000,
		coin_typ=2,
		cycle_type=[[]],
		description=[[5 성 룬 성 승급에 필요한 재료.]],
		id=203020,
		iospayid=[[]],
		item_id=14035,
		mark=0,
		name=[[5성 운모]],
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[204001]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204001,
		iospayid=[[]],
		item_id=20302,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[204002]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204002,
		iospayid=[[]],
		item_id=20303,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[204003]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204003,
		iospayid=[[]],
		item_id=20311,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[204004]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204004,
		iospayid=[[]],
		item_id=20312,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[204005]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204005,
		iospayid=[[]],
		item_id=20314,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[204006]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204006,
		iospayid=[[]],
		item_id=20316,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[204007]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204007,
		iospayid=[[]],
		item_id=20402,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[204008]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204008,
		iospayid=[[]],
		item_id=20407,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[204009]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204009,
		iospayid=[[]],
		item_id=20503,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[204010]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204010,
		iospayid=[[]],
		item_id=20504,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[204011]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204011,
		iospayid=[[]],
		item_id=20505,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[204012]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204012,
		iospayid=[[]],
		item_id=20506,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[204013]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204013,
		iospayid=[[]],
		item_id=20508,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[204014]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204014,
		iospayid=[[]],
		item_id=20509,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=14,
		vip=0,
	},
	[204015]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204015,
		iospayid=[[]],
		item_id=20313,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=15,
		vip=0,
	},
	[204016]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204016,
		iospayid=[[]],
		item_id=20409,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=16,
		vip=0,
	},
	[204017]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204017,
		iospayid=[[]],
		item_id=20514,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=17,
		vip=0,
	},
	[204018]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204018,
		iospayid=[[]],
		item_id=20315,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=18,
		vip=0,
	},
	[204019]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204019,
		iospayid=[[]],
		item_id=20512,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=19,
		vip=0,
	},
	[204020]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204020,
		iospayid=[[]],
		item_id=20405,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=20,
		vip=0,
	},
	[204021]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204021,
		iospayid=[[]],
		item_id=20418,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=21,
		vip=0,
	},
	[204022]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204022,
		iospayid=[[]],
		item_id=20510,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=22,
		vip=0,
	},
	[204023]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204023,
		iospayid=[[]],
		item_id=20511,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=23,
		vip=0,
	},
	[204024]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204024,
		iospayid=[[]],
		item_id=20305,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=24,
		vip=0,
	},
	[204025]={
		coin_count=1500,
		coin_typ=3,
		cycle_type=[[]],
		id=204025,
		iospayid=[[]],
		item_id=20306,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=25,
		vip=0,
	},
	[204026]={
		coin_count=750,
		coin_typ=3,
		cycle_type=[[]],
		id=204026,
		iospayid=[[]],
		item_id=11005,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=26,
		vip=0,
	},
	[204027]={
		coin_count=750,
		coin_typ=3,
		cycle_type=[[]],
		id=204027,
		iospayid=[[]],
		item_id=11006,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=27,
		vip=0,
	},
	[204028]={
		coin_count=750,
		coin_typ=3,
		cycle_type=[[]],
		id=204028,
		iospayid=[[]],
		item_id=11007,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=28,
		vip=0,
	},
	[204029]={
		coin_count=750,
		coin_typ=3,
		cycle_type=[[]],
		id=204029,
		iospayid=[[]],
		item_id=11008,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=29,
		vip=0,
	},
	[204030]={
		coin_count=750,
		coin_typ=3,
		cycle_type=[[]],
		id=204030,
		iospayid=[[]],
		item_id=11009,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=30,
		vip=0,
	},
	[204031]={
		coin_count=125,
		coin_typ=3,
		cycle_type=[[]],
		description=[[상점에서 스킨 교환 가능]],
		id=204031,
		iospayid=[[]],
		item_id=1017,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=31,
		vip=0,
	},
	[204032]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204032,
		iospayid=[[]],
		item_id=20417,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=32,
		vip=0,
	},
	[204033]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204033,
		iospayid=[[]],
		item_id=20403,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=33,
		vip=0,
	},
	[204034]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204034,
		iospayid=[[]],
		item_id=20513,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=34,
		vip=0,
	},
	[204035]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204035,
		iospayid=[[]],
		item_id=20501,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=35,
		vip=0,
	},
	[204036]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204036,
		iospayid=[[]],
		item_id=20507,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=36,
		vip=0,
	},
	[204037]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204037,
		iospayid=[[]],
		item_id=20502,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=37,
		vip=0,
	},
	[204038]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204038,
		iospayid=[[]],
		item_id=20416,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=38,
		vip=0,
	},
	[204039]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204039,
		iospayid=[[]],
		item_id=20415,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=39,
		vip=0,
	},
	[204040]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204040,
		iospayid=[[]],
		item_id=20414,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=40,
		vip=0,
	},
	[204041]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204041,
		iospayid=[[]],
		item_id=20413,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=41,
		vip=0,
	},
	[204042]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204042,
		iospayid=[[]],
		item_id=20412,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=42,
		vip=0,
	},
	[204043]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204043,
		iospayid=[[]],
		item_id=20410,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=43,
		vip=0,
	},
	[204044]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204044,
		iospayid=[[]],
		item_id=20404,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=44,
		vip=0,
	},
	[204045]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204045,
		iospayid=[[]],
		item_id=20401,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=45,
		vip=0,
	},
	[204046]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204046,
		iospayid=[[]],
		item_id=20308,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=46,
		vip=0,
	},
	[204047]={
		coin_count=1000,
		coin_typ=3,
		cycle_type=[[]],
		id=204047,
		iospayid=[[]],
		item_id=20301,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=47,
		vip=0,
	},
	[205001]={
		coin_count=4000,
		coin_typ=4,
		cycle_type=[[week]],
		description=[[임의의 파트너 조각을 교환할 수 있습니다]],
		id=205001,
		iospayid=[[]],
		item_id=14002,
		mark=4,
		name=[[마스터 조각]],
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[205002]={
		coin_count=500,
		coin_typ=4,
		cycle_type=[[]],
		description=[[파트너 스킬 레벨업에 필요한 재료입니다.]],
		id=205002,
		iospayid=[[]],
		item_id=14011,
		mark=0,
		name=[[캐러멜찐빵]],
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[205003]={
		coin_count=3000,
		coin_typ=4,
		cycle_type=[[]],
		description=[[신화 특수 무기 세트를 제작할 수 있다]],
		id=205003,
		iospayid=[[]],
		item_id=11004,
		mark=0,
		name=[[무기 원석]],
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[205004]={
		coin_count=120,
		coin_typ=4,
		cycle_type=[[]],
		description=[[공격 룬 탐식에 사용 가능, 공격력 증가]],
		id=205004,
		iospayid=[[]],
		item_id=310001,
		mark=0,
		name=[[파괴 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[205005]={
		coin_count=1026,
		coin_typ=4,
		cycle_type=[[]],
		description=[[공격 룬 탐식에 사용 가능, 공격력 증가]],
		id=205005,
		iospayid=[[]],
		item_id=310003,
		mark=0,
		name=[[파괴 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[205006]={
		coin_count=8748,
		coin_typ=4,
		cycle_type=[[]],
		description=[[공격 룬 탐식에 사용 가능, 공격력 증가]],
		id=205006,
		iospayid=[[]],
		item_id=310005,
		mark=0,
		name=[[파괴 룬석·Ⅴ]],
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[205007]={
		coin_count=120,
		coin_typ=4,
		cycle_type=[[]],
		description=[[체력 룬 탐식에 사용 가능, 상태이상 저항 및 치명타 확률 증가]],
		id=205007,
		iospayid=[[]],
		item_id=330001,
		mark=0,
		name=[[광휘 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[205008]={
		coin_count=1026,
		coin_typ=4,
		cycle_type=[[]],
		description=[[체력 룬 탐식에 사용 가능, 상태이상 저항 및 치명타 확률 증가]],
		id=205008,
		iospayid=[[]],
		item_id=330003,
		mark=0,
		name=[[광휘 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[205009]={
		coin_count=8748,
		coin_typ=4,
		cycle_type=[[]],
		description=[[체력 룬 탐식에 사용 가능, 상태이상 저항 및 치명타 확률 증가]],
		id=205009,
		iospayid=[[]],
		item_id=330005,
		mark=0,
		name=[[광휘 룬석·Ⅴ]],
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[205010]={
		coin_count=120,
		coin_typ=4,
		cycle_type=[[]],
		description=[[방어 룬 탐식에 사용 가능, 체력 증가]],
		id=205010,
		iospayid=[[]],
		item_id=320001,
		mark=0,
		name=[[성령 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[205011]={
		coin_count=1026,
		coin_typ=4,
		cycle_type=[[]],
		description=[[방어 룬 탐식에 사용 가능, 체력 증가]],
		id=205011,
		iospayid=[[]],
		item_id=320003,
		mark=0,
		name=[[성령 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[205012]={
		coin_count=8748,
		coin_typ=4,
		cycle_type=[[]],
		description=[[방어 룬 탐식에 사용 가능, 체력 증가]],
		id=205012,
		iospayid=[[]],
		item_id=320005,
		mark=0,
		name=[[성령 룬석·Ⅴ]],
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[205013]={
		coin_count=120,
		coin_typ=4,
		cycle_type=[[]],
		description=[[쌍령 룬 탐식에 사용 가능, 상태이상 명중 및 치명타 피해 증가]],
		id=205013,
		iospayid=[[]],
		item_id=340001,
		mark=0,
		name=[[금단 룬석·Ⅰ]],
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[205014]={
		coin_count=1026,
		coin_typ=4,
		cycle_type=[[]],
		description=[[쌍령 룬 탐식에 사용 가능, 상태이상 명중 및 치명타 피해 증가]],
		id=205014,
		iospayid=[[]],
		item_id=340003,
		mark=0,
		name=[[금단 룬석·Ⅲ]],
		payid=[[]],
		recharge=0,
		sortId=14,
		vip=0,
	},
	[205015]={
		coin_count=8748,
		coin_typ=4,
		cycle_type=[[]],
		description=[[쌍령 룬 탐식에 사용 가능, 상태이상 명중 및 치명타 피해 증가]],
		id=205015,
		iospayid=[[]],
		item_id=340005,
		mark=0,
		name=[[금단 룬석·Ⅴ]],
		payid=[[]],
		recharge=0,
		sortId=15,
		vip=0,
	},
	[206001]={
		coin_count=40000,
		coin_typ=2,
		cycle_type=[[]],
		id=206001,
		iospayid=[[]],
		item_id=12843,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[206002]={
		coin_count=200000,
		coin_typ=2,
		cycle_type=[[]],
		id=206002,
		iospayid=[[]],
		item_id=12853,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[206003]={
		coin_count=1000000,
		coin_typ=2,
		cycle_type=[[]],
		id=206003,
		iospayid=[[]],
		item_id=12863,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[207001]={
		coin_count=160,
		coin_typ=6,
		cycle_type=[[]],
		id=207001,
		iospayid=[[]],
		item_id=11001,
		mark=0,
		name=[[성몽광]],
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[207002]={
		coin_count=2000,
		coin_typ=6,
		cycle_type=[[]],
		id=207002,
		iospayid=[[]],
		item_id=11002,
		mark=0,
		name=[[월영광]],
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[207003]={
		coin_count=6000,
		coin_typ=6,
		cycle_type=[[]],
		id=207003,
		iospayid=[[]],
		item_id=11003,
		mark=0,
		name=[[일면광]],
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[207004]={
		coin_count=400,
		coin_typ=6,
		cycle_type=[[]],
		id=207004,
		iospayid=[[]],
		item_id=11101,
		mark=0,
		name=[[쉬령운정]],
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[207005]={
		coin_count=1440,
		coin_typ=6,
		cycle_type=[[]],
		description=[[장착: 무기에 장착할 수 있다 공격+46
유명진 유적에서 발견된 상고 보석.]],
		id=207005,
		iospayid=[[]],
		item_id=18002,
		mark=0,
		name=[[3레벨 비홍 보석]],
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[207006]={
		coin_count=1440,
		coin_typ=6,
		cycle_type=[[]],
		description=[[장착: 반지에 장착할 수 있다치명타률 +1.3%
유명진 유적에서 발견된 상고 보석.]],
		id=207006,
		iospayid=[[]],
		item_id=18302,
		mark=0,
		name=[[3레벨 골드 보석]],
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[207007]={
		coin_count=1440,
		coin_typ=6,
		cycle_type=[[]],
		description=[[장착: 신발에 장착할 수 있다 속도+28
유명진 유적에서 발견된 상고 보석.]],
		id=207007,
		iospayid=[[]],
		item_id=18502,
		mark=0,
		name=[[3레벨 질풍 보석]],
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[207008]={
		coin_count=720,
		coin_typ=6,
		cycle_type=[[]],
		description=[[장착: 목걸이에 장착할 수 있다 치명타저항률+0.6%
유명진 유적에서 발견된 상고 보석.]],
		id=207008,
		iospayid=[[]],
		item_id=18102,
		mark=0,
		name=[[3레벨 질풍 보석]],
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[207009]={
		coin_count=720,
		coin_typ=6,
		cycle_type=[[]],
		description=[[장착: 의상에 장착할 수 있다 방어+9
유명진 유적에서 발견된 상고 보석.]],
		id=207009,
		iospayid=[[]],
		item_id=18202,
		mark=0,
		name=[[3레벨 팔운 보석]],
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[207010]={
		coin_count=720,
		coin_typ=6,
		cycle_type=[[]],
		description=[[장착: 벨트에 장착할 수 있다 체력+401
유명진 유적에서 발견된 상고 보석.]],
		id=207010,
		iospayid=[[]],
		item_id=18402,
		mark=0,
		name=[[3레벨 취성보석]],
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[207011]={
		coin_count=400,
		coin_typ=6,
		cycle_type=[[]],
		description=[[각성 비법이 기록된 서적]],
		id=207011,
		iospayid=[[]],
		item_id=27402,
		mark=0,
		name=[[몽각서-하]],
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[207012]={
		coin_count=400,
		coin_typ=6,
		cycle_type=[[]],
		description=[[인간 파트너의 힘을 각성하는 재료]],
		id=207012,
		iospayid=[[]],
		item_id=27405,
		mark=0,
		name=[[인영결-하]],
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[207013]={
		coin_count=400,
		coin_typ=6,
		cycle_type=[[]],
		description=[[요괴 파트너의 힘을 각성하는 재료]],
		id=207013,
		iospayid=[[]],
		item_id=27408,
		mark=0,
		name=[[요풍령-하]],
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[207014]={
		coin_count=400,
		coin_typ=6,
		cycle_type=[[]],
		description=[[유령 파트너의 힘을 각성하는 재료]],
		id=207014,
		iospayid=[[]],
		item_id=27411,
		mark=0,
		name=[[귀운윤-하]],
		payid=[[]],
		recharge=0,
		sortId=14,
		vip=0,
	},
	[208004]={
		coin_count=80,
		coin_typ=5,
		cycle_type=[[]],
		description=[[오픈 후 200000 골드 획득, 게임 중 기초 화폐]],
		id=208004,
		iospayid=[[]],
		item_id=13312,
		mark=0,
		name=[[200000골드]],
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[208005]={
		coin_count=20,
		coin_typ=5,
		cycle_type=[[week]],
		description=[[보물이 묻힌 곳을 기록한 신비한 도면]],
		id=208005,
		iospayid=[[]],
		item_id=10024,
		mark=4,
		name=[[성상도]],
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[208006]={
		coin_count=80,
		coin_typ=5,
		cycle_type=[[week]],
		description=[[80개의 조각을 모아 일발 입혼 계약을 합성할 수 있습니다.일발 입혼 계약은 3 성 전설 파트너를 랜덤으로 획득]],
		id=208006,
		iospayid=[[]],
		item_id=13212,
		mark=4,
		name=[[일발입혼조각]],
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[208007]={
		coin_count=20,
		coin_typ=5,
		cycle_type=[[]],
		description=[[합성된 장비에 랜덤 강력 효과를 부가할 수 있습니다.]],
		id=208007,
		iospayid=[[]],
		item_id=11010,
		mark=0,
		name=[[마법 부여 원석]],
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[209003]={
		coin_count=15000,
		coin_typ=7,
		cycle_type=[[day]],
		description=[[오픈 후 랜덤으로 골드, 수정 획득]],
		id=209003,
		iospayid=[[]],
		item_id=13209,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[209004]={
		coin_count=15000,
		coin_typ=7,
		cycle_type=[[day]],
		description=[[다음 신격 중 하나를 선택할 수 있다: 
◇일시적으로 캐릭터 공격력 증가하는 신격-공 1개
◇일시적으로 캐릭터 증속하는 신격-속 1개
◇ 일시적으로 캐릭터 치명타 확률 증가하는 신격-폭 1개]],
		id=209004,
		iospayid=[[]],
		item_id=13216,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[209005]={
		coin_count=15000,
		coin_typ=7,
		cycle_type=[[day]],
		description=[[다음 신격 중 하나를 선택할 수 있다 
◇일시적으로 캐릭터 HP 증가하는 신격-생 1개
◇일시적으로 캐릭터 방어력 증가하는 신격-방 1개
◇ 일시적으로 캐릭터 저항 확률 증가하는 신격-항 1개]],
		id=209005,
		iospayid=[[]],
		item_id=13217,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[210001]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210001,
		iospayid=[[]],
		item_id=203032,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[210002]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210002,
		iospayid=[[]],
		item_id=203062,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[210003]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210003,
		iospayid=[[]],
		item_id=203152,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[210004]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210004,
		iospayid=[[]],
		item_id=203162,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[210005]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210005,
		iospayid=[[]],
		item_id=204052,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[210006]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210006,
		iospayid=[[]],
		item_id=204072,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[210007]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210007,
		iospayid=[[]],
		item_id=204092,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[210008]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210008,
		iospayid=[[]],
		item_id=204182,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[210011]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210011,
		iospayid=[[]],
		item_id=205042,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[210012]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210012,
		iospayid=[[]],
		item_id=205102,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[210013]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210013,
		iospayid=[[]],
		item_id=205122,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[210014]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210014,
		iospayid=[[]],
		item_id=203132,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[210015]={
		coin_count=80,
		coin_typ=9,
		cycle_type=[[]],
		id=210015,
		iospayid=[[]],
		item_id=204183,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=14,
		vip=0,
	},
	[211001]={
		coin_count=5,
		coin_typ=10,
		cycle_type=[[]],
		id=211001,
		iospayid=[[]],
		item_id=27501,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[211002]={
		coin_count=5,
		coin_typ=10,
		cycle_type=[[]],
		id=211002,
		iospayid=[[]],
		item_id=27502,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[211003]={
		coin_count=20,
		coin_typ=10,
		cycle_type=[[]],
		id=211003,
		iospayid=[[]],
		item_id=27503,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[211004]={
		coin_count=20,
		coin_typ=10,
		cycle_type=[[]],
		id=211004,
		iospayid=[[]],
		item_id=27504,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[211005]={
		coin_count=80,
		coin_typ=10,
		cycle_type=[[]],
		id=211005,
		iospayid=[[]],
		item_id=27505,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[211006]={
		coin_count=80,
		coin_typ=10,
		cycle_type=[[]],
		id=211006,
		iospayid=[[]],
		item_id=27506,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[211007]={
		coin_count=60,
		coin_typ=10,
		cycle_type=[[]],
		id=211007,
		iospayid=[[]],
		item_id=10024,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[211009]={
		coin_count=10,
		coin_typ=10,
		cycle_type=[[]],
		id=211009,
		iospayid=[[]],
		item_id=27402,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[211010]={
		coin_count=50,
		coin_typ=10,
		cycle_type=[[]],
		id=211010,
		iospayid=[[]],
		item_id=27403,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[211011]={
		coin_count=10,
		coin_typ=10,
		cycle_type=[[]],
		id=211011,
		iospayid=[[]],
		item_id=27405,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[211012]={
		coin_count=50,
		coin_typ=10,
		cycle_type=[[]],
		id=211012,
		iospayid=[[]],
		item_id=27406,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[211013]={
		coin_count=10,
		coin_typ=10,
		cycle_type=[[]],
		id=211013,
		iospayid=[[]],
		item_id=27408,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[211014]={
		coin_count=50,
		coin_typ=10,
		cycle_type=[[]],
		id=211014,
		iospayid=[[]],
		item_id=27409,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[211015]={
		coin_count=10,
		coin_typ=10,
		cycle_type=[[]],
		id=211015,
		iospayid=[[]],
		item_id=27411,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=14,
		vip=0,
	},
	[211016]={
		coin_count=50,
		coin_typ=10,
		cycle_type=[[]],
		id=211016,
		iospayid=[[]],
		item_id=27412,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=15,
		vip=0,
	},
	[211017]={
		coin_count=16,
		coin_typ=10,
		cycle_type=[[]],
		id=211017,
		iospayid=[[]],
		item_id=14031,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=16,
		vip=0,
	},
	[211018]={
		coin_count=25,
		coin_typ=10,
		cycle_type=[[]],
		id=211018,
		iospayid=[[]],
		item_id=14032,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=17,
		vip=0,
	},
	[211019]={
		coin_count=30,
		coin_typ=10,
		cycle_type=[[]],
		id=211019,
		iospayid=[[]],
		item_id=14033,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=18,
		vip=0,
	},
	[211020]={
		coin_count=40,
		coin_typ=10,
		cycle_type=[[]],
		id=211020,
		iospayid=[[]],
		item_id=14034,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=19,
		vip=0,
	},
	[211021]={
		coin_count=50,
		coin_typ=10,
		cycle_type=[[]],
		id=211021,
		iospayid=[[]],
		item_id=14035,
		mark=0,
		payid=[[]],
		recharge=0,
		sortId=20,
		vip=0,
	},
	[212001]={
		coin_count=50000,
		coin_typ=2,
		cycle_type=[[day]],
		description=[[오픈 후 60 체력을 획득]],
		id=212001,
		iospayid=[[]],
		item_id=13301,
		mark=4,
		name=[[60체]],
		payid=[[]],
		recharge=0,
		sortId=28,
		vip=0,
	},
	[212002]={
		coin_count=100,
		coin_typ=1,
		cycle_type=[[]],
		description=[[오픈 후 50000 골드 획득, 게임 중 기초 화폐]],
		id=212002,
		iospayid=[[]],
		item_id=13311,
		mark=0,
		name=[[50000골드]],
		payid=[[]],
		recharge=0,
		sortId=23,
		vip=0,
	},
	[212003]={
		coin_count=1,
		coin_typ=1,
		cycle_type=[[]],
		description=[[전투 관문 소탕에 사용할 수 있습니다.]],
		id=212003,
		iospayid=[[]],
		item_id=10030,
		mark=0,
		name=[[소탕권]],
		payid=[[]],
		recharge=0,
		sortId=24,
		vip=0,
	},
	[212004]={
		coin_count=300,
		coin_typ=1,
		cycle_type=[[]],
		description=[[80개의 조각을 모아 일발 입혼 계약을 합성할 수 있습니다.일발 입혼 계약은 3 성 전설 파트너를 랜덤으로 획득]],
		id=212004,
		iospayid=[[]],
		item_id=13212,
		mark=0,
		name=[[일발입혼조각]],
		payid=[[]],
		recharge=0,
		sortId=14,
		vip=0,
	},
	[212005]={
		coin_count=600,
		coin_typ=1,
		cycle_type=[[day]],
		description=[[오픈 후 영혼 보물 상자의 영혼 열쇠 5개를 획득할 수 있다]],
		id=212005,
		iospayid=[[]],
		item_id=13239,
		mark=4,
		name=[[영혼열쇠팩]],
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
	[212006]={
		coin_count=150,
		coin_typ=1,
		cycle_type=[[]],
		description=[[오픈 후 50개 성몽광, 장비 돌파에 필요한 기본 재료를 획득한다]],
		id=212006,
		iospayid=[[]],
		item_id=13221,
		mark=6,
		name=[[성몽광 패키지]],
		payid=[[]],
		recharge=0,
		sortId=15,
		vip=0,
	},
	[212007]={
		coin_count=400,
		coin_typ=1,
		cycle_type=[[]],
		description=[[오픈 후 10개 월영광을 획득, 돌파 레벨이 60 레벨보다 높은 장비가 계속 돌파 시 사용 가능.]],
		id=212007,
		iospayid=[[]],
		item_id=13224,
		mark=6,
		name=[[월영광 패키지]],
		payid=[[]],
		recharge=0,
		sortId=16,
		vip=0,
	},
	[212008]={
		coin_count=600,
		coin_typ=1,
		cycle_type=[[]],
		description=[[오픈 후 5 개의 일면광을 획득, 돌파 레벨이 80 레벨 이상의 장비가 계속 돌파 시 사용 가능.]],
		id=212008,
		iospayid=[[]],
		item_id=13238,
		mark=6,
		name=[[일면광 패키지]],
		payid=[[]],
		recharge=0,
		sortId=26,
		vip=0,
	},
	[212009]={
		coin_count=160,
		coin_typ=1,
		cycle_type=[[]],
		description=[[오픈 후 획득한 20개의 쉬령운정은 장비 쉬령에 사용할 수 있다]],
		id=212009,
		iospayid=[[]],
		item_id=13262,
		mark=6,
		name=[[쉬령운정 선물 가방]],
		payid=[[]],
		recharge=0,
		sortId=27,
		vip=0,
	},
	[212010]={
		coin_count=280,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 보석 중 하나를 선택할 수 있다: 
◇공격력 증가하는 3레벨 비홍 보석 10개
◇치명타 확률 증가하는 3레벨 황금 보석 10개
◇증속하는 3레벨 질풍 보석 10개]],
		id=212010,
		iospayid=[[]],
		item_id=13214,
		mark=6,
		name=[[공격 보석 패키지]],
		payid=[[]],
		recharge=0,
		sortId=17,
		vip=0,
	},
	[212011]={
		coin_count=140,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 보석 중 하나를 선택할 수 있다: 
◇저항 확률 증가하는 3레벨 팔운 보석 10개
◇방어력 증가하는 3레벨 쌍생 보석 10개
◇HP 증가하는 3레벨 취성 보석 10개]],
		id=212011,
		iospayid=[[]],
		item_id=13215,
		mark=6,
		name=[[방어 보석 패키지]],
		payid=[[]],
		recharge=0,
		sortId=18,
		vip=0,
	},
	[212012]={
		coin_count=10,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 룬석 중 하나를 선택할 수 있다: 
◇파괴 룬석-Ⅰ
◇광휘 룬석-Ⅰ
◇성령 룬석-Ⅰ
◇금단 룬석-Ⅰ]],
		id=212012,
		iospayid=[[]],
		item_id=14201,
		mark=6,
		name=[[1레벨 룬석 가방]],
		payid=[[]],
		recharge=0,
		sortId=30,
		vip=0,
	},
	[212013]={
		coin_count=80,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 룬석 중 하나를 선택할 수 있다: 
◇파괴 룬석-Ⅲ
◇광휘 룬석-Ⅲ
◇성령 룬석-Ⅲ
◇금단 룬석-Ⅲ]],
		id=212013,
		iospayid=[[]],
		item_id=14203,
		mark=6,
		name=[[3레벨 룬석 가방]],
		payid=[[]],
		recharge=0,
		sortId=31,
		vip=0,
	},
	[212014]={
		coin_count=688,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 룬석 중 하나를 선택할 수 있다: 
◇파괴 룬석-Ⅴ
◇광휘 룬석-Ⅴ
◇성령 룬석-Ⅴ
◇금단 룬석-Ⅴ]],
		id=212014,
		iospayid=[[]],
		item_id=14205,
		mark=6,
		name=[[5레벨 룬석 가방]],
		payid=[[]],
		recharge=0,
		sortId=32,
		vip=0,
	},
	[212015]={
		coin_count=29000,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 파트너 스킨 구매에 사용 가능한 60 스킨권을 획득한다]],
		id=212015,
		iospayid=[[com.kaopu.ylq.appstore.lb.128]],
		item_id=13307,
		mark=0,
		name=[[스킨권 패키지]],
		payid=[[com.kaopu.ylq.lb.128]],
		recharge=0,
		sortId=22,
		vip=0,
	},
	[212016]={
		coin_count=1500,
		coin_typ=13,
		cycle_type=[[day]],
		description=[[월정액/평생 카드 소지자는 1일 1회 구매 가능,오픈 후 60수정, 60000골드, 60체력을 획득]],
		id=212016,
		iospayid=[[com.kaopu.ylq.appstore.lb.6]],
		item_id=13265,
		mark=4,
		name=[[일일 특권 패키지]],
		payid=[[com.kaopu.ylq.lb.6]],
		recharge=0,
		sortId=8,
		vip=1,
	},
	[212017]={
		coin_count=1,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 5000 골드 획득, 게임 중 기초 화폐]],
		id=212017,
		iospayid=[[com.kaopu.ylq.appstore.lb.1]],
		item_id=13313,
		mark=0,
		name=[[1원 패키지]],
		payid=[[com.kaopu.ylq.lb.1]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[212018]={
		coin_count=1500,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 30000 골드 획득, 게임 중 기초 화폐]],
		id=212018,
		iospayid=[[com.kaopu.ylq.appstore.lb.6]],
		item_id=13314,
		mark=0,
		name=[[6원 패키지]],
		payid=[[com.kaopu.ylq.lb.6]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[212019]={
		coin_count=3000,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 60000 골드 획득, 게임 중 기초 화폐]],
		id=212019,
		iospayid=[[com.kaopu.ylq.appstore.lb.12]],
		item_id=13315,
		mark=0,
		name=[[12원 패키지]],
		payid=[[com.kaopu.ylq.lb.12]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[212020]={
		coin_count=7500,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 150000 골드 획득, 게임 중 기초 화폐]],
		id=212020,
		iospayid=[[com.kaopu.ylq.appstore.lb.30]],
		item_id=13316,
		mark=0,
		name=[[30원 패키지]],
		payid=[[com.kaopu.ylq.lb.30]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[212021]={
		coin_count=17000,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 340000 골드 획득, 게임 중 기초 화폐]],
		id=212021,
		iospayid=[[com.kaopu.ylq.appstore.lb.68]],
		item_id=13317,
		mark=0,
		name=[[68원 패키지]],
		payid=[[com.kaopu.ylq.lb.68]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[212022]={
		coin_count=29000,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 640000 골드 획득, 게임 중 기초 화폐]],
		id=212022,
		iospayid=[[com.kaopu.ylq.appstore.lb.128]],
		item_id=13318,
		mark=0,
		name=[[128원 패키지]],
		payid=[[com.kaopu.ylq.lb.128]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[212023]={
		coin_count=68000,
		coin_typ=13,
		cycle_type=[[]],
		description=[[오픈 후 1640000 골드 획득, 게임 중 기초 화폐]],
		id=212023,
		iospayid=[[com.kaopu.ylq.appstore.lb.328]],
		item_id=13319,
		mark=0,
		name=[[328원 패키지]],
		payid=[[com.kaopu.ylq.lb.328]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[212024]={
		coin_count=1500,
		coin_typ=13,
		cycle_type=[[day]],
		description=[[월정액/평생 카드 소지자는 1일 1회 구매 가능，오픈 후 140000골드 획득]],
		id=212024,
		iospayid=[[com.kaopu.ylq.appstore.lb.6]],
		item_id=13266,
		mark=4,
		name=[[일일 골드 패키지]],
		payid=[[com.kaopu.ylq.lb.6]],
		recharge=0,
		sortId=9,
		vip=1,
	},
	[212025]={
		coin_count=3000,
		coin_typ=13,
		cycle_type=[[day]],
		description=[[오픈 후 룬 강화 시 소모되는 재료인 남색 호박 50개를 획득한다]],
		id=212025,
		iospayid=[[com.kaopu.ylq.appstore.lb.12]],
		item_id=13267,
		mark=4,
		name=[[일일 남색 패키지]],
		payid=[[com.kaopu.ylq.lb.12]],
		recharge=0,
		sortId=10,
		vip=1,
	},
	[212026]={
		coin_count=240,
		coin_typ=1,
		cycle_type=[[]],
		description=[[오픈 후 룬 강화 시 소모되는 재료인 남색 호박 30개를 획득한다]],
		id=212026,
		iospayid=[[]],
		item_id=13331,
		mark=0,
		name=[[남색 호박 패키지]],
		payid=[[]],
		recharge=0,
		sortId=13,
		vip=0,
	},
	[212027]={
		coin_count=150,
		coin_typ=1,
		cycle_type=[[]],
		description=[[신비한 열쇠, 영혼의 보물상자를 여는 데 사용.]],
		id=212027,
		iospayid=[[]],
		item_id=10040,
		mark=0,
		name=[[영혼열쇠]],
		payid=[[]],
		recharge=0,
		sortId=11,
		vip=0,
	},
	[212028]={
		coin_count=50,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 룬석 중 하나를 선택할 수 있다: 
◇5개 파괴 룬석-Ⅰ, 룬 공격을 증가할 수 있다
◇5개 성령 룬석-Ⅰ, 룬 체력을 증가할 수 있다
◇5개 광휘 룬석-Ⅰ, 룬 이상 저항과 치명타를 증가할 수 있다
◇5개 금단 룬석-Ⅰ, 룬석 이상 명중과 치명타 피해를 증가할 수 있다
]],
		id=212028,
		iospayid=[[]],
		item_id=14251,
		mark=0,
		name=[[1레벨 룬석 선물 가방]],
		payid=[[]],
		recharge=0,
		sortId=19,
		vip=0,
	},
	[212029]={
		coin_count=145,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 룬석 중 하나를 선택할 수 있다: 
◇5개 파괴 룬석-Ⅱ, 룬 공격을 증가할 수 있다
◇5개 성령 룬석-Ⅱ, 룬 체력을 증가할 수 있다
◇5개 광휘 룬석-Ⅱ, 룬 이상 저항과 치명타를 증가할 수 있다
◇5개 금단 룬석-Ⅱ, 룬석 이상 명중과 치명타 피해를 증가할 수 있다

]],
		id=212029,
		iospayid=[[]],
		item_id=14252,
		mark=0,
		name=[[2레벨 룬석 선물 가방]],
		payid=[[]],
		recharge=0,
		sortId=20,
		vip=0,
	},
	[212030]={
		coin_count=400,
		coin_typ=1,
		cycle_type=[[]],
		description=[[다음 룬석 중 하나를 선택할 수 있다: 
◇5개 파괴 룬석-Ⅲ, 룬 공격을 증가할 수 있다
◇5개 성령 룬석-Ⅲ, 룬 체력을 증가할 수 있다
◇5개 광휘 룬석-Ⅲ, 룬 이상 저항과 치명타를 증가할 수 있다
◇5개 금단 룬석-Ⅲ, 룬석 이상 명중과 치명타 피해를 증가할 수 있다]],
		id=212030,
		iospayid=[[]],
		item_id=14253,
		mark=0,
		name=[[3레벨 룬석 선물 가방]],
		payid=[[]],
		recharge=0,
		sortId=21,
		vip=0,
	},
	[214001]={
		coin_count=16,
		coin_typ=12,
		cycle_type=[[]],
		description=[[오픈 후 50개 성몽광, 장비 돌파에 필요한 기본 재료를 획득한다]],
		id=214001,
		iospayid=[[]],
		item_id=13221,
		mark=0,
		name=[[성몽광 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=1,
		vip=0,
	},
	[214002]={
		coin_count=20,
		coin_typ=12,
		cycle_type=[[]],
		description=[[오픈 후 10개 쉬령운정 획득해 장비 쉬령에 사용할 수 있다]],
		id=214002,
		iospayid=[[]],
		item_id=13222,
		mark=0,
		name=[[쉬령운정 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=2,
		vip=0,
	},
	[214003]={
		coin_count=10,
		coin_typ=12,
		cycle_type=[[]],
		description=[[큰 고기 찐빵,고기 진빵 20개 획득，제공할 수 있는 경험치는 다른 N 카드의 1.5배로 좋은 레벨업 재료입니다.]],
		id=214003,
		iospayid=[[]],
		item_id=13223,
		mark=2,
		name=[[고기 찐빵 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=3,
		vip=0,
	},
	[214004]={
		coin_count=67,
		coin_typ=12,
		cycle_type=[[]],
		description=[[오픈 후 10개 영월광 획득해 40레벨 이상인 장비 돌파에 사용할 수 있다]],
		id=214004,
		iospayid=[[]],
		item_id=13224,
		mark=0,
		name=[[월영광 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=4,
		vip=0,
	},
	[214005]={
		coin_count=43,
		coin_typ=12,
		cycle_type=[[]],
		description=[[다음 보석 중 하나를 선택할 수 있다: 
◇공격력 증가하는 4레벨 비홍 보석 10개
◇치명타 확률 증가하는 4레벨 황금 보석 10개
◇증속하는 4레벨 질풍 보석 10개]],
		id=214005,
		iospayid=[[]],
		item_id=13214,
		mark=3,
		name=[[공격 보석 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=5,
		vip=0,
	},
	[214006]={
		coin_count=22,
		coin_typ=12,
		cycle_type=[[]],
		description=[[다음 보석 중 하나를 선택할 수 있다: 
◇저항 확률 증가하는 4레벨 팔운 보석 10개
◇방어력 증가하는 4레벨 쌍생 보석 10개
◇HP 증가하는 4레벨 취성 보석 10개]],
		id=214006,
		iospayid=[[]],
		item_id=13215,
		mark=3,
		name=[[방어 보석 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=6,
		vip=0,
	},
	[214007]={
		coin_count=40,
		coin_typ=12,
		cycle_type=[[]],
		description=[[오픈 후 1 성 경험치 룬 10개 획득, 각 경험치 룬이 탐식하면 1000 포인트 경험치 제공]],
		id=214007,
		iospayid=[[]],
		item_id=13232,
		mark=2,
		name=[[경험치 부적 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=7,
		vip=0,
	},
	[214008]={
		coin_count=648,
		coin_typ=12,
		cycle_type=[[]],
		description=[[오픈 후 무기 원석 3개를 획득,신화 무기 제작에 사용]],
		id=214008,
		iospayid=[[]],
		item_id=13235,
		mark=0,
		name=[[무기 원석 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=8,
		vip=0,
	},
	[214009]={
		coin_count=285,
		coin_typ=12,
		cycle_type=[[]],
		description=[[다음 원석 중 하나를 선택할 수 있다: 
◇신화 목걸이 세트 제작에 목걸이 원석 10개 사용
◇신화 코스튬 세트 제작에 10개 의상 원석 사용
◇신화 반지 세트 제작에 10개 반지 원석 사용
◇신화 벨트 세트 제작에 10개 벨트 원석 사용
◇신화 신발 세트 제작에 10개 신발 원석 사용]],
		id=214009,
		iospayid=[[]],
		item_id=13236,
		mark=0,
		name=[[장비 원석 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=9,
		vip=0,
	},
	[214010]={
		coin_count=98,
		coin_typ=12,
		cycle_type=[[]],
		description=[[다음 마법 부여 재료 중 하나를 선택할 수 있다: 
◇10개 마법 부여-황, 합성한 장비 마법 부여[황]
◇10개 마법 부여-판, 합성한 장비 마법 부여[판]
◇10개 마법 부여-익, 합성한 장비 마법 부여[익]
◇10개 마법 부여-한, 합성한 장비 마법 부여[한]
◇10개 마법 부여-광, 합성한 장비 마법 부여[광]
◇10개 마법 부여-혼, 합성한 장비 마법 부여[혼] ]],
		id=214010,
		iospayid=[[]],
		item_id=13237,
		mark=0,
		name=[[마법 부여 재료 선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=10,
		vip=0,
	},
	[214011]={
		coin_count=180,
		coin_typ=12,
		cycle_type=[[week]],
		description=[[큰 팥 찐빵,팥 진빵 10개 획득，자체 레벨업에 필요한 경험치가 절반으로 줄어들어 성승급 재료로 재배하기에 적합합니다.]],
		id=214011,
		iospayid=[[]],
		item_id=13238,
		mark=4,
		name=[[팥 찐빵 선물 증정]],
		payid=[[]],
		recharge=2000,
		sortId=11,
		vip=0,
	},
	[214012]={
		coin_count=518,
		coin_typ=12,
		cycle_type=[[]],
		description=[[오픈 후 일발 혼입계약 1개와 마스터계약 10개를 얻을 수 있으며 일발혼입계약을 통해 영입하면 반드시 SSR 파트너를 얻을 수 있습니다.]],
		id=214012,
		iospayid=[[]],
		item_id=13239,
		mark=4,
		name=[[입혼선물 증정]],
		payid=[[]],
		recharge=0,
		sortId=12,
		vip=0,
	},
}

Currency={
	[1]={
		currency_type=1,
		emoji=[[#w2]],
		get_way={[1]=16,[2]=11,[3]=14,[4]=12,[5]=3,[6]=7,[7]=6,[8]=13,},
		icon=[[1003]],
		id=1,
		name=[[수정]],
		virtual_id=1003,
	},
	[2]={
		currency_type=2,
		emoji=[[#w1]],
		get_way={[1]=15,[2]=1,[3]=2,[4]=3,},
		icon=[[1002]],
		id=2,
		name=[[골드]],
		virtual_id=1002,
	},
	[3]={
		currency_type=3,
		emoji=[[#w3]],
		get_way={[1]=7,[2]=3,[3]=18,},
		icon=[[1011]],
		id=3,
		name=[[훈장]],
		virtual_id=1011,
	},
	[4]={
		currency_type=4,
		emoji=[[#w4]],
		get_way={[1]=8,},
		icon=[[1009]],
		id=4,
		name=[[영예]],
		virtual_id=1009,
	},
	[5]={
		currency_type=5,
		emoji=[[#w5]],
		get_way={[1]=6,},
		icon=[[1013]],
		id=5,
		name=[[활약]],
		virtual_id=1013,
	},
	[6]={
		currency_type=6,
		emoji=[[#w6]],
		get_way={[1]=9,[2]=13,},
		icon=[[1014]],
		id=6,
		name=[[기여]],
		virtual_id=1014,
	},
	[7]={
		currency_type=7,
		emoji=[[#w7]],
		get_way={},
		icon=[[1015]],
		id=7,
		name=[[길드 자금]],
		virtual_id=1015,
	},
	[8]={
		currency_type=8,
		emoji=[[#w8]],
		get_way={},
		icon=[[]],
		id=8,
		name=[[암뇌 탐색 포인트]],
		virtual_id=0,
	},
	[9]={
		currency_type=9,
		emoji=[[#w9]],
		get_way={[1]=4,},
		icon=[[1017]],
		id=9,
		name=[[스킨권]],
		virtual_id=1017,
	},
	[10]={
		currency_type=10,
		emoji=[[]],
		get_way={},
		icon=[[]],
		id=10,
		name=[[원정 포인트 ]],
		virtual_id=0,
	},
	[11]={
		currency_type=11,
		emoji=[[]],
		get_way={},
		icon=[[]],
		id=11,
		name=[[길드 명성 ]],
		virtual_id=0,
	},
	[12]={
		currency_type=12,
		emoji=[[#wa]],
		get_way={},
		icon=[[1001]],
		id=12,
		name=[[채정]],
		virtual_id=1001,
	},
	[13]={
		currency_type=13,
		emoji=[[#tl]],
		get_way={},
		icon=[[]],
		id=13,
		name=[[인민폐]],
		virtual_id=0,
	},
}

StorePage={
	[1]={
		icon=[[text_zahuo1]],
		id=1,
		name=[[잡화]],
		selected_icon=[[text_zahuo2]],
		sortId=1,
		subId={[1]=208,[2]=204,[3]=205,[4]=207,},
	},
	[2]={
		icon=[[text_jinbi1]],
		id=2,
		name=[[골드]],
		selected_icon=[[text_jinbi2]],
		sortId=2,
		subId={[1]=203,},
	},
	[3]={
		icon=[[text_pifu1]],
		id=3,
		name=[[스킨]],
		selected_icon=[[text_pifu2]],
		sortId=3,
		subId={[1]=210,},
	},
	[4]={
		icon=[[text_libao1]],
		id=4,
		name=[[패키지]],
		selected_icon=[[text_libao2]],
		sortId=4,
		subId={[1]=212,[2]=201,[3]=202,},
	},
}

StoreTag={
	[201]={
		coin_typ=1,
		icon=[[text_cebiaoqian_zhuangbei1]],
		id=201,
		name=[[준비]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[text_cebiaoqian_zhuangbei2]],
		show_limit=0,
		storepage=4,
	},
	[202]={
		coin_typ=1,
		icon=[[text_cebiaoqian_huoban1]],
		id=202,
		name=[[파트너]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[text_cebiaoqian_huoban2]],
		show_limit=0,
		storepage=4,
	},
	[203]={
		coin_typ=2,
		icon=[[]],
		id=203,
		name=[[전부]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[]],
		show_limit=0,
		storepage=2,
	},
	[204]={
		coin_typ=3,
		icon=[[text_cebiaoqian_xunzhang1]],
		id=204,
		name=[[훈장]],
		open_grade=1,
		refresh_rule=1003,
		refresh_tips=[[【매일 0시 자동리셋】]],
		select_item=10,
		selected_icon=[[text_cebiaoqian_xunzhang2]],
		show_limit=1,
		storepage=1,
	},
	[205]={
		coin_typ=4,
		icon=[[text_cebiaoqian_yongyu1]],
		id=205,
		name=[[영예]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[text_cebiaoqian_yongyu2]],
		show_limit=1,
		storepage=1,
	},
	[206]={
		coin_typ=2,
		icon=[[]],
		id=206,
		name=[[전부]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[]],
		show_limit=1,
	},
	[207]={
		coin_typ=6,
		icon=[[text_cebiaoqian_gongxian1]],
		id=207,
		name=[[공헌]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[text_cebiaoqian_gongxian2]],
		show_limit=0,
		storepage=1,
	},
	[208]={
		coin_typ=5,
		icon=[[text_cebiaoqian_huoyue1]],
		id=208,
		name=[[활약]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[text_cebiaoqian_huoyue2]],
		show_limit=1,
		storepage=1,
	},
	[209]={
		coin_typ=7,
		icon=[[]],
		id=209,
		name=[[길드 혜택]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[]],
		show_limit=1,
	},
	[210]={
		coin_typ=9,
		icon=[[]],
		id=210,
		name=[[즐겨찾기]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[스킨권은 성상도、상점 등 여러 가지 방법으로 획득 가능]],
		select_item=0,
		selected_icon=[[]],
		show_limit=0,
		storepage=3,
	},
	[211]={
		coin_typ=10,
		icon=[[]],
		id=211,
		name=[[신비한 상인]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[]],
		show_limit=0,
	},
	[212]={
		coin_typ=1,
		icon=[[text_cebiaoqian_changyong1]],
		id=212,
		name=[[즐겨찾기]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[text_cebiaoqian_changyong2]],
		show_limit=0,
		storepage=4,
	},
	[213]={
		coin_typ=2,
		icon=[[text_cebiaoqian_jinbi1]],
		id=213,
		name=[[골드]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[text_cebiaoqian_jinbi2]],
		show_limit=0,
	},
	[214]={
		coin_typ=12,
		icon=[[]],
		id=214,
		name=[[컬러 수정]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[]],
		show_limit=0,
	},
	[215]={
		coin_typ=12,
		icon=[[]],
		id=215,
		name=[[한정]],
		open_grade=1,
		refresh_rule=0,
		refresh_tips=[[]],
		select_item=0,
		selected_icon=[[]],
		show_limit=0,
	},
}

PageSort={[1]=1,[2]=2,[3]=3,[4]=4,}

GoodsDataSort={
	[201]={
		[1]=201001,
		[2]=201002,
		[3]=201003,
		[4]=201004,
		[5]=201005,
		[6]=201006,
		[7]=201007,
		[8]=201008,
		[9]=201009,
		[10]=201010,
	},
	[202]={
		[1]=202001,
		[2]=202002,
		[3]=202003,
		[4]=202004,
		[5]=202005,
		[6]=202006,
		[7]=202007,
		[8]=202008,
		[9]=202009,
		[10]=202010,
		[11]=202011,
		[12]=202012,
		[13]=202013,
	},
	[203]={
		[1]=203001,
		[2]=203002,
		[3]=203003,
		[4]=203004,
		[5]=203013,
		[6]=203014,
		[7]=203015,
		[8]=203016,
		[9]=203017,
		[10]=203018,
		[11]=203019,
		[12]=203020,
		[13]=203005,
		[14]=203006,
		[15]=203007,
		[16]=203008,
		[17]=203009,
		[18]=203010,
		[19]=203011,
		[20]=203012,
	},
	[204]={
		[1]=204001,
		[2]=204002,
		[3]=204003,
		[4]=204004,
		[5]=204005,
		[6]=204007,
		[7]=204006,
		[8]=204008,
		[9]=204009,
		[10]=204010,
		[11]=204011,
		[12]=204012,
		[13]=204013,
		[14]=204014,
		[15]=204015,
		[16]=204016,
		[17]=204017,
		[18]=204018,
		[19]=204019,
		[20]=204020,
		[21]=204021,
		[22]=204022,
		[23]=204023,
		[24]=204024,
		[25]=204025,
		[26]=204026,
		[27]=204027,
		[28]=204028,
		[29]=204029,
		[30]=204030,
		[31]=204031,
		[32]=204032,
		[33]=204033,
		[34]=204034,
		[35]=204035,
		[36]=204036,
		[37]=204037,
		[38]=204038,
		[39]=204039,
		[40]=204040,
		[41]=204041,
		[42]=204042,
		[43]=204043,
		[44]=204044,
		[45]=204045,
		[46]=204046,
		[47]=204047,
	},
	[205]={
		[1]=205001,
		[2]=205002,
		[3]=205003,
		[4]=205004,
		[5]=205005,
		[6]=205006,
		[7]=205007,
		[8]=205008,
		[9]=205009,
		[10]=205010,
		[11]=205011,
		[12]=205012,
		[13]=205013,
		[14]=205014,
		[15]=205015,
	},
	[206]={[1]=206001,[2]=206002,[3]=206003,},
	[207]={
		[1]=207001,
		[2]=207002,
		[3]=207003,
		[4]=207004,
		[5]=207005,
		[6]=207006,
		[7]=207007,
		[8]=207008,
		[9]=207009,
		[10]=207010,
		[11]=207011,
		[12]=207012,
		[13]=207013,
		[14]=207014,
	},
	[208]={[1]=208006,[2]=208005,[3]=208004,[4]=208007,},
	[209]={[1]=209003,[2]=209004,[3]=209005,},
	[210]={
		[1]=210001,
		[2]=210002,
		[3]=210003,
		[4]=210004,
		[5]=210005,
		[6]=210006,
		[7]=210007,
		[8]=210008,
		[9]=210011,
		[10]=210012,
		[11]=210013,
		[12]=210014,
		[13]=210015,
	},
	[211]={
		[1]=211001,
		[2]=211002,
		[3]=211003,
		[4]=211004,
		[5]=211005,
		[6]=211006,
		[7]=211007,
		[8]=211009,
		[9]=211010,
		[10]=211011,
		[11]=211012,
		[12]=211013,
		[13]=211014,
		[14]=211015,
		[15]=211016,
		[16]=211017,
		[17]=211018,
		[18]=211019,
		[19]=211020,
		[20]=211021,
	},
	[212]={
		[1]=212017,
		[2]=212018,
		[3]=212019,
		[4]=212020,
		[5]=212021,
		[6]=212022,
		[7]=212023,
		[8]=212016,
		[9]=212024,
		[10]=212025,
		[11]=212027,
		[12]=212005,
		[13]=212026,
		[14]=212004,
		[15]=212006,
		[16]=212007,
		[17]=212010,
		[18]=212011,
		[19]=212028,
		[20]=212029,
		[21]=212030,
		[22]=212015,
		[23]=212002,
		[24]=212003,
		[25]=212008,
		[26]=212009,
		[27]=212001,
		[28]=212012,
		[29]=212013,
		[30]=212014,
	},
	[214]={
		[1]=214001,
		[2]=214002,
		[3]=214003,
		[4]=214004,
		[5]=214005,
		[6]=214006,
		[7]=214007,
		[8]=214008,
		[9]=214009,
		[10]=214010,
		[11]=214011,
		[12]=214012,
	},
}

CurrencyGuide={
	[1]={
		blockkey=[[task]],
		cls_name=[[CTaskMainView]],
		control_key=[[task]],
		desc=[[퀘스트를 완료하면 획득할 수 있습니다 ]],
		go_inwar=1,
		id=1,
		name=[[의뢰]],
		need_close=0,
		open_id=1012,
	},
	[2]={
		blockkey=[[trapmine]],
		cls_name=[[CMapMainView]],
		control_key=[[trapmine]],
		desc=[[탐색 전투에서 획득할 수 있습니다]],
		go_inwar=1,
		id=2,
		name=[[탐색]],
		need_close=0,
		open_id=1011,
	},
	[3]={
		blockkey=[[pata]],
		cls_name=[[CPaTaView]],
		control_key=[[pata]],
		desc=[[지하감옥에서 획득할 수 있습니다]],
		go_inwar=1,
		id=3,
		name=[[감옥]],
		need_close=0,
		open_id=1003,
	},
	[4]={
		blockkey=[[treasure]],
		cls_name=[[CTreasureDescView]],
		control_key=[[treasure]],
		desc=[[성상도 보물상자에서 획득할 수 있습니다]],
		go_inwar=1,
		id=4,
		name=[[성상도]],
		need_close=0,
		open_id=1010,
	},
	[5]={
		blockkey=[[shop]],
		cls_name=[[CNpcShopView]],
		control_key=[[shop]],
		desc=[[잡화 훈장 페이지에서 구매할 수 있습니다]],
		go_inwar=0,
		id=5,
		name=[[훈장상점]],
		need_close=0,
		open_id=504,
	},
	[6]={
		blockkey=[[schedule]],
		cls_name=[[CScheduleMainView]],
		control_key=[[schedule]],
		desc=[[일일 퀘스트를 완료하면 획득할 수 있습니다]],
		go_inwar=0,
		id=6,
		name=[[매일필수 ]],
		need_close=0,
		open_id=702,
	},
	[7]={
		blockkey=[[worldboss]],
		cls_name=[[CWorldBossView]],
		control_key=[[worldboss]],
		desc=[[봉인된 곳을 클리어하여 획득할 수 있습니다]],
		go_inwar=1,
		id=7,
		name=[[봉인된 곳]],
		need_close=0,
		open_id=1006,
	},
	[8]={
		blockkey=[[arenagame]],
		cls_name=[[CArenaView]],
		control_key=[[arenagame]],
		desc=[[경기 플레이에서 영예를 획득할 수 있습니다]],
		go_inwar=1,
		id=8,
		name=[[경기]],
		need_close=0,
		open_id=1056,
	},
	[9]={
		blockkey=[[org]],
		cls_name=[[COrgChamberView]],
		control_key=[[org]],
		desc=[[길드 건설을 완료하면 획득할 수 있습니다]],
		go_inwar=1,
		id=9,
		name=[[길드건설]],
		need_close=0,
		open_id=601,
	},
	[10]={
		blockkey=[[shop]],
		cls_name=[[]],
		control_key=[[shop]],
		desc=[[채경을 사용해 직접 교환할 수 있습니다]],
		go_inwar=1,
		id=10,
		name=[[교환]],
		need_close=1,
		open_id=1020,
	},
	[11]={
		blockkey=[[welfare]],
		cls_name=[[CWelfareView]],
		control_key=[[welfare]],
		desc=[[월간 카드에서 매일 획득할 수 있습니다]],
		go_inwar=0,
		id=11,
		name=[[월정액]],
		need_close=0,
		open_id=1018,
	},
	[12]={
		blockkey=[[achieve]],
		cls_name=[[CAchieveMainView]],
		control_key=[[achieve]],
		desc=[[업적을 달성하면 수정을 획득할 수 있습니다]],
		go_inwar=0,
		id=12,
		name=[[업적]],
		need_close=0,
		open_id=1016,
	},
	[13]={
		blockkey=[[org]],
		cls_name=[[COrgActivityCenterView]],
		control_key=[[org]],
		desc=[[길드 이벤트에 참여하면 획득할 수 있습니다]],
		go_inwar=1,
		id=13,
		name=[[길드이벤트]],
		need_close=0,
		open_id=603,
	},
	[14]={
		blockkey=[[welfare]],
		cls_name=[[CWelfareView]],
		control_key=[[welfare]],
		desc=[[성장기금을 구매하면 획득할 수 있습니다]],
		go_inwar=0,
		id=14,
		name=[[성장기금]],
		need_close=0,
		open_id=1019,
	},
	[15]={
		blockkey=[[shop]],
		cls_name=[[CExchangeCoinView]],
		control_key=[[shop]],
		desc=[[수정을 사용해 직접 교환할 수 있습니다]],
		go_inwar=0,
		id=15,
		name=[[교환]],
		need_close=1,
		open_id=1017,
	},
	[16]={
		blockkey=[[shop]],
		cls_name=[[]],
		control_key=[[shop]],
		desc=[[충전으로 수정을 구매할 수 있습니다]],
		go_inwar=0,
		id=16,
		name=[[충전]],
		need_close=1,
		open_id=1034,
	},
	[17]={
		blockkey=[[shop]],
		cls_name=[[]],
		control_key=[[shop]],
		desc=[[패키지 구매로 획득할 수 있습니다]],
		go_inwar=0,
		id=17,
		name=[[패키지]],
		need_close=1,
		open_id=512,
	},
	[18]={
		blockkey=[[MonsterAtk]],
		cls_name=[[CMonsterAtkCityMainView]],
		control_key=[[msattack]],
		desc=[[몬스터공성에서 훈장을 획득할 수 있습니다]],
		go_inwar=1,
		id=18,
		name=[[제국침공]],
		need_close=0,
		open_id=1036,
	},
}

GetWay={
	coin={get_way={[1]=178,[2]=48,[3]=22,[4]=14,},id=[[coin]],},
	goldcoin={get_way={[1]=47,[2]=173,[3]=14,},id=[[goldcoin]],},
}

RechargeStore={
	[1001]={
		RMB=33500,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[1]],
		id=1001,
		mark=1,
		name=[[#w270]],
		payid=[[com.kaopu.ylq.6]],
		platform=1,
		random_talk={[1]=1,},
		sort_id=1,
	},
	[1002]={
		RMB=3000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[2]],
		id=1002,
		mark=1,
		name=[[#w2150]],
		payid=[[com.kaopu.ylq.12]],
		platform=1,
		random_talk={[1]=1,},
		sort_id=2,
	},
	[1003]={
		RMB=7500,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[3]],
		id=1003,
		mark=1,
		name=[[#w2380]],
		payid=[[com.kaopu.ylq.30]],
		platform=1,
		random_talk={[1]=1,},
		sort_id=3,
	},
	[1004]={
		RMB=17000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[4]],
		id=1004,
		mark=1,
		name=[[#w2880]],
		payid=[[com.kaopu.ylq.68]],
		platform=1,
		random_talk={[1]=1,},
		sort_id=4,
	},
	[1005]={
		RMB=29000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[5]],
		id=1005,
		mark=1,
		name=[[#w21600]],
		payid=[[com.kaopu.ylq.128]],
		platform=1,
		random_talk={[1]=1,},
		sort_id=5,
	},
	[1006]={
		RMB=68000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[7]],
		id=1006,
		mark=1,
		name=[[#w23880]],
		payid=[[com.kaopu.ylq.328]],
		platform=1,
		random_talk={[1]=1,},
		sort_id=6,
	},
	[1007]={
		RMB=129000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[6]],
		id=1007,
		mark=1,
		name=[[#w28080]],
		payid=[[com.kaopu.ylq.648]],
		platform=1,
		random_talk={[1]=1,},
		sort_id=7,
	},
	[1009]={
		RMB=7500,
		desc=[[]],
		front_str=[[]],
		icon=[[8]],
		id=1009,
		mark=2,
		name=[[월정액]],
		payid=[[com.kaopu.ylq.yk]],
		platform=1,
		random_talk={[1]=2,},
		sort_id=9,
	},
	[1010]={
		RMB=22000,
		desc=[[]],
		front_str=[[]],
		icon=[[9]],
		id=1010,
		mark=3,
		name=[[영구권]],
		payid=[[com.kaopu.ylq.zsk]],
		platform=1,
		random_talk={[1]=3,},
		sort_id=10,
	},
	[1011]={
		RMB=22000,
		desc=[[]],
		front_str=[[]],
		icon=[[10]],
		id=1011,
		mark=4,
		name=[[성장기금]],
		payid=[[com.kaopu.ylq.czjj]],
		platform=1,
		random_talk={[1]=4,},
		sort_id=11,
	},
	[2001]={
		RMB=1500,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[1]],
		id=2001,
		mark=1,
		name=[[#w270]],
		payid=[[com.kaopu.ylq.appstore.6]],
		platform=2,
		random_talk={[1]=1,},
		sort_id=1,
	},
	[2002]={
		RMB=3000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[2]],
		id=2002,
		mark=1,
		name=[[#w2150]],
		payid=[[com.kaopu.ylq.appstore.12]],
		platform=2,
		random_talk={[1]=1,},
		sort_id=2,
	},
	[2003]={
		RMB=7500,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[3]],
		id=2003,
		mark=1,
		name=[[#w2380]],
		payid=[[com.kaopu.ylq.appstore.30]],
		platform=2,
		random_talk={[1]=1,},
		sort_id=3,
	},
	[2004]={
		RMB=17000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[4]],
		id=2004,
		mark=1,
		name=[[#w2880]],
		payid=[[com.kaopu.ylq.appstore.68]],
		platform=2,
		random_talk={[1]=1,},
		sort_id=4,
	},
	[2005]={
		RMB=29000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[5]],
		id=2005,
		mark=1,
		name=[[#w21600]],
		payid=[[com.kaopu.ylq.appstore.128]],
		platform=2,
		random_talk={[1]=1,},
		sort_id=5,
	},
	[2006]={
		RMB=68000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[7]],
		id=2006,
		mark=1,
		name=[[#w23880]],
		payid=[[com.kaopu.ylq.appstore.328]],
		platform=2,
		random_talk={[1]=1,},
		sort_id=6,
	},
	[2007]={
		RMB=129000,
		desc=[[]],
		front_str=[[goldcoinstore_]],
		icon=[[6]],
		id=2007,
		mark=1,
		name=[[#w28080]],
		payid=[[com.kaopu.ylq.appstore.648]],
		platform=2,
		random_talk={[1]=1,},
		sort_id=7,
	},
	[2008]={
		RMB=7500,
		desc=[[즉시 획득#w2300매일#w2100수령할 수 있습니다]],
		front_str=[[]],
		icon=[[8]],
		id=2008,
		mark=2,
		name=[[월정액]],
		payid=[[com.kaopu.ylq.appstore.yk]],
		platform=2,
		random_talk={[1]=2,},
		sort_id=9,
	},
	[2009]={
		RMB=22000,
		desc=[[즉식 획득#w2980매일#w2110수령할 수 있습니다]],
		front_str=[[]],
		icon=[[9]],
		id=2009,
		mark=3,
		name=[[영구권]],
		payid=[[com.kaopu.ylq.appstore.zsk]],
		platform=2,
		random_talk={[1]=3,},
		sort_id=10,
	},
	[2010]={
		RMB=22000,
		desc=[[레벨이 오를 수록 7배 반환합니다#w2]],
		front_str=[[]],
		icon=[[10]],
		id=2010,
		mark=4,
		name=[[성장기금]],
		payid=[[com.kaopu.ylq.appstore.czjj]],
		platform=2,
		random_talk={[1]=4,},
		sort_id=11,
	},
}

RandomTalk={
	[1]={content=[[사장님 감사합니다.~~]],id=1,},
	[2]={
		content=[[월정액 카드 구매해 주셔서 감사합니다，매일 100개의 수정을 받는 것 외에도 다른 특권도 누릴 수 있습니다.]],
		id=2,
	},
	[3]={
		content=[[영구권 구매해 주셔서 감사합니다，매일 110개의 수정을 받는 것 외에도 다른 특권도 누릴 수 있습니다.]],
		id=3,
	},
	[4]={
		content=[[성장기금을 구매해 주셔서 감사합니다，보상을 받으러 가세요.]],
		id=4,
	},
}

RechargeQQ={
	kaopu={channel_name=[[kaopu]],qq=[[800803007]],},
	sm={channel_name=[[sm]],qq=[[800032225]],},
}
