-- 测试强制更新功能
-- 设置环境变量
os.execute("set FORCE_UPDATE=1")

-- 执行客户端转换
require("client.convert._common")

print("=== 强制更新测试 ===")

-- 直接设置强制更新
local original_SaveToFile = SaveToFile
SaveToFile = function(filename, s)
    return original_SaveToFile(filename, s, true) -- 强制更新
end

-- 测试几个模块
local test_modules = {"global", "item"}

for _, module_name in ipairs(test_modules) do
    print(string.format("=== 测试模块: %s ===", module_name))
    
    local success, module = pcall(function()
        return require("client.convert." .. module_name)
    end)
    
    if success and module.main then
        local r1, r2 = pcall(function()
            module.main()
        end)
        if not r1 then
            print("ERROR:", module_name, r2)
        else
            print("SUCCESS:", module_name, "强制更新完成")
        end
    else
        print("ERROR: 无法加载模块", module_name)
    end
end

print("=== 测试完成 ===")
