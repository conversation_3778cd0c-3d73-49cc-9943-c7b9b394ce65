-- ./excel/log/treasure.xlsx
return {

    ["init_mapinfo"] = {
        desc = "보물지도좌표생성",
        log_format = {["itemid"] = {["id"] = "itemid", ["desc"] = "보물지도 아이템ID"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저ID"}, ["posinfo"] = {["id"] = "posinfo", ["desc"] = "보물지도좌표정보"}},
        subtype = "init_mapinfo",
    },

    ["find_treasure"] = {
        desc = "보물파기",
        log_format = {["itemid"] = {["id"] = "itemid", ["desc"] = "보물지도 아이템ID"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저ID"}, ["posinfo"] = {["id"] = "posinfo", ["desc"] = "보물찾기좌표정보"}, ["rewardinfo"] = {["id"] = "rewardinfo", ["desc"] = "사건발생정보"}, ["times"] = {["id"] = "times", ["desc"] = "보물찾기횟수"}},
        subtype = "find_treasure",
    },

    ["playerboy_changepos"] = {
        desc = "개구쟁이보물상자 위치 랜덤",
        log_format = {["newinfo"] = {["id"] = "newinfo", ["desc"] = "위치 변경 후 보상정보"}, ["npcid"] = {["id"] = "npcid", ["desc"] = "npcid"}, ["oldinfo"] = {["id"] = "oldinfo", ["desc"] = "위치 변경 전 보상정보"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저ID"}},
        subtype = "playerboy_changepos",
    },

    ["playerboy_getreward"] = {
        desc = "개구쟁이보물상자 오픈",
        log_format = {["index"] = {["id"] = "index", ["desc"] = "보물상자 위치"}, ["npcid"] = {["id"] = "npcid", ["desc"] = "npcid"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저ID"}, ["rewardidx"] = {["id"] = "rewardidx", ["desc"] = "아이템 획득"}, ["times"] = {["id"] = "times", ["desc"] = "보물상자 오픈 횟수"}},
        subtype = "playerboy_getreward",
    },

    ["trigger_playerboy"] = {
        desc = "개구쟁이보물상자 발견",
        log_format = {["itemid"] = {["id"] = "itemid", ["desc"] = "보물지도 아이템ID"}, ["npcid"] = {["id"] = "npcid", ["desc"] = "npcid"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저ID"}, ["posinfo"] = {["id"] = "posinfo", ["desc"] = "개구쟁이좌표정보"}},
        subtype = "trigger_playerboy",
    },

    ["trigger_legendboy"] = {
        desc = "전설동료 발견",
        log_format = {["itemid"] = {["id"] = "itemid", ["desc"] = "보물지도 아이템ID"}, ["npcid"] = {["id"] = "npcid", ["desc"] = "npcid"}, ["pid"] = {["id"] = "pid", ["desc"] = "유저ID"}, ["posinfo"] = {["id"] = "posinfo", ["desc"] = "개구쟁이좌표정보"}},
        subtype = "trigger_legendboy",
    },

    ["legendboy_enter"] = {
        desc = "전설동료 던전 시작",
        log_format = {["enterplayer"] = {["id"] = "enterplayer", ["desc"] = "던전 진입 유저"}, ["gameid"] = {["id"] = "gameid", ["desc"] = "가위바위보 던전 컨텐츠id"}, ["npcid"] = {["id"] = "npcid", ["desc"] = "npcid"}},
        subtype = "legendboy_enter",
    },

    ["caiquan_gamestart"] = {
        desc = "가위바위보 시작",
        log_format = {["gameid"] = {["id"] = "gameid", ["desc"] = "컨텐츠ID"}, ["npcid_list"] = {["id"] = "npcid_list", ["desc"] = "npcid리스트"}, ["player"] = {["id"] = "player", ["desc"] = "컨텐츠참여한유저"}, ["sceneid"] = {["id"] = "sceneid", ["desc"] = "던전 배경id"}},
        subtype = "caiquan_gamestart",
    },

    ["caiquan_gameend"] = {
        desc = "가위바위보 종료",
        log_format = {["gameid"] = {["id"] = "gameid", ["desc"] = "컨텐츠ID"}, ["player"] = {["id"] = "player", ["desc"] = "컨텐츠참여한유저"}, ["reason"] = {["id"] = "reason", ["desc"] = "사유"}, ["result"] = {["id"] = "result", ["desc"] = "컨텐츠 결과"}, ["sceneid"] = {["id"] = "sceneid", ["desc"] = "던전 배경id"}},
        subtype = "caiquan_gameend",
    },

    ["caiquannpc_event"] = {
        desc = "가위바위보npc사건（도전，승리，패배，무승부및양측 손가락모양）",
        log_format = {["event"] = {["id"] = "event", ["desc"] = "이벤트"}, ["gameid"] = {["id"] = "gameid", ["desc"] = "컨텐츠ID"}, ["npcid"] = {["id"] = "npcid", ["desc"] = "npcid"}, ["pid"] = {["id"] = "pid", ["desc"] = "컨텐츠참여한유저"}, ["times"] = {["id"] = "times", ["desc"] = "횟수"}},
        subtype = "caiquannpc_event",
    },

    ["caiquan_reward"] = {
        desc = "가위바위보 보상 획득",
        log_format = {["gameid"] = {["id"] = "gameid", ["desc"] = "컨텐츠ID"}, ["pid"] = {["id"] = "pid", ["desc"] = "보상 수령한유저"}, ["playertype"] = {["id"] = "playertype", ["desc"] = "유저유형"}, ["rewardidx"] = {["id"] = "rewardidx", ["desc"] = "보상id"}, ["times"] = {["id"] = "times", ["desc"] = "횟수"}},
        subtype = "caiquan_reward",
    },

}
