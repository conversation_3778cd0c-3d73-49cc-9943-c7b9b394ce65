module(...)
--auto generate data
FuliReward={
	[100001]={
		condition_des=[[[9A928F]해당 테스트에서 다음 조건 중 하나를 달성해야 합니다: 
[159a80]◆7일 연속 로그인
◆캐릭터 55레벨 달성[-]
게임 오픈 베타에서 동일 계정으로 생성한 첫 캐릭터는 아래와 같은 보상을 획득합니다:
]],
		desc=[[클로즈 베타 혜택]],
		id=100001,
		name=[[클로즈 베타 혜택]],
		open=0,
		reward={
			[1]={num=1,sid=[[1003(value=1000)]],},
			[2]={num=50,sid=[[14011]],},
		},
		time_des=[[2018년 6월 6일-2018년 6월 13일]],
		title_reward=1083,
	},
}

TotalRecharge={
	[10001]={
		condition=500,
		id=10001,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=88888)]],},
			[2]={num=3,sid=[[10040]],},
			[3]={num=10,sid=[[20503]],},
		},
		text=0,
		title=0,
	},
	[10002]={
		condition=1000,
		id=10002,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=120000)]],},
			[2]={num=1,sid=[[205032]],},
			[3]={num=20,sid=[[20503]],},
		},
		text=0,
		title=0,
	},
	[10003]={
		condition=2000,
		id=10003,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=200000)]],},
			[2]={num=30,sid=[[14011]],},
			[3]={num=35,sid=[[20503]],},
		},
		text=0,
		title=0,
	},
	[10004]={
		condition=5000,
		id=10004,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=300000)]],},
			[2]={num=2,sid=[[13270]],},
			[3]={num=40,sid=[[20503]],},
		},
		text=0,
		title=0,
	},
	[10005]={
		condition=10000,
		id=10005,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=1000000)]],},
			[2]={num=80,sid=[[14011]],},
			[3]={num=45,sid=[[20503]],},
		},
		text=0,
		title=0,
	},
	[10006]={
		condition=20000,
		id=10006,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=1200000)]],},
			[2]={num=2,sid=[[13271]],},
			[3]={num=50,sid=[[20503]],},
		},
		text=0,
		title=0,
	},
	[10007]={
		condition=50000,
		id=10007,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=2000000)]],},
			[2]={num=1,sid=[[1027(gain_way=1)]],},
			[3]={num=55,sid=[[20503]],},
		},
		text=1002,
		title=0,
	},
	[10008]={
		condition=100000,
		id=10008,
		open=1,
		reward={[1]={num=120,sid=[[14011]],},[2]={num=60,sid=[[20503]],},},
		text=1003,
		title=1074,
	},
	[10009]={
		condition=200000,
		id=10009,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=5000000)]],},
			[2]={num=20,sid=[[14002]],},
			[3]={num=75,sid=[[20503]],},
		},
		text=0,
		title=0,
	},
	[10010]={
		condition=500000,
		id=10010,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=6000000)]],},
			[2]={num=500,sid=[[14011]],},
			[3]={num=30,sid=[[14002]],},
		},
		text=0,
		title=0,
	},
	[10011]={
		condition=1000000,
		id=10011,
		open=1,
		reward={[1]={num=1000,sid=[[14011]],},[2]={num=50,sid=[[14002]],},},
		text=1004,
		title=1073,
	},
}

WelfareControl={
	[1]={
		desc=[[]],
		grade=0,
		id=1,
		item_list={},
		name=[[베타혜택]],
		open=0,
		short_desc=[[]],
	},
	[2]={
		desc=[[]],
		grade=0,
		id=2,
		item_list={},
		name=[[일일출석]],
		open=0,
		short_desc=[[]],
	},
	[3]={
		desc=[[]],
		grade=0,
		id=3,
		item_list={},
		name=[[충전보너스]],
		open=1,
		short_desc=[[]],
	},
	[4]={
		desc=[[]],
		grade=0,
		id=4,
		item_list={},
		name=[[누적보상]],
		open=1,
		short_desc=[[]],
	},
	[5]={
		desc=[[]],
		grade=0,
		id=5,
		item_list={},
		name=[[모집보류]],
		open=0,
		short_desc=[[]],
	},
	[6]={
		desc=[[]],
		grade=0,
		id=6,
		item_list={},
		name=[[7일목표]],
		open=1,
		short_desc=[[]],
	},
	[7]={
		desc=[[]],
		grade=0,
		id=7,
		item_list={},
		name=[[오프라인]],
		open=1,
		short_desc=[[]],
	},
	[8]={
		desc=[[]],
		grade=0,
		id=8,
		item_list={},
		name=[[성장기금]],
		open=1,
		short_desc=[[]],
	},
	[9]={
		desc=[[]],
		grade=0,
		id=9,
		item_list={[1]={amount=300,sid=[[1003]],},},
		name=[[월정액]],
		open=1,
		short_desc=[[친밀도육성 버프
무료소탕권 획득
더 많은 특권]],
	},
	[10]={
		desc=[[]],
		grade=0,
		id=10,
		item_list={
			[1]={amount=980,sid=[[1003]],},
			[2]={amount=5,sid=[[10040]],},
			[3]={amount=1,sid=[[13281]],},
		},
		name=[[영구권]],
		open=1,
		short_desc=[[장비＆어령던전 무료소탕
악몽던전 횟수 증가
더 많은 특권]],
	},
	[11]={
		desc=[[]],
		grade=9,
		id=11,
		item_list={},
		name=[[페스타]],
		open=1,
		short_desc=[[]],
	},
	[12]={
		desc=[[]],
		grade=0,
		id=12,
		item_list={},
		name=[[일일체력]],
		open=1,
		short_desc=[[]],
	},
	[13]={
		desc=[[]],
		grade=0,
		id=13,
		item_list={},
		name=[[쿠폰교환]],
		open=1,
		short_desc=[[]],
	},
	[14]={
		desc=[[]],
		grade=0,
		id=14,
		item_list={},
		name=[[충전보너스]],
		open=0,
		short_desc=[[]],
	},
	[15]={
		desc=[[]],
		grade=0,
		id=15,
		item_list={},
		name=[[소비보너스]],
		open=1,
		short_desc=[[]],
	},
	[16]={
		desc=[[]],
		grade=0,
		id=16,
		item_list={},
		name=[[투력랭킹]],
		open=0,
		short_desc=[[]],
	},
	[17]={
		desc=[[]],
		grade=0,
		id=17,
		item_list={},
		name=[[파트너랭킹]],
		open=0,
		short_desc=[[]],
	},
	[18]={
		desc=[[]],
		grade=0,
		id=18,
		item_list={},
		name=[[회원혜택]],
		open=1,
		short_desc=[[]],
	},
}

DailySign_Week={
	[1]={id=1,reward={amount=1,sid=[[10021]],},},
	[2]={id=2,reward={amount=2,sid=[[10021]],},},
	[3]={id=3,reward={amount=3,sid=[[10021]],},},
	[4]={id=4,reward={amount=4,sid=[[10021]],},},
	[5]={id=5,reward={amount=5,sid=[[10021]],},},
	[6]={id=6,reward={amount=6,sid=[[10021]],},},
	[7]={id=7,reward={amount=7,sid=[[10021]],},},
}

DailySign_Type={week={day=7,key=[[week]],},}

FirstCharge={
	[100001]={
		desc=[[첫충전]],
		id=100001,
		name=[[첫충전]],
		open=0,
		reward={
			[1]=1001,
			[2]=1002,
			[3]=1003,
			[4]=1004,
			[5]=1005,
			[6]=1006,
			[7]=1007,
		},
	},
}

RewardBack={
	[2001]={
		after_reward={[1]={num=5000,sid=[[1005]],},[2]={num=10000,sid=[[1002]],},},
		before_reward={
			[1]={num=10000,sid=[[1005]],},
			[2]={num=20000,sid=[[1002]],},
		},
		cost=30,
		desc=[[꼴지의역습]],
		free_reward={[1]=1001,[2]=1002,},
		gold_reward={[1]=1001,[2]=1002,},
		key=2001,
		limit=1,
		name=[[question]],
		open_limit=0,
	},
	[2002]={
		after_reward={[1]={num=5000,sid=[[1005]],},[2]={num=10000,sid=[[1002]],},},
		before_reward={
			[1]={num=10000,sid=[[1005]],},
			[2]={num=20000,sid=[[1002]],},
		},
		cost=30,
		desc=[[범생어디가]],
		free_reward={[1]=1001,[2]=1002,},
		gold_reward={[1]=1001,[2]=1002,},
		key=2002,
		limit=1,
		name=[[question2]],
		open_limit=0,
	},
	[2003]={
		after_reward={[1]={num=10000,sid=[[1002]],},[2]={num=100,sid=[[1011]],},},
		before_reward={[1]={num=20000,sid=[[1002]],},[2]={num=200,sid=[[1011]],},},
		cost=20,
		desc=[[봉인된 곳]],
		free_reward={[1]=1001,[2]=1003,},
		gold_reward={[1]=1001,[2]=1003,},
		key=2003,
		limit=1,
		name=[[worldboss]],
		open_limit=0,
	},
	[2004]={
		after_reward={[1]={num=10000,sid=[[1002]],},[2]={num=100,sid=[[1011]],},},
		before_reward={[1]={num=20000,sid=[[1002]],},[2]={num=200,sid=[[1011]],},},
		cost=20,
		desc=[[제국침공]],
		free_reward={[1]=1001,[2]=1003,},
		gold_reward={[1]=1001,[2]=1003,},
		key=2004,
		limit=1,
		name=[[msattack]],
		open_limit=0,
	},
	[2005]={
		after_reward={[1]={num=2500,sid=[[1005]],},[2]={num=12000,sid=[[1002]],},},
		before_reward={[1]={num=5000,sid=[[1005]],},[2]={num=24000,sid=[[1002]],},},
		cost=20,
		desc=[[제국기사단]],
		free_reward={[1]=2003,[2]=4001,},
		gold_reward={[1]=2003,[2]=4001,},
		key=2005,
		limit=3,
		name=[[convoy]],
		open_limit=1,
	},
	[2006]={
		after_reward={[1]={num=7500,sid=[[1005]],},[2]={num=1,sid=[[10024]],},},
		before_reward={[1]={num=15000,sid=[[1005]],},[2]={num=2,sid=[[10024]],},},
		cost=50,
		desc=[[순찰잡무]],
		free_reward={[1]=4002,[2]=5002,},
		gold_reward={[1]=4002,[2]=5002,},
		key=2006,
		limit=2,
		name=[[shimen]],
		open_limit=1,
	},
}

CostScoreData={
	[100001]={
		buy_limit=80,
		desc=[[연조각 획득]],
		id=100001,
		name=[[연조각]],
		open=1,
		point=1000,
		reward={num=1,sid=[[20407]],},
	},
	[100002]={
		buy_limit=80,
		desc=[[호현조각 획득]],
		id=100002,
		name=[[호현조각]],
		open=1,
		point=1000,
		reward={num=1,sid=[[20415]],},
	},
	[100003]={
		buy_limit=80,
		desc=[[견요조각 획득]],
		id=100003,
		name=[[견요조각]],
		open=1,
		point=1000,
		reward={num=1,sid=[[20409]],},
	},
	[100004]={
		buy_limit=0,
		desc=[[장비쉬령에 사용]],
		id=100004,
		name=[[쉬령운정]],
		open=1,
		point=125,
		reward={num=1,sid=[[11101]],},
	},
	[100005]={
		buy_limit=0,
		desc=[[장비단조에 사용]],
		id=100005,
		name=[[장비원석]],
		open=1,
		point=1250,
		reward={num=1,sid=[[11010]],},
	},
	[100006]={
		buy_limit=0,
		desc=[[무기 외 원석 1개 획득]],
		id=100006,
		name=[[원석상자]],
		open=1,
		point=1875,
		reward={num=1,sid=[[13213]],},
	},
	[100007]={
		buy_limit=0,
		desc=[[영혼상자 오픈에 사용]],
		id=100007,
		name=[[영혼열쇠]],
		open=1,
		point=1875,
		reward={num=1,sid=[[10040]],},
	},
	[100008]={
		buy_limit=0,
		desc=[[무기단조에 사용]],
		id=100008,
		name=[[무기원석]],
		open=1,
		point=3750,
		reward={num=1,sid=[[11004]],},
	},
	[100009]={
		buy_limit=30,
		desc=[[만능조각 획득]],
		id=100009,
		name=[[만능조각]],
		open=1,
		point=2000,
		reward={num=1,sid=[[14002]],},
	},
	[100010]={
		buy_limit=100,
		desc=[[무기단조에 사용]],
		id=100010,
		name=[[장비원석]],
		open=1,
		point=500,
		reward={num=1,sid=[[11010]],},
	},
	[100011]={
		buy_limit=30,
		desc=[[무기 외 원석 1개 획득]],
		id=100011,
		name=[[원석상자]],
		open=1,
		point=500,
		reward={num=1,sid=[[13213]],},
	},
	[100012]={
		buy_limit=50,
		desc=[[영혼상자 오픈에 사용]],
		id=100012,
		name=[[영혼열쇠]],
		open=1,
		point=750,
		reward={num=1,sid=[[10040]],},
	},
	[100013]={
		buy_limit=180,
		desc=[[파트너 스킬 강화에 사용]],
		id=100013,
		name=[[카라멜진빵]],
		open=1,
		point=250,
		reward={num=1,sid=[[14011]],},
	},
	[100014]={
		buy_limit=50,
		desc=[[랜덤 파트너 조각 획득]],
		id=100014,
		name=[[파트너조각]],
		open=1,
		point=1500,
		reward={num=1,sid=[[13260]],},
	},
	[100015]={
		buy_limit=10,
		desc=[[무기단조에 사용]],
		id=100015,
		name=[[무기원석]],
		open=1,
		point=1500,
		reward={num=1,sid=[[11004]],},
	},
	[100016]={
		buy_limit=0,
		desc=[[1만골드 획득]],
		id=100016,
		name=[[1만골드]],
		open=1,
		point=125,
		reward={num=1,sid=[[13306]],},
	},
}

LuckyDrawData={
	[1]={
		desc=[[무기 외 원석 1개 획득]],
		id=1,
		item={[1]={idx=1,rate=10000,},},
		name=[[원석상자]],
		rate=10000,
		sid=12004,
		srate=500,
	},
	[2]={
		desc=[[룬강화에 사용]],
		id=2,
		item={[1]={idx=2,rate=10000,},},
		name=[[남색호박*10]],
		rate=10000,
		sid=14021,
		srate=2000,
	},
	[3]={
		desc=[[장비쉬령에 사용]],
		id=3,
		item={[1]={idx=22,rate=10000,},},
		name=[[쉬령운정*3]],
		rate=10000,
		sid=11101,
		srate=2000,
	},
	[4]={
		desc=[[5만골드 획득]],
		id=4,
		item={[1]={idx=23,rate=10000,},},
		name=[[5만골드]],
		rate=10000,
		sid=1002,
		srate=1000,
	},
	[5]={
		desc=[[영혼상자 오픈에 사용]],
		id=5,
		item={[1]={idx=24,rate=10000,},},
		name=[[영혼열쇠*1]],
		rate=10000,
		sid=10040,
		srate=800,
	},
	[6]={
		desc=[[입혼조각 획득]],
		id=6,
		item={[1]={idx=26,rate=10000,},},
		name=[[입혼조각]],
		rate=10000,
		sid=13212,
		srate=200,
	},
	[7]={
		desc=[[고기찐빵 30개 획득]],
		id=7,
		item={[1]={idx=25,rate=10000,},},
		name=[[고기찐빵*30]],
		rate=10000,
		sid=14001,
		srate=500,
	},
	[8]={
		desc=[[랜덤 파트너 조각 획득]],
		id=8,
		item={
			[1]={idx=100,rate=250,},
			[2]={idx=101,rate=250,},
			[3]={idx=102,rate=250,},
			[4]={idx=103,rate=250,},
			[5]={idx=104,rate=250,},
			[6]={idx=105,rate=250,},
			[7]={idx=106,rate=250,},
			[8]={idx=107,rate=250,},
			[9]={idx=108,rate=250,},
			[10]={idx=109,rate=250,},
			[11]={idx=110,rate=250,},
			[12]={idx=111,rate=250,},
			[13]={idx=112,rate=250,},
			[14]={idx=113,rate=250,},
			[15]={idx=114,rate=250,},
			[16]={idx=115,rate=250,},
			[17]={idx=116,rate=250,},
			[18]={idx=117,rate=250,},
			[19]={idx=118,rate=250,},
			[20]={idx=119,rate=250,},
			[21]={idx=120,rate=250,},
			[22]={idx=121,rate=250,},
			[23]={idx=122,rate=250,},
			[24]={idx=123,rate=250,},
			[25]={idx=124,rate=250,},
			[26]={idx=125,rate=250,},
			[27]={idx=126,rate=250,},
			[28]={idx=127,rate=250,},
			[29]={idx=128,rate=250,},
			[30]={idx=129,rate=250,},
			[31]={idx=130,rate=250,},
			[32]={idx=131,rate=250,},
			[33]={idx=132,rate=250,},
			[34]={idx=133,rate=250,},
			[35]={idx=134,rate=250,},
			[36]={idx=135,rate=250,},
			[37]={idx=136,rate=250,},
			[38]={idx=137,rate=250,},
			[39]={idx=138,rate=250,},
			[40]={idx=139,rate=250,},
		},
		name=[[랜덤조각]],
		rate=10000,
		sid=16010,
		srate=3000,
	},
}

RechargeScoreData={
	[1]={
		buy_limit=0,
		desc=[[뚜뚜루조각 획득]],
		id=1,
		name=[[뚜뚜루조각]],
		open=1,
		point=60,
		reward={num=1,sid=[[20418]],},
	},
	[2]={
		buy_limit=0,
		desc=[[영월조각 획득]],
		id=2,
		name=[[영월조각]],
		open=1,
		point=60,
		reward={num=1,sid=[[20405]],},
	},
	[3]={
		buy_limit=30,
		desc=[[무기 외 원석 1개 획득]],
		id=3,
		name=[[원석상자]],
		open=1,
		point=20,
		reward={num=1,sid=[[13213]],},
	},
	[4]={
		buy_limit=50,
		desc=[[영혼상자 오픈에 사용]],
		id=4,
		name=[[영혼열쇠]],
		open=1,
		point=30,
		reward={num=1,sid=[[10040]],},
	},
	[5]={
		buy_limit=100,
		desc=[[무기단조에 사용]],
		id=5,
		name=[[장비원석]],
		open=1,
		point=20,
		reward={num=1,sid=[[11010]],},
	},
	[6]={
		buy_limit=50,
		desc=[[랜덤 파트너 조각 획득]],
		id=6,
		name=[[랜덤조각]],
		open=1,
		point=60,
		reward={num=1,sid=[[13260]],},
	},
	[7]={
		buy_limit=10,
		desc=[[무기단조에 사용]],
		id=7,
		name=[[무기원석]],
		open=1,
		point=60,
		reward={num=1,sid=[[11004]],},
	},
	[8]={
		buy_limit=0,
		desc=[[1만골드 획득]],
		id=8,
		name=[[골드패키지]],
		open=1,
		point=5,
		reward={num=1,sid=[[13306]],},
	},
	[9]={
		buy_limit=30,
		desc=[[만능조각 획득]],
		id=9,
		name=[[마스터조각]],
		open=1,
		point=80,
		reward={num=1,sid=[[14002]],},
	},
	[10]={
		buy_limit=180,
		desc=[[파트너 스킬강화에 사용]],
		id=10,
		name=[[카라멜진빵]],
		open=1,
		point=10,
		reward={num=1,sid=[[14011]],},
	},
	[11]={
		buy_limit=30,
		desc=[[무기 외 원석 1개 획득]],
		id=11,
		name=[[원석상자]],
		open=1,
		point=20,
		reward={num=1,sid=[[13213]],},
	},
	[12]={
		buy_limit=50,
		desc=[[영혼상자 오픈에 사용]],
		id=12,
		name=[[영혼열쇠]],
		open=1,
		point=30,
		reward={num=1,sid=[[10040]],},
	},
	[13]={
		buy_limit=100,
		desc=[[무기단조에 사용]],
		id=13,
		name=[[장비원석]],
		open=1,
		point=20,
		reward={num=1,sid=[[11010]],},
	},
	[14]={
		buy_limit=50,
		desc=[[랜덤 파트너 조각 획득]],
		id=14,
		name=[[랜덤조각]],
		open=1,
		point=60,
		reward={num=1,sid=[[13260]],},
	},
	[15]={
		buy_limit=10,
		desc=[[무기단조에 사용]],
		id=15,
		name=[[무기원석]],
		open=1,
		point=60,
		reward={num=1,sid=[[11004]],},
	},
	[16]={
		buy_limit=0,
		desc=[[1만골드 획득]],
		id=16,
		name=[[골드패키지]],
		open=1,
		point=5,
		reward={num=1,sid=[[13306]],},
	},
}

RechargeScoreConfig={
	[1]={
		end_time=[[2018/5/27]],
		id=1,
		sale_item={[1]=1,[2]=2,[3]=3,[4]=4,[5]=5,[6]=6,[7]=7,[8]=8,},
		start_time=[[2018/5/19]],
	},
	[2]={
		end_time=[[2018/5/27]],
		id=2,
		sale_item={[1]=9,[2]=10,[3]=11,[4]=12,[5]=13,[6]=14,[7]=15,[8]=16,},
		start_time=[[2018/5/19]],
	},
}

CHARGE_REWARD={
	[1]={
		charge_rmb=272,
		limit=2,
		reward_id={[1]=1101,[2]=1102,[3]=1103,},
		schedule_id=1,
		sub_schedule_des=[[[9A928F]단일충전 [159a80]2720수정[9A928F] 시 획득]],
		sub_schedule_id=1,
	},
	[2]={
		charge_rmb=516,
		limit=2,
		reward_id={[1]=1104,[2]=1105,[3]=1106,},
		schedule_id=1,
		sub_schedule_des=[[[9A928F]단일충전 [159a80]5160수정[9A928F] 시 획득]],
		sub_schedule_id=2,
	},
	[3]={
		charge_rmb=272,
		limit=9999,
		reward_id={[1]=1201,[2]=1202,[3]=1203,},
		schedule_id=2,
		sub_schedule_des=[[[9A928F]단일충전 [159a80]2720수정[9A928F] 시 획득]],
		sub_schedule_id=1,
	},
	[4]={
		charge_rmb=516,
		limit=9999,
		reward_id={[1]=1204,[2]=1205,[3]=1206,},
		schedule_id=2,
		sub_schedule_des=[[[9A928F]단일충전 [159a80]5160수정[9A928F] 시 획득]],
		sub_schedule_id=2,
	},
}

CHARGE_REWARD_OPEN={
	[1]={
		schedule=1,
		schedule_des=[[[9A928F]이벤트 기간 단번에[159a80]2720、5160[9A928F]수정 충전 시 
보너스 증정！ 종류별 수령 가능 횟수[159a80]2[9A928F]회]],
	},
	[2]={
		schedule=2,
		schedule_des=[[[9A928F]이벤트 기간 단번에[159a80]2720、5160[9A928F]수정 충전 시 
보너스 증정！ ]],
	},
}

YiYuanLiBao={
	[1]={
		id=1,
		iospayid=[[com.kaopu.ylq.appstore.lb.1]],
		itemlist={[1]={amount=1,sid=[[10040]],},},
		payid=[[com.kaopu.ylq.lb.1]],
		plan=1,
		price=1,
	},
	[2]={
		id=2,
		iospayid=[[com.kaopu.ylq.appstore.lb.6]],
		itemlist={
			[1]={amount=1,sid=[[14002]],},
			[2]={amount=1,sid=[[18301]],},
			[3]={amount=10,sid=[[11101]],},
		},
		payid=[[com.kaopu.ylq.lb.6]],
		plan=1,
		price=1500,
	},
	[3]={
		id=3,
		iospayid=[[com.kaopu.ylq.appstore.lb.12]],
		itemlist={
			[1]={amount=10,sid=[[14011]],},
			[2]={amount=1,sid=[[18002]],},
			[3]={amount=1,sid=[[11205]],},
		},
		payid=[[com.kaopu.ylq.lb.12]],
		plan=1,
		price=3000,
	},
	[4]={
		id=4,
		iospayid=[[com.kaopu.ylq.appstore.lb.1]],
		itemlist={[1]={amount=1,sid=[[10040]],},},
		payid=[[com.kaopu.ylq.lb.1]],
		plan=2,
		price=1,
	},
	[7]={
		id=7,
		iospayid=[[com.kaopu.ylq.sg.30]],
		itemlist={
			[1]={amount=5,sid=[[11201]],},
			[2]={amount=5,sid=[[11202]],},
			[3]={amount=5,sid=[[11203]],},
			[4]={amount=5,sid=[[11204]],},
			[5]={amount=5,sid=[[11205]],},
			[6]={amount=5,sid=[[11206]],},
		},
		payid=[[com.kaopu.ylq.sg.30]],
		plan=2,
		price=7500,
	},
	[8]={
		id=8,
		iospayid=[[com.kaopu.ylq.qh.128]],
		itemlist={
			[1]={amount=120,sid=[[14021]],},
			[2]={amount=40,sid=[[14001]],},
			[3]={amount=20,sid=[[14011]],},
			[4]={amount=1,sid=[[13302]],},
		},
		payid=[[com.kaopu.ylq.qh.128]],
		plan=2,
		price=29000,
	},
	[9]={
		id=9,
		iospayid=[[com.kaopu.ylq.qh.328]],
		itemlist={
			[1]={amount=300,sid=[[14021]],},
			[2]={amount=100,sid=[[14001]],},
			[3]={amount=50,sid=[[14011]],},
			[4]={amount=1,sid=[[13303]],},
		},
		payid=[[com.kaopu.ylq.qh.328]],
		plan=2,
		price=68000,
	},
	[10]={
		id=10,
		iospayid=[[com.kaopu.ylq.zb.128]],
		itemlist={
			[1]={amount=2,sid=[[13221]],},
			[2]={amount=1,sid=[[13234]],},
			[3]={amount=2,sid=[[13215]],},
			[4]={amount=1,sid=[[13236]],},
		},
		payid=[[com.kaopu.ylq.zb.128]],
		plan=2,
		price=29000,
	},
	[11]={
		id=11,
		iospayid=[[com.kaopu.ylq.zb.328]],
		itemlist={
			[1]={amount=4,sid=[[13261]],},
			[2]={amount=2,sid=[[13234]],},
			[3]={amount=3,sid=[[13214]],},
			[4]={amount=3,sid=[[13235]],},
		},
		payid=[[com.kaopu.ylq.zb.328]],
		plan=2,
		price=68000,
	},
	[12]={
		id=12,
		iospayid=[[com.kaopu.ylq.pf.328]],
		itemlist={[1]={amount=20,sid=[[1017]],},},
		payid=[[com.kaopu.ylq.pf.328]],
		plan=2,
		price=68000,
	},
	[13]={
		id=13,
		iospayid=[[com.kaopu.ylq.pf.648]],
		itemlist={[1]={amount=40,sid=[[1017]],},},
		payid=[[com.kaopu.ylq.pf.648]],
		plan=2,
		price=129000,
	},
	[14]={
		id=14,
		iospayid=[[com.kaopu.ylq.rh.328]],
		itemlist={[1]={amount=20,sid=[[13212]],},},
		payid=[[com.kaopu.ylq.rh.328]],
		plan=2,
		price=68000,
	},
	[15]={
		id=15,
		iospayid=[[com.kaopu.ylq.rh.648]],
		itemlist={[1]={amount=40,sid=[[13212]],},},
		payid=[[com.kaopu.ylq.rh.648]],
		plan=2,
		price=129000,
	},
	[16]={
		id=16,
		iospayid=[[com.kaopu.ylq.yl.328]],
		itemlist={[1]={amount=2,sid=[[13271]],},},
		payid=[[com.kaopu.ylq.yl.328]],
		plan=2,
		price=68000,
	},
	[17]={
		id=17,
		iospayid=[[com.kaopu.ylq.yl.648]],
		itemlist={[1]={amount=4,sid=[[13271]],},},
		payid=[[com.kaopu.ylq.yl.648]],
		plan=2,
		price=129000,
	},
	[18]={
		id=18,
		iospayid=[[com.kaopu.ylq.wn.328]],
		itemlist={[1]={amount=15,sid=[[14002]],},},
		payid=[[com.kaopu.ylq.wn.328]],
		plan=2,
		price=68000,
	},
	[19]={
		id=19,
		iospayid=[[com.kaopu.ylq.wn.648]],
		itemlist={[1]={amount=30,sid=[[14002]],},},
		payid=[[com.kaopu.ylq.wn.648]],
		plan=2,
		price=129000,
	},
	[20]={
		id=20,
		iospayid=[[com.kaopu.ylq.jx.648]],
		itemlist={
			[1]={amount=1,sid=[[13100]],},
			[2]={amount=1000,sid=[[1003]],},
		},
		payid=[[com.kaopu.ylq.jx.648]],
		plan=2,
		price=129000,
	},
}

RushRecharge={
	[1]={
		id=1,
		itemlist={
			[1]={amount=2,sid=[[10040]],},
			[2]={amount=50,sid=[[11001]],},
			[3]={amount=1,sid=[[1002(value=50000)]],},
		},
		plan=1,
		progress=300,
	},
	[2]={
		id=2,
		itemlist={
			[1]={amount=5,sid=[[10040]],},
			[2]={amount=100,sid=[[11001]],},
			[3]={amount=1,sid=[[1002(value=80000)]],},
		},
		plan=1,
		progress=680,
	},
	[3]={
		id=3,
		itemlist={
			[1]={amount=1,sid=[[13270]],},
			[2]={amount=120,sid=[[14021]],},
			[3]={amount=1,sid=[[1002(value=120000)]],},
		},
		plan=1,
		progress=1280,
	},
	[4]={
		id=4,
		itemlist={
			[1]={amount=3,sid=[[13270]],},
			[2]={amount=150,sid=[[14021]],},
			[3]={amount=1,sid=[[1002(value=150000)]],},
		},
		plan=1,
		progress=3280,
	},
	[5]={
		id=5,
		itemlist={
			[1]={amount=5,sid=[[13270]],},
			[2]={amount=200,sid=[[14021]],},
			[3]={amount=1,sid=[[1002(value=200000)]],},
		},
		plan=1,
		progress=6480,
	},
	[6]={
		id=6,
		itemlist={
			[1]={amount=2,sid=[[10040]],},
			[2]={amount=50,sid=[[11001]],},
			[3]={amount=1,sid=[[1002(value=22222)]],},
		},
		plan=2,
		progress=300,
	},
	[7]={
		id=7,
		itemlist={
			[1]={amount=5,sid=[[10040]],},
			[2]={amount=100,sid=[[11001]],},
			[3]={amount=1,sid=[[1002(value=22222)]],},
		},
		plan=2,
		progress=680,
	},
	[8]={
		id=8,
		itemlist={
			[1]={amount=1,sid=[[13270]],},
			[2]={amount=120,sid=[[14021]],},
			[3]={sid=[[1002(value=22222)1]],},
		},
		plan=2,
		progress=1280,
	},
	[9]={
		id=9,
		itemlist={
			[1]={amount=3,sid=[[13270]],},
			[2]={amount=150,sid=[[14021]],},
			[3]={amount=1,sid=[[1002(value=22222)]],},
		},
		plan=2,
		progress=3280,
	},
	[10]={
		id=10,
		itemlist={
			[1]={amount=5,sid=[[13270]],},
			[2]={amount=200,sid=[[14021]],},
			[3]={amount=1,sid=[[1002(value=22222)]],},
		},
		plan=2,
		progress=6480,
	},
}

LoopPay={
	[1]={
		day=1,
		id=1,
		itemlist={
			[1]={amount=1,sid=[[1002(value=10000)]],},
			[2]={amount=1,sid=[[10040]],},
		},
		plan=1001,
	},
	[2]={
		day=2,
		id=2,
		itemlist={
			[1]={amount=1,sid=[[1002(value=20000)]],},
			[2]={amount=20,sid=[[11001]],},
			[3]={amount=15,sid=[[14001]],},
		},
		plan=1001,
	},
	[3]={
		day=3,
		id=3,
		itemlist={
			[1]={amount=1,sid=[[1002(value=40000)]],},
			[2]={amount=120,sid=[[14031]],},
		},
		plan=1001,
	},
	[4]={
		day=4,
		id=4,
		itemlist={
			[1]={amount=1,sid=[[1002(value=80000)]],},
			[2]={amount=1,sid=[[13269]],},
		},
		plan=1001,
	},
	[5]={
		day=5,
		id=5,
		itemlist={
			[1]={amount=1,sid=[[1002(value=100000)]],},
			[2]={amount=1,sid=[[13214]],},
		},
		plan=1001,
	},
	[6]={
		day=6,
		id=6,
		itemlist={
			[1]={amount=1,sid=[[1002(value=120000)]],},
			[2]={amount=1,sid=[[14103]],},
		},
		plan=1001,
	},
	[7]={
		day=7,
		id=7,
		itemlist={
			[1]={amount=1,sid=[[1002(value=150000)]],},
			[2]={amount=1,sid=[[13270]],},
		},
		plan=1001,
	},
	[8]={
		day=1,
		id=8,
		itemlist={
			[1]={amount=1,sid=[[1002(value=10000)]],},
			[2]={amount=1,sid=[[10040]],},
		},
		plan=1002,
	},
	[9]={
		day=2,
		id=9,
		itemlist={
			[1]={amount=1,sid=[[1002(value=20000)]],},
			[2]={amount=20,sid=[[11001]],},
			[3]={amount=15,sid=[[14001]],},
		},
		plan=1002,
	},
	[10]={
		day=3,
		id=10,
		itemlist={
			[1]={amount=1,sid=[[1002(value=40000)]],},
			[2]={amount=120,sid=[[14031]],},
		},
		plan=1002,
	},
	[11]={
		day=4,
		id=11,
		itemlist={
			[1]={amount=1,sid=[[1002(value=80000)]],},
			[2]={amount=1,sid=[[13269]],},
		},
		plan=1002,
	},
	[12]={
		day=5,
		id=12,
		itemlist={
			[1]={amount=1,sid=[[1002(value=100000)]],},
			[2]={amount=1,sid=[[13214]],},
		},
		plan=1002,
	},
	[13]={
		day=6,
		id=13,
		itemlist={
			[1]={amount=1,sid=[[1002(value=120000)]],},
			[2]={amount=1,sid=[[14103]],},
		},
		plan=1002,
	},
	[14]={
		day=7,
		id=14,
		itemlist={
			[1]={amount=1,sid=[[1002(value=150000)]],},
			[2]={amount=1,sid=[[13270]],},
		},
		plan=1002,
	},
}

ConsumePlan={
	[1]={
		detail={
			[1]=100001,
			[2]=100002,
			[3]=100003,
			[4]=100004,
			[5]=100005,
			[6]=100006,
			[7]=100007,
			[8]=100008,
		},
		id=1,
	},
	[2]={
		detail={
			[1]=100009,
			[2]=100010,
			[3]=100011,
			[4]=100012,
			[5]=100013,
			[6]=100014,
			[7]=100015,
			[8]=100016,
		},
		id=2,
	},
	[3]={
		detail={
			[1]=100001,
			[2]=100002,
			[3]=100003,
			[4]=100004,
			[5]=100005,
			[6]=100006,
			[7]=100007,
			[8]=100008,
		},
		id=3,
	},
	[4]={
		detail={
			[1]=100001,
			[2]=100002,
			[3]=100003,
			[4]=100004,
			[5]=100005,
			[6]=100006,
			[7]=100007,
			[8]=100008,
		},
		id=4,
	},
	[5]={
		detail={
			[1]=100001,
			[2]=100002,
			[3]=100003,
			[4]=100004,
			[5]=100005,
			[6]=100006,
			[7]=100007,
			[8]=100008,
		},
		id=5,
	},
	[6]={
		detail={
			[1]=100001,
			[2]=100002,
			[3]=100003,
			[4]=100004,
			[5]=100005,
			[6]=100006,
			[7]=100007,
			[8]=100008,
		},
		id=6,
	},
	[7]={
		detail={
			[1]=100001,
			[2]=100002,
			[3]=100003,
			[4]=100004,
			[5]=100005,
			[6]=100006,
			[7]=100007,
			[8]=100008,
		},
		id=7,
	},
	[8]={
		detail={
			[1]=100001,
			[2]=100002,
			[3]=100003,
			[4]=100004,
			[5]=100005,
			[6]=100006,
			[7]=100007,
			[8]=100008,
		},
		id=8,
	},
}

LimitPay={
	[10001]={
		condition=1000,
		id=10001,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=10000)]],},
			[2]={num=1,sid=[[10040]],},
		},
	},
	[10002]={
		condition=2000,
		id=10002,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=20000)]],},
			[2]={num=1,sid=[[14002]],},
		},
	},
	[10003]={
		condition=4000,
		id=10003,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=40000)]],},
			[2]={num=30,sid=[[14011]],},
		},
	},
	[10004]={
		condition=8000,
		id=10004,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=80000)]],},
			[2]={num=3,sid=[[10040]],},
		},
	},
	[10005]={
		condition=12000,
		id=10005,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=120000)]],},
			[2]={num=1,sid=[[13270]],},
		},
	},
	[10006]={
		condition=20000,
		id=10006,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=200000)]],},
			[2]={num=10,sid=[[10040]],},
		},
	},
	[10007]={
		condition=30000,
		id=10007,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=300000)]],},
			[2]={num=50,sid=[[14011]],},
		},
	},
	[10008]={
		condition=40000,
		id=10008,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=400000)]],},
			[2]={num=10,sid=[[14002]],},
		},
	},
	[10009]={
		condition=50000,
		id=10009,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=500000)]],},
			[2]={num=1,sid=[[13240]],},
		},
	},
	[10010]={
		condition=998,
		id=10010,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=10000)]],},
			[2]={num=1,sid=[[10040]],},
		},
	},
	[10011]={
		condition=1998,
		id=10011,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=20000)]],},
			[2]={num=1,sid=[[14002]],},
		},
	},
	[10012]={
		condition=3998,
		id=10012,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=40000)]],},
			[2]={num=30,sid=[[14011]],},
		},
	},
	[10013]={
		condition=7998,
		id=10013,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=80000)]],},
			[2]={num=3,sid=[[10040]],},
		},
	},
	[10014]={
		condition=11998,
		id=10014,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=120000)]],},
			[2]={num=1,sid=[[13270]],},
		},
	},
	[10015]={
		condition=19998,
		id=10015,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=200000)]],},
			[2]={num=10,sid=[[10040]],},
		},
	},
	[10016]={
		condition=29998,
		id=10016,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=300000)]],},
			[2]={num=50,sid=[[14011]],},
		},
	},
	[10017]={
		condition=39998,
		id=10017,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=400000)]],},
			[2]={num=10,sid=[[14002]],},
		},
	},
	[10018]={
		condition=49998,
		id=10018,
		open=1,
		reward={
			[1]={num=1,sid=[[1002(value=500000)]],},
			[2]={num=1,sid=[[13240]],},
		},
	},
}

LimitPayPlan={
	[1]={
		id=1,
		item_list={
			[1]=10001,
			[2]=10002,
			[3]=10003,
			[4]=10004,
			[5]=10005,
			[6]=10006,
			[7]=10007,
			[8]=10008,
			[9]=10009,
		},
	},
	[2]={
		id=2,
		item_list={
			[1]=10010,
			[2]=10011,
			[3]=10012,
			[4]=10013,
			[5]=10014,
			[6]=10015,
			[7]=10016,
			[8]=10017,
			[9]=10018,
		},
	},
}

PowerRank={
	[1]={
		key=1,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=103,
		reward={[1]={amount=1,sid=[[1003(value=10000)]],},},
		subtype=0,
	},
	[2]={
		key=2,
		range={lower=2,upper=3,},
		range_desc=[[2-3위]],
		rank_id=103,
		reward={[1]={amount=1,sid=[[1003(value=5000)]],},},
		subtype=0,
	},
	[3]={
		key=3,
		range={lower=4,upper=10,},
		range_desc=[[4-10위]],
		rank_id=103,
		reward={[1]={amount=1,sid=[[1003(value=4000)]],},},
		subtype=0,
	},
	[4]={
		key=4,
		range={lower=11,upper=20,},
		range_desc=[[11-20위]],
		rank_id=103,
		reward={[1]={amount=1,sid=[[1003(value=3000)]],},},
		subtype=0,
	},
	[5]={
		key=5,
		range={lower=21,upper=50,},
		range_desc=[[21-50위]],
		rank_id=103,
		reward={[1]={amount=1,sid=[[1003(value=2000)]],},},
		subtype=0,
	},
	[6]={
		key=6,
		range={lower=51,upper=100,},
		range_desc=[[51-100위]],
		rank_id=103,
		reward={[1]={amount=1,sid=[[1003(value=1000)]],},},
		subtype=0,
	},
}

PartnerRank={
	[1]={
		key=31,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20301]],},},
		subtype=301,
	},
	[2]={
		key=32,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20302]],},},
		subtype=302,
	},
	[3]={
		key=33,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20303]],},},
		subtype=303,
	},
	[4]={
		key=34,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20305]],},},
		subtype=305,
	},
	[5]={
		key=35,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20306]],},},
		subtype=306,
	},
	[6]={
		key=36,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20308]],},},
		subtype=308,
	},
	[7]={
		key=37,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20311]],},},
		subtype=311,
	},
	[8]={
		key=38,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20312]],},},
		subtype=312,
	},
	[9]={
		key=39,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20313]],},},
		subtype=313,
	},
	[10]={
		key=40,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20314]],},},
		subtype=314,
	},
	[11]={
		key=41,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20315]],},},
		subtype=315,
	},
	[12]={
		key=42,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20316]],},},
		subtype=316,
	},
	[13]={
		key=43,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20401]],},},
		subtype=401,
	},
	[14]={
		key=44,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20402]],},},
		subtype=402,
	},
	[15]={
		key=45,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20403]],},},
		subtype=403,
	},
	[16]={
		key=46,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20404]],},},
		subtype=404,
	},
	[17]={
		key=47,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20405]],},},
		subtype=405,
	},
	[18]={
		key=48,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20407]],},},
		subtype=407,
	},
	[19]={
		key=49,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20409]],},},
		subtype=409,
	},
	[20]={
		key=50,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20410]],},},
		subtype=410,
	},
	[21]={
		key=51,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20412]],},},
		subtype=412,
	},
	[22]={
		key=52,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20413]],},},
		subtype=413,
	},
	[23]={
		key=53,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20414]],},},
		subtype=414,
	},
	[24]={
		key=54,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20415]],},},
		subtype=415,
	},
	[25]={
		key=55,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20416]],},},
		subtype=416,
	},
	[26]={
		key=56,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20417]],},},
		subtype=417,
	},
	[27]={
		key=57,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20418]],},},
		subtype=418,
	},
	[28]={
		key=58,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20501]],},},
		subtype=501,
	},
	[29]={
		key=59,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20502]],},},
		subtype=502,
	},
	[30]={
		key=60,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20503]],},},
		subtype=503,
	},
	[31]={
		key=61,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20504]],},},
		subtype=504,
	},
	[32]={
		key=62,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20505]],},},
		subtype=505,
	},
	[33]={
		key=63,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20506]],},},
		subtype=506,
	},
	[34]={
		key=64,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20507]],},},
		subtype=507,
	},
	[35]={
		key=65,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20508]],},},
		subtype=508,
	},
	[36]={
		key=66,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20509]],},},
		subtype=509,
	},
	[37]={
		key=67,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20510]],},},
		subtype=510,
	},
	[38]={
		key=68,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20511]],},},
		subtype=511,
	},
	[39]={
		key=69,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20512]],},},
		subtype=512,
	},
	[40]={
		key=70,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20513]],},},
		subtype=513,
	},
	[41]={
		key=71,
		range={lower=1,upper=1,},
		range_desc=[[1위]],
		rank_id=115,
		reward={[1]={amount=80,sid=[[20514]],},},
		subtype=514,
	},
}

QQVip={
	[10001]={
		channel_id=10001,
		des=[[초보 패키지]],
		reward={
			[1]={num=1,sid=[[1003(value=388)]],},
			[2]={num=1,sid=[[1020(value=60)]],},
			[3]={num=1,sid=[[1002(value=50000)]],},
		},
		stype=[[newman]],
		title=0,
		value=200,
	},
	[10002]={
		channel_id=10002,
		des=[[슈퍼 멤버십 패키지]],
		reward={
			[1]={num=1,sid=[[1003(value=888)]],},
			[2]={num=1,sid=[[1020(value=120)]],},
			[3]={num=1,sid=[[1002(value=150000)]],},
		},
		stype=[[supervip]],
		title=0,
		value=200,
	},
	[10003]={
		channel_id=10003,
		des=[[개통/연장 슈퍼 멤버십 패키지]],
		reward={
			[1]={num=10,sid=[[10040]],},
			[2]={num=1,sid=[[1020(value=240)]],},
			[3]={num=1,sid=[[1002(value=300000)]],},
		},
		stype=[[openvip]],
		title=0,
		value=200,
	},
	[10004]={
		channel_id=10004,
		des=[[QQ 슈퍼 멤버십 패키지1]],
		reward={
			[1]={num=1,sid=[[1003(value=688)]],},
			[2]={num=1,sid=[[1002(value=50000)]],},
		},
		stype=[[specialvip1]],
		title=1084,
		value=2400,
	},
	[10005]={
		channel_id=10005,
		des=[[슈퍼 멤버십 패키지2]],
		reward={
			[1]={num=5,sid=[[13212]],},
			[2]={num=2,sid=[[14002]],},
			[3]={num=1,sid=[[1002(value=100000)]],},
		},
		stype=[[specialvip2]],
		title=0,
		value=2400,
	},
}
