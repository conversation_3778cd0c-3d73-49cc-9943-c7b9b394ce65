-- 检查数据流向脚本
require("client.convert._common")

print("=== 数据流向检查 ===")

-- 获取luadata中的所有lua文件
local function get_lua_files(dir)
    local files = {}
    local handle = io.popen('find ' .. dir .. ' -name "*.lua" -type f')
    if handle then
        for file in handle:lines() do
            local basename = file:match("([^/]+)%.lua$")
            if basename then
                table.insert(files, basename)
            end
        end
        handle:close()
    end
    return files
end

-- 获取client/convert中的转换脚本
local function get_convert_scripts()
    local scripts = {}
    local handle = io.popen('find client/convert -name "*.lua" -type f')
    if handle then
        for file in handle:lines() do
            local basename = file:match("([^/]+)%.lua$")
            if basename and basename ~= "_common" and basename ~= "_run" then
                table.insert(scripts, basename)
            end
        end
        handle:close()
    end
    return scripts
end

print("1. 检查luadata文件...")
local luadata_files = get_lua_files("luadata")
print("luadata中的文件数量:", #luadata_files)

print("\n2. 检查client/convert转换脚本...")
local convert_scripts = get_convert_scripts()
print("转换脚本数量:", #convert_scripts)

print("\n3. 检查没有转换脚本的luadata文件...")
local convert_map = {}
for _, script in ipairs(convert_scripts) do
    convert_map[script] = true
end

local no_convert = {}
for _, file in ipairs(luadata_files) do
    if not convert_map[file] then
        table.insert(no_convert, file)
    end
end

print("没有转换脚本的luadata文件:")
for _, file in ipairs(no_convert) do
    print("  -", file)
end

print("\n4. 检查client/data中的文件...")
local client_data_files = get_lua_files("client/data")
print("client/data中的文件数量:", #client_data_files)

print("\n=== 检查完成 ===")
