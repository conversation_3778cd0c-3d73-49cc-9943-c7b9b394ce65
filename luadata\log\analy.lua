-- ./excel/log/analy.xlsx
return {

    ["newaccount"] = {
        desc = "가입정보",
        log_format = {["alist"] = {["id"] = "alist", ["desc"] = "계정리스트"}, ["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}},
        subtype = "newaccount",
    },

    ["newrole"] = {
        desc = "캐릭터생성정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}, ["plist"] = {["id"] = "plist", ["desc"] = "캐릭터리스트"}},
        subtype = "newrole",
    },

    ["newdevice"] = {
        desc = "뉴디바이스정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["mlist"] = {["id"] = "mlist", ["desc"] = "디바이스리스트"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}},
        subtype = "newdevice",
    },

    ["loginact"] = {
        desc = "계정로그인정보",
        log_format = {["alist"] = {["id"] = "alist", ["desc"] = "계정리스트"}, ["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}},
        subtype = "loginact",
    },

    ["loginrole"] = {
        desc = "캐릭터로그인정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}, ["plist"] = {["id"] = "plist", ["desc"] = "캐릭터리스트"}},
        subtype = "loginrole",
    },

    ["logindev"] = {
        desc = "디바이스로그인정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["mlist"] = {["id"] = "mlist", ["desc"] = "디바이스리스트"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}},
        subtype = "logindev",
    },

    ["loginmodel"] = {
        desc = "기형로그인정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["mlist"] = {["id"] = "mlist", ["desc"] = "기형리스트"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}},
        subtype = "loginmodel",
    },

    ["online"] = {
        desc = "온라인정보",
        log_format = {["avgcnt"] = {["id"] = "avgcnt", ["desc"] = "평균접속자수"}, ["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["interval"] = {["id"] = "interval", ["desc"] = "시간대접속자수"}, ["maxcnt"] = {["id"] = "maxcnt", ["desc"] = "최대접속자수"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}},
        subtype = "online",
    },

    ["upgrade"] = {
        desc = "레벨정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}, ["plist"] = {["id"] = "plist", ["desc"] = "캐릭터정보리스트"}},
        subtype = "upgrade",
    },

    ["duration"] = {
        desc = "접속시간정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}, ["plist"] = {["id"] = "plist", ["desc"] = "캐릭터정보리스트"}},
        subtype = "duration",
    },

    ["storytask"] = {
        desc = "메인퀘스트정보",
        log_format = {["channel"] = {["id"] = "channel", ["desc"] = "마켓"}, ["platform"] = {["id"] = "platform", ["desc"] = "플랫폼"}, ["plist"] = {["id"] = "plist", ["desc"] = "캐릭터정보리스트"}},
        subtype = "storytask",
    },

}
