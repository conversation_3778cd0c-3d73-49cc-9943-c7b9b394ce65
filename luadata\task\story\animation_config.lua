-- ./excel/task/story/animation_config.xlsx
return {

    [10001] = {
        content = "프롤로그,비밀 구역!상고요족",
        delay_time = 2,
        id = 10001,
        mapId = 200100,
        show_content_time = 1,
        type = 1,
    },

    [10002] = {
        content = "제1장 출발,새로운 여정!",
        delay_time = 2,
        id = 10002,
        mapId = 101000,
        show_content_time = 1,
        type = 1,
    },

    [10003] = {
        content = "제2장 치열한 전투,무도대회!",
        delay_time = 2,
        id = 10003,
        mapId = 101000,
        show_content_time = 1,
        type = 1,
    },

    [10004] = {
        content = "제3장 수사,회생석!",
        delay_time = 2,
        id = 10004,
        mapId = 200000,
        show_content_time = 1,
        type = 1,
    },

    [10005] = {
        content = "제4장 혼돈,단혼애!",
        delay_time = 2,
        id = 10005,
        mapId = 201000,
        show_content_time = 1,
        type = 1,
    },

    [10006] = {
        content = "제5장 원죄,신원의 비밀!",
        delay_time = 2,
        id = 10006,
        mapId = 210400,
        show_content_time = 1,
        type = 1,
    },

    [10007] = {
        content = "제6장 악몽,요족의 섬!",
        delay_time = 2,
        id = 10007,
        mapId = 204000,
        show_content_time = 1,
        type = 1,
    },

    [10008] = {
        content = "제7장 공격,풍죽림!",
        delay_time = 2,
        id = 10008,
        mapId = 202000,
        show_content_time = 1,
        type = 1,
    },

    [10009] = {
        content = "제8장 전투,명운잔상!",
        delay_time = 2,
        id = 10009,
        mapId = 205000,
        show_content_time = 1,
        type = 1,
    },

    [10010] = {
        content = "제9장 귀향,환혼 장례곡!",
        delay_time = 2,
        id = 10010,
        mapId = 206000,
        show_content_time = 1,
        type = 1,
    },

}
