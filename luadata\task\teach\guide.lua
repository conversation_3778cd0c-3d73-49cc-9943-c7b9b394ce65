-- ./excel/task/teach/task.xlsx
return {

    [1] = {
        desc = "[654A33]기능[654A33]버튼을 클릭하면 [be3d3b]기능[654A33] 인터페이스가 열립니다!",
        id = 1,
        pic = "teach_00001",
    },

    [2] = {
        desc = "[654A33]업그레이드[654A33]를 클릭하시면 [be3d3b]업그레이드 스킬[654A33]이 가능합니다!",
        id = 2,
        pic = "teach_00002",
    },

    [3] = {
        desc = "[654A33]모집[654A33]버튼을 클릭하면 [be3d3b]모집[654A33] 화면이 열립니다!",
        id = 3,
        pic = "teach_00003",
    },

    [4] = {
        desc = "[654A33]를 클릭하여 [654A33]을 소환하면 [be3d3b]동료 모집[654A33]!",
        id = 4,
        pic = "teach_00004",
    },

    [5] = {
        desc = "[654A33]be3d3b]파트너[654A33]버튼을 클릭하면 [be3d3b]동료[654A33] 인터페이스가 열립니다!",
        id = 5,
        pic = "teach_00005",
    },

    [6] = {
        desc = "[654A33]육성[654A33]을 클릭하여 [be3d3b] 육성 인터페이스를 엽니다!",
        id = 6,
        pic = "teach_00006",
    },

    [7] = {
        desc = "[654A33]be3d3b]동료[654A33]버튼을 클릭하면 [be3d3b]룬석[654A33] 인터페이스가 열립니다!",
        id = 7,
        pic = "teach_00007",
    },

    [8] = {
        desc = "[654A33]룬석을 선택[654A33]하여 [be3d3b]를 클릭하여 착용[654A33]!",
        id = 8,
        pic = "teach_00008",
    },

    [9] = {
        desc = "[654A33] 저택[654A33] 을 클릭하면 [be3d3b] 저택[654A33] 인터페이스가 열립니다!",
        id = 9,
        pic = "teach_00009",
    },

    [10] = {
        desc = "[654A33]를 클릭하여 [654A33]을 소환하면 [be3d3b]동료 모집[654A33]!",
        id = 10,
        pic = "teach_00010",
    },

    [11] = {
        desc = "[654A33]친구[654A33]버튼을 클릭하여 [be3d3b]공간을 엽니다!",
        id = 11,
        pic = "teach_00011",
    },

    [12] = {
        desc = "[654A33] 개인정보[654A33]을 편집하면 [be3d3b] 보너스[654A33]를 받을 수 있습니다!",
        id = 12,
        pic = "teach_00012",
    },

    [13] = {
        desc = "[654A33] 길드[654A33] 버튼을 클릭하여 [be3d3b] 길드[654A33] 인터페이스를 엽니다!",
        id = 13,
        pic = "teach_00013",
    },

    [14] = {
        desc = "[654A33]일괄가입[654A33]을 클릭하시면 [be3d3b] 길드[654A33]에 가입하실 수 있습니다!",
        id = 14,
        pic = "teach_00014",
    },

    [15] = {
        desc = "[654A33]가방[654A33]을 열고[be3d3b]성상도[654A33]를 사용하세요!",
        id = 15,
        pic = "teach_00015",
    },

    [16] = {
        desc = "[654A33] 나침반 가이드[654A33]에 따라 [be3d3b] 보물[654A33]을 발굴하세요!",
        id = 16,
        pic = "teach_00016",
    },

    [17] = {
        desc = "[654A33]비무장 [654A33]버튼을 클릭하면 [be3d3b]티어비무장 [654A33] 인터페이스가 열립니다!",
        id = 17,
        pic = "teach_00017",
    },

    [18] = {
        desc = "[654A33]be3d3b]버튼[654A33]을 클릭하시면 [be3d3b]티어비무에 들어가실 수 있습니다!",
        id = 18,
        pic = "teach_00018",
    },

    [19] = {
        desc = "[654A33]모집[654A33]버튼을 클릭하면 [be3d3b]모집[654A33] 화면이 열립니다!",
        id = 19,
        pic = "teach_00019",
    },

    [20] = {
        desc = "[be3d3b]소환[654A33]클릭[654A33]즉시[be3d3b]동료모집[654A33]!",
        id = 20,
        pic = "teach_00020",
    },

    [21] = {
        desc = "[be3d3b]파트너[654A33]버튼[654A33]을 클릭하여[be3d3b]파트너[654A33] 인터페이스를 엽니다!",
        id = 21,
        pic = "teach_00021",
    },

    [22] = {
        desc = "[be3d3b] [654A33] 착용한 [be3d3b]를 선택하고 [654A33] 강화 [654A33]를 클릭하세요.",
        id = 22,
        pic = "teach_00022",
    },

    [23] = {
        desc = "[654A33]이벤트 입구[654A33]를 클릭하여 [be3d3b]퀴즈[654A33] 인터페이스로 이동합니다!",
        id = 23,
        pic = "teach_00023",
    },

    [24] = {
        desc = "[654A33]이벤트 인터페이스에서 [be3d3b] 퀴즈 참여[654A33]!",
        id = 24,
        pic = "teach_00024",
    },

    [25] = {
        desc = "[654A33]모집[654A33]버튼을 클릭하면 [be3d3b]모집[654A33] 화면이 열립니다!",
        id = 25,
        pic = "teach_00025",
    },

    [26] = {
        desc = "[654A33]를 클릭하여 [654A33]을 소환하면 [be3d3b]파트너 모집[654A33]!",
        id = 26,
        pic = "teach_00026",
    },

    [27] = {
        desc = "[654A33]메인 인터페이스 [be3d3b]수행 [654A33]버튼을 클릭하여 [be3d3b]수행[654A33] 인터페이스를 엽니다!",
        id = 27,
        pic = "teach_00027",
    },

    [28] = {
        desc = "[654A33]를 클릭하여 수행을 시작하시면 됩니다!",
        id = 28,
        pic = "teach_00028",
    },

    [29] = {
        desc = "[654A33]be3d3b 백팩[654A33]에서 [be3d3b]경화수월[654A33아이템을 사용하세요!",
        id = 29,
        pic = "teach_00029",
    },

    [30] = {
        desc = "[654A33]선택 [be3d3b]목표몬스터[654A33] 전투에 돌입!",
        id = 30,
        pic = "teach_00030",
    },

    [31] = {
        desc = "[654A33]장비[654A33]버튼을 클릭하면 [be3d3b]장비[654A33] 인터페이스가 열립니다!",
        id = 31,
        pic = "teach_00031",
    },

    [32] = {
        desc = "[654A33]보석[654A33]을 선택하여 박음질 클릭!",
        id = 32,
        pic = "teach_00032",
    },

    [33] = {
        desc = "[654A33]야외지도[654A33]로 이동하여 [be3d3b]탐색[654A33]버튼을 클릭합니다.",
        id = 33,
        pic = "teach_00033",
    },

    [34] = {
        desc = "[654A33]탐색[654A33]을 클릭하여 탐색을 시작합니다!",
        id = 34,
        pic = "teach_00034",
    },

    [35] = {
        desc = "[654A33]장비[654A33]버튼을 클릭하시면 [be3d3b]장비-취령[654A33]인터페이스가 열립니다!",
        id = 35,
        pic = "teach_00035",
    },

    [36] = {
        desc = "[654A33]를 클릭하여 [654A33]을 재설정하면 [be3d3b] 장비 취령을 진행할 수 있습니다!",
        id = 36,
        pic = "teach_00036",
    },

    [37] = {
        desc = "[654A33]장비[654A33]버튼을 클릭하면 [be3d3b]장비-돌파[654A33] 인터페이스가 열립니다!",
        id = 37,
        pic = "teach_00037",
    },

    [38] = {
        desc = "[654A33]돌파[654A33]를 누르시면 [be3d3b]장비돌파[654A33]가 가능합니다!",
        id = 38,
        pic = "teach_00038",
    },

    [39] = {
        desc = "[654A33]be3d3b]파트너[654A33]버튼을 클릭하면 [be3d3b]부문[654A33] 인터페이스가 열립니다!",
        id = 39,
        pic = "teach_00039",
    },

    [40] = {
        desc = "[654A33]룬석을 선택[654A33]하여 [be3d3b]를 클릭하여 착용합니다[654A33]!",
        id = 40,
        pic = "teach_00040",
    },

    [41] = {
        desc = "[654A33] 가방[654A33] 버튼을 클릭하면 [be3d3b] 장비[654A33] 인터페이스가 열립니다!",
        id = 41,
        pic = "teach_00041",
    },

    [42] = {
        desc = "[654A33]장비[654A33]를 선택하여 [be3d3b]를 클릭하여 [654A33]을 교체합니다!",
        id = 42,
        pic = "teach_00042",
    },

    [43] = {
        desc = "[654A33] 야외 [be3d3b]다과회NPC[654A33]를 클릭하여 [be3d3b]NPC 대화[654A33] 인터페이스를 엽니다!",
        id = 43,
        pic = "teach_00043",
    },

    [44] = {
        desc = "[654A33] 전투[654A33]를 클릭하여 [be3d3b] 야옹이 다과회에 도전합니다!",
        id = 44,
        pic = "teach_00044",
    },

    [45] = {
        desc = "[654A33]업적[654A33]을 클릭하여[be3d3b]업적[654A33]인터페이스를 엽니다!",
        id = 45,
        pic = "teach_00045",
    },

    [46] = {
        desc = "[654A33]임의 5개[654A33]업적[be3d3b]을 달성하면 상을 받을 수 있습니다!",
        id = 46,
        pic = "teach_00046",
    },

    [47] = {
        desc = "[654A33] 도감[654A33] 버튼을 클릭하면 [be3d3b] 세계도감[654A33] 인터페이스가 열립니다!",
        id = 47,
        pic = "teach_00047",
    },

    [48] = {
        desc = "be3d3b]동료를[654A33]수집하여[be3d3b]월드스토리를[654A33]해제하세요[654A33]!",
        id = 48,
        pic = "teach_00048",
    },

    [49] = {
        desc = "[654A33] 도감[654A33] 버튼을 클릭하여 [be3d3b] 인물도감[654A33] 인터페이스를 엽니다!",
        id = 49,
        pic = "teach_00049",
    },

    [50] = {
        desc = "[654A33]다른 지도[654A33]로 가면[be3d3b]인물[654A33]에 도전할 수 있습니다.",
        id = 50,
        pic = "teach_00050",
    },

    [51] = {
        desc = "[654A33]be3d3b]원정[654A33]버튼을 클릭하면 [be3d3b]원정[654A33] 인터페이스가 열립니다!",
        id = 51,
        pic = "teach_00051",
    },

    [52] = {
        desc = "[be3d3b]동료[654A33]선택[654A33]즉시[be3d3b]원정시작[654A33]!",
        id = 52,
        pic = "teach_00052",
    },

    [53] = {
        desc = "[be3d3b]공평비무[654A33]버튼[654A33]클릭하여[be3d3b]비무[654A33]인터페이스 오픈",
        id = 53,
        pic = "teach_00053",
    },

    [54] = {
        desc = "[be3d3b]버튼[654A33]클릭[654A33]즉시[be3d3b]비무입장[654A33]!",
        id = 54,
        pic = "teach_00054",
    },

    [55] = {
        desc = "[be3d3b]모험[654A33]버튼을[654A33]클릭하여[be3d3b]이벤트[654A33]창 오픈!",
        id = 55,
        pic = "teach_00055",
    },

    [56] = {
        desc = "[be3d3b]제도NPC扳尾[654A33]로이동[654A33]즉시[be3d3b]던전진입[654A33]!",
        id = 56,
        pic = "teach_00056",
    },

    [57] = {
        desc = "[be3d3b]동료[654A33]버튼[654A33]클릭하여[be3d3b]동료[654A33]인터페이스 오픈!",
        id = 57,
        pic = "teach_00057",
    },

    [58] = {
        desc = "[be3d3b]각성아이템[654A33]수집[654A33]시[be3d3b]동료각성가능[654A33]!",
        id = 58,
        pic = "teach_00058",
    },

    [59] = {
        desc = "[654A33] 모험[654A33] 버튼을 클릭하여 [be3d3b] 이벤트[654A33] 인터페이스를 엽니다!",
        id = 59,
        pic = "teach_00059",
    },

    [60] = {
        desc = "[654A33]제도NPC모[654A33]로 가시면 됩니다[be3d3b]던전으로 입장하실 수 있습니다!",
        id = 60,
        pic = "teach_00060",
    },

    [61] = {
        desc = "[654A33] 저택[654A33] 버튼을 클릭하고 [be3d3b] 저택[654A33] 인터페이스에서 조리대를 클릭!",
        id = 61,
        pic = "teach_00061",
    },

    [62] = {
        desc = "[654A33]셰프 대화[654A33]를 클릭하여[be3d3b]요리 만들기[654A33]!",
        id = 62,
        pic = "teach_00062",
    },

    [63] = {
        desc = "[654A33] 저택 조리대[654A33] 클릭, [be3d3b] 만든 선물받기!",
        id = 63,
        pic = "teach_00063",
    },

    [64] = {
        desc = "[654A33]저택파트너에게 선물을 드래그하여 드리면[654A33]선물완료[654A33]!",
        id = 64,
        pic = "teach_00064",
    },

    [65] = {
        desc = "[654A33]be3d3b]동료[654A33] 인터페이스를 열고 [be3d3b]룬석 선택[654A33]을 클릭하여 합성합니다!",
        id = 65,
        pic = "teach_00065",
    },

    [66] = {
        desc = "[654A33]합성할 룬석을 선택하시면 [654A33]합성 시작하실 수 있습니다!",
        id = 66,
        pic = "teach_00066",
    },

    [500] = {
        desc = "[654A33]모집[654A33]버튼을 클릭하여 [be3d3b]모집[654A33] 화면을 엽니다",
        id = 500,
        pic = "teach_00003",
    },

    [501] = {
        desc = "[654A33]소환카드[654A33]를 클릭하시면 [be3d3b]동료 모집을 진행하실 수 있습니다.",
        id = 501,
        pic = "teach_00004",
    },

    [510] = {
        desc = "[654A33]모험[654A33]버튼을 클릭하면 [be3d3b]모험[654A33]이 열립니다.",
        id = 510,
        pic = "teach_00510",
    },

    [511] = {
        desc = [=[[654A33]이벤트[654A33]에서 [be3d3b]이공 유배[654A36]를 찾습니다.
[654A33]※보상: [3b6cbe]파트너룬석]=],
        id = 511,
        pic = "teach_00511",
    },

    [520] = {
        desc = "[654A33]메인 인터페이스 [be3d3b]저택[654A33]버튼을 클릭하면 [be3d3b]저택[654A33]이 열립니다.",
        id = 520,
        pic = "teach_00520",
    },

    [521] = {
        desc = "[654A33] 저택을 많이 [654A33]뒤지다보면[be3d3b]기묘한 일[654A33]이 생깁니다.",
        id = 521,
        pic = "teach_00521",
    },

    [530] = {
        desc = "[654A33] 메뉴 [be3d3b] 길드[654A33] 버튼을 클릭하면 [be3d3b] 길드[654A33] 인터페이스가 열립니다.",
        id = 530,
        pic = "teach_00013",
    },

    [531] = {
        desc = "[654A33]일괄가입[654A33]을 클릭하여 [be3d3b] 길드에 가입합니다.",
        id = 531,
        pic = "teach_00014",
    },

    [540] = {
        desc = "[654A33]메인 인터페이스 [be3d3b]모험 [654A33]버튼을 클릭하면 [be3d3b]모험[654A33]이 열립니다.",
        id = 540,
        pic = "teach_00510",
    },

    [541] = {
        desc = "[654A33]이벤트[654A33] 탭에서 [be3d3b]파트너 원정[654A36] 탭을 찾습니다.",
        id = 541,
        pic = "teach_00541",
    },

    [550] = {
        desc = "[654A33]메인 인터페이스 [be3d3b]모험 [654A33]버튼을 클릭하면 [be3d3b]모험[654A33]이 열립니다.",
        id = 550,
        pic = "teach_00510",
    },

    [551] = {
        desc = [=[[654A33]이벤트[654A33]에서 [be3d3b]야옹이 다과회[654A36]를 찾았습니다.
[654A33]※보상: [3b6cbe]파트너 조각, 파트너 룬석]=],
        id = 551,
        pic = "teach_00551",
    },

    [560] = {
        desc = [=[[654A33] 메뉴 [be3d3b] 지하감옥[654A33] 버튼을 클릭하여 [be3d3b] 지하감옥[654A33]으로 이동합니다.
]=],
        id = 560,
        pic = "teach_00560",
    },

    [561] = {
        desc = [=[[654A33]몬스터[654A33]를 클릭하시면 [be3d3b]전투에 들어가실 수 있습니다.
[654A33]※보상: [3b6cbe]금화, 훈장, 취령석, 강화석, 보석]=],
        id = 561,
        pic = "teach_00561",
    },

    [570] = {
        desc = "[654A33]비무장[654A33]버튼을 클릭하여 [be3d3b]비무장[654A33]으로 이동합니다.",
        id = 570,
        pic = "teach_00017",
    },

    [571] = {
        desc = [=[[654A33]"전"[654A33]"을 클릭하면 [be3d3b]이 비무를 진행할 수 있습니다.
[654A33]※보상: [3b6cbe]영예,수정,스킨권]=],
        id = 571,
        pic = "teach_00018",
    },

    [580] = {
        desc = "[654A33]点击主界面[be3d3b]冒险[654A33]按钮，打开[be3d3b]冒险[654A33]",
        id = 580,
        pic = "teach_00510",
    },

    [581] = {
        desc = [=[[654A33]全天活动[654A33]中找到[be3d3b]埋骨之地[654A36]
[654A33]※产出：[3b6cbe]人物装备、宝石、强化石、淬灵石]=],
        id = 581,
        pic = "teach_00581",
    },

    [590] = {
        desc = "[654A33]点击主界面[be3d3b]修行[654A33]按钮，打开[be3d3b]修行[654A33]",
        id = 590,
        pic = "teach_00027",
    },

    [591] = {
        desc = [=[[654A33]点击[be3d3b]开始修行[654A33]即可
[654A33]※产出：[3b6cbe]人物经验、金币、鲜肉包]=],
        id = 591,
        pic = "teach_00028",
    },

    [600] = {
        desc = "[654A33]点击主界面[be3d3b]冒险[654A33]按钮，打开[be3d3b]冒险[654A33]",
        id = 600,
        pic = "teach_00510",
    },

    [601] = {
        desc = [=[[654A33]在[be3d3b]全天活动[654A33]中找到[be3d3b]探索[654A36]
[654A33]※产出：[3b6cbe]人物经验、水晶、金币、伙伴碎片]=],
        id = 601,
        pic = "teach_00601",
    },

    [610] = {
        desc = "[654A33]点击[be3d3b]“伙伴”-“N”[654A33]界面，找到[be3d3b]鲜肉包[654A33]",
        id = 610,
        pic = "teach_00611",
    },

    [611] = {
        desc = "[654A33]鲜肉包[654A33]：[be3d3b]培育[654A36]中，将它喂给其他伙伴，伙伴能获得大量经验",
        id = 611,
        pic = "teach_00610",
    },

    [620] = {
        desc = "[654A33]玩家之间的[be3d3b]切磋[654A33]只可在[be3d3b]擂台[654A33]上进行",
        id = 620,
        pic = "teach_00620",
    },

    [621] = {
        desc = "[654A33]擂台[be3d3b]位于[654A33]帝都[be3d3b]（12，13）[654A36]",
        id = 621,
        pic = "teach_00621",
    },

}
