@echo off
echo ===========================================
echo 客户端数据目录调试脚本
echo ===========================================

echo.
echo 1. 检查client目录结构:
if exist "client" (
    echo client目录存在
    dir client /b
) else (
    echo ERROR: client目录不存在！
    goto end
)

echo.
echo 2. 检查client\data目录:
if exist "client\data" (
    echo client\data目录存在
    echo 文件列表:
    dir "client\data" /b | head -20
    echo.
    echo 文件总数:
    dir "client\data" /b | find /c /v ""
) else (
    echo WARNING: client\data目录不存在！
    echo 尝试创建目录...
    mkdir "client\data"
    if exist "client\data" (
        echo 目录创建成功
    ) else (
        echo ERROR: 无法创建目录
    )
)

echo.
echo 3. 检查client\convert目录:
if exist "client\convert" (
    echo client\convert目录存在
    echo 转换脚本数量:
    dir "client\convert\*.lua" /b | find /c /v ""
) else (
    echo ERROR: client\convert目录不存在！
)

echo.
echo 4. 检查luadata目录:
if exist "luadata" (
    echo luadata目录存在
    echo 数据文件数量:
    dir "luadata\*.lua" /b | find /c /v ""
) else (
    echo WARNING: luadata目录不存在！
)

echo.
echo 5. 检查最近修改的client\data文件:
if exist "client\data" (
    echo 最近修改的5个文件:
    forfiles /p "client\data" /m *.lua /c "cmd /c echo @path @fdate @ftime" 2>nul | sort /r | head -5
)

:end
echo.
echo 调试完成
pause
