-- ./excel/task/shimen/taskdialog.xlsx
return {

    [1] = {
        content = "백 할머니가 버섯이 좀 필요한데 같이 채집하실래요?",
        dialog_id = 60001,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [2] = {
        content = "저를 도와 버섯을 채집해주셔서 감사합니다",
        dialog_id = 60001,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [3] = {
        content = "이런 큰 버섯을,정말 고맙습니다!",
        dialog_id = 60001,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [4] = {
        content = "나비야,아름다운 나비야,갖고 싶어,냥~",
        dialog_id = 60002,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [5] = {
        content = "꽃 수확을 도와주셔서 감사합니다",
        dialog_id = 60002,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [6] = {
        content = "정보과는 명계에 관한 기록이 적으니 당신이 가서 조사해 주세요!",
        dialog_id = 60003,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [7] = {
        content = "잘했습니다!",
        dialog_id = 60003,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [8] = {
        content = "야옹이 다과회가 곧 시작됩니다.올해 특별 손님은 집양이니,풍죽림으로 가서 그에게 초팀장을 전하세요.",
        dialog_id = 60004,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [9] = {
        content = "다과회,다과회,맛있는 게 많은 다과회,갈게요!",
        dialog_id = 60004,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [10] = {
        content = "정찰과 수사는 모든 신병의 필수 스킬로 외근과로 가서 추무에게 정찰 훈련을 수료할 수 있습니다.",
        dialog_id = 60005,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [11] = {
        content = "지금까지 올해 신입생의 훈련 결과는 매우 좋습니다,열심히 노력하세요!",
        dialog_id = 60005,
        finish_event = "",
        last_action = {{["content"] = "특훈 시작", ["event"] = "F10011"}, {["content"] = "다시 준비", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [12] = {
        content = "내 검은콩 건어물을 누가 훔쳐간 거야!",
        dialog_id = 60006,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [13] = {
        content = "건어물의 쫄깃함과 검은콩의 향은 그야말로 세계 최고!",
        dialog_id = 60006,
        finish_event = "",
        last_action = {{["content"] = "건어물 건네기", ["event"] = "F10021"}, {["content"] = "사람을 잘못 봤어", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [14] = {
        content = "닭!아침에 닭이 없어졌어요!도와주세요",
        dialog_id = 60007,
        finish_event = "TARGET40004",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [15] = {
        content = "드디어 찾았다,함께 집에 가자!",
        dialog_id = 60007,
        finish_event = "",
        last_action = {},
        next = "2",
        pre_id_list = "40004,0",
        status = 1,
        subid = 2,
        type = 4,
        ui_mode = 2,
        voice = 0,
    },

    [16] = {
        content = "꼬꼬꼬...",
        dialog_id = 60007,
        finish_event = "STARTTRACE,COMMIT,_DESC2",
        last_action = {},
        next = "0",
        pre_id_list = "40004,0",
        status = 1,
        subid = 3,
        type = 5,
        ui_mode = 2,
        voice = 0,
    },

    [17] = {
        content = "우리 아기가 돌아왔네요,정말 고마워요,이 계란을 줄게요!",
        dialog_id = 60007,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 4,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [18] = {
        content = "영월은 자주 설연이 보고싶다고 했는데 유명진으로 가서 따다줄 수 있나요?",
        dialog_id = 60011,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [19] = {
        content = "설연",
        dialog_id = 60011,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [20] = {
        content = "어,연화가 어디서 ,저한테 주시는 거예요,고마워요!녹리에게 가져가 보여주겠어요.",
        dialog_id = 60011,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [21] = {
        content = "비용은 관상 대나무를 재배하고 싶어하지만 이런 대나무는 풍죽림에만 있습니다.",
        dialog_id = 60012,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [22] = {
        content = "죽순",
        dialog_id = 60012,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [23] = {
        content = "제두에서 찾고 싶어도 찾을 곳이 없으니,이 죽순은 제가 받을게요!",
        dialog_id = 60012,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [24] = {
        content = "원대부는 버섯이 필요한데 절 도와서 채집할 수 있나요?",
        dialog_id = 60013,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [25] = {
        content = "저를 도와 버섯을 채집해주셔서 감사합니다",
        dialog_id = 60013,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [26] = {
        content = "바로 이런 버섯입니다!이젠 맛있는 불고리를 할 수 있겠네요!",
        dialog_id = 60013,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [27] = {
        content = "함어비는 최근에 나에게 명반이 있는지 물으러 왔던데 이 물건이 왜 필요한거죠?",
        dialog_id = 60014,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [28] = {
        content = "저를 도와 버섯을 채집해주셔서 감사합니다",
        dialog_id = 60014,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [29] = {
        content = "수원을 정화할 수 있습니다",
        dialog_id = 60014,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [30] = {
        content = "리사는 설연의 모습을보고 주님이 그녀의 소원을 이뤄주길 바랍니다.",
        dialog_id = 60015,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [31] = {
        content = "설연",
        dialog_id = 60015,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [32] = {
        content = "연화!주님께서 내 기도를 들으신 겁니다!",
        dialog_id = 60015,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [33] = {
        content = "아침에 간 의사는 단혼애에서만 발견되는 갈철광을 판매하고 있는지 물었습니다.",
        dialog_id = 60016,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [34] = {
        content = "설연",
        dialog_id = 60016,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [35] = {
        content = "갈철광!이것으로 지혈약 한 냄비를 만들 수 있습니다.",
        dialog_id = 60016,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [36] = {
        content = "올해는 모래폭풍이 잦아서 연은 광굴의 수원 부근에 대나무를 재배해 풍사를 막고 싶습니다.",
        dialog_id = 60017,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [37] = {
        content = "죽순",
        dialog_id = 60017,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [38] = {
        content = "좋아요,바로 심어줄게요!",
        dialog_id = 60017,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [39] = {
        content = "만수사화,피안화,유명의 불,내 동생은 분명히 좋아할 겁니다!",
        dialog_id = 60018,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [40] = {
        content = "죽순",
        dialog_id = 60018,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [41] = {
        content = "아무 이유 없이 남에게 꽃을 주다니,참~",
        dialog_id = 60018,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [42] = {
        content = "등주예는 항상 피안화를 원했는데 내가 형으로서 어떻게 꽃 한 송이를 안 주겠어!",
        dialog_id = 60019,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [43] = {
        content = "죽순",
        dialog_id = 60019,
        finish_event = "STARTPICK",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [44] = {
        content = "명...명계에 있어야 할 물건을 어떻게 당신이 갖고 있죠,말도 안 돼요!",
        dialog_id = 60019,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [45] = {
        content = "제두에서 북으로 가면 눈이 내리는 곳이 있는데 자색 꽃이 자라고 있습니다.",
        dialog_id = 60021,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [46] = {
        content = "꽃 수확을 도와주셔서 감사합니다",
        dialog_id = 60021,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [47] = {
        content = "부활초는 더이상 광석의 도시에서 수확할 수 없으니 단혼애에서 찾을 수 있습니다.",
        dialog_id = 60022,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [48] = {
        content = "꽃 수확을 도와주셔서 감사합니다",
        dialog_id = 60022,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [49] = {
        content = "영월은 나비를 원합니다,어서 절 도와 나비 한 마리를 잡아주세요!",
        dialog_id = 60023,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [50] = {
        content = "꽃 수확을 도와주셔서 감사합니다",
        dialog_id = 60023,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [51] = {
        content = "성당에서는 희귀한 품종의 나비를 배양했는데,샘플 하나라도 가지고 싶네요.",
        dialog_id = 60024,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [52] = {
        content = "꽃 수확을 도와주셔서 감사합니다",
        dialog_id = 60024,
        finish_event = "_FP",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [53] = {
        content = "외근과의 보고에 의하면 월견도에서 요족의 종적을 발견했고 통수부는 당신을 순찰에 파견했습니다.",
        dialog_id = 60031,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [54] = {
        content = "잘했습니다!",
        dialog_id = 60031,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [55] = {
        content = "최근 잦은 유민습격이 팔문촌 주민들에 의해 보고되었으니 당신이 가서 이 사건을 처리하세요.",
        dialog_id = 60032,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [56] = {
        content = "잘했습니다!",
        dialog_id = 60032,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [57] = {
        content = "외근과는 풍죽림에 깊이 관여할 수 없기에 당신이 가서 시도해 보세요.",
        dialog_id = 60033,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [58] = {
        content = "잘했습니다!",
        dialog_id = 60033,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [59] = {
        content = "무도대회에 참가한 실종자에 대해서는 아직 단서가 없고,총수부에서 단혼애 재수사에 당신을 파견했습니다.",
        dialog_id = 60034,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [60] = {
        content = "잘했습니다!",
        dialog_id = 60034,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [61] = {
        content = "광석도시로 파견을 받은 조사인원들이 송환되었고 통수부는 신병들을 파견하기로 했습니다.",
        dialog_id = 60035,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [62] = {
        content = "잘했습니다!",
        dialog_id = 60035,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [63] = {
        content = "유명진의 질서는 아직 정상으로 돌아오지 않았으며 통수부는 당신에게 그것을 진압하도록 지정했습니다.",
        dialog_id = 60036,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [64] = {
        content = "잘했습니다!",
        dialog_id = 60036,
        finish_event = "_PATROL",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [65] = {
        content = "중급팀이 몇 가지 단서를 찾았고 수사에 협조하려면 광석의 도시의 봉의 협조가 필요합니다,통수부에 이 소환장을 전해주세요.",
        dialog_id = 60041,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [66] = {
        content = "하루 종일 조사해도 끝이 안 납니다!",
        dialog_id = 60041,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [67] = {
        content = "팔문촌의 묘옥수가 제작한 옷이 이미 완료 되었으니 절 도와 가져다 주세요,아잉~",
        dialog_id = 60042,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [68] = {
        content = "내 옷이 드디어 도착했네요,지금 당장 입고 싶어요!",
        dialog_id = 60042,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [69] = {
        content = "외근과는 단혼애에서 중요한 단서를 찾았습니다,정보를 즉시 교염에게 전해주세요!",
        dialog_id = 60043,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [70] = {
        content = "잘했어요!이 정보는 우리에게 매우 중요합니다.",
        dialog_id = 60043,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [71] = {
        content = "성당은 단혼애 촌민들의 기도를 받았기에 이 복음을 그들에게 전해주세요.",
        dialog_id = 60044,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [72] = {
        content = "신부님이 우리를 보호하기 위해 기사단을 보낼까요?",
        dialog_id = 60044,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [73] = {
        content = "영월 언니를 안 본지 오래 됐는데 언제쯤 절 보러 올까요?",
        dialog_id = 60045,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [74] = {
        content = "풍죽림의 집양?어쩌면 다시는 안 돌아갈지도.",
        dialog_id = 60045,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [75] = {
        content = "어제 이상한 옷 차림의 언니가 떨어트린 물건을 주웠는데 절 대신 가져다주세요,히히히~",
        dialog_id = 60046,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [76] = {
        content = "말하는 게시판?인간 세계는 정말 위험하네요!",
        dialog_id = 60046,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [77] = {
        content = "망천의 하,유명의 아들,지금은 어떨지?",
        dialog_id = 60047,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [78] = {
        content = "봉주야학,저와 그는 만난 적 있나요?",
        dialog_id = 60047,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [79] = {
        content = "인내심은 모든 신병들의 필수 자질이니 팔문촌의 판다 할아버지에게 내성 훈련을 받으세요.",
        dialog_id = 60051,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [80] = {
        content = "평화로운 마음,인내심은 버프에 없어서는 안 됩니다.",
        dialog_id = 60051,
        finish_event = "",
        last_action = {{["content"] = "특훈 시작", ["event"] = "F10012"}, {["content"] = "다시 준비", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [81] = {
        content = "근거리 전투는 모든 신병의 필수 스킬로 정명을 찾아 경연장으로 가서 훈련하세요.",
        dialog_id = 60052,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [82] = {
        content = "무도대회의 우승자 아닌가요?화이팅!",
        dialog_id = 60052,
        finish_event = "",
        last_action = {{["content"] = "특훈 시작", ["event"] = "F10013"}, {["content"] = "다시 준비", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [83] = {
        content = "현장 응급 처치는 모든 신병의 필수 스킬로 간 의사를 찾아 응급 처치를 훈련하세요.",
        dialog_id = 60053,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [84] = {
        content = "전장에서의 부상은 피할수 없으며 모든 신병은 응급 처치를 배워야 합니다.",
        dialog_id = 60053,
        finish_event = "",
        last_action = {{["content"] = "특훈 시작", ["event"] = "F10014"}, {["content"] = "다시 준비", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [85] = {
        content = "광야에서의 생존은 모든 신병의 필수 스킬이니 야옹이를 찾아 요리 훈련을 하세요!",
        dialog_id = 60054,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [86] = {
        content = "야옹이의 찐빵은 야외에서 먹을 수 없어요",
        dialog_id = 60054,
        finish_event = "",
        last_action = {{["content"] = "특훈 시작", ["event"] = "F10018"}, {["content"] = "다시 준비", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [87] = {
        content = "어제 시립 감옥에서 탈옥이 있었고 통수부는 탈옥자를 되찾기 위해 당신을 유명진으로 파견했습니다.",
        dialog_id = 60061,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [88] = {
        content = "내 탈옥 경험을 영화로 만들 수 있습니다!",
        dialog_id = 60061,
        finish_event = "",
        last_action = {{["content"] = "나와 함께 돌아가요", ["event"] = "F10022"}, {["content"] = "사람을 잘못 봤어", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [89] = {
        content = "누가 내 찐빵을 훔쳤어요!어서 잡아주세요~",
        dialog_id = 60062,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [90] = {
        content = "만두,호빵,팥빵",
        dialog_id = 60062,
        finish_event = "",
        last_action = {{["content"] = "찐빵 내놓기", ["event"] = "F10023"}, {["content"] = "사람을 잘못 봤어", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [91] = {
        content = "오늘 아침에 곰패가 또 길을 잃었다는 것을 알았습니다. 찾도록 도와 주시겠습니까?",
        dialog_id = 60063,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [92] = {
        content = "으르렁,으르렁,으르렁~~~",
        dialog_id = 60063,
        finish_event = "",
        last_action = {{["content"] = "나와 함께 돌아와", ["event"] = "F10024"}, {["content"] = "사람을 잘못 봤어", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [93] = {
        content = "오늘 아침에 마을 밖에서 요족 활동을 발견했는데,통수부가 우리를 포기한 것일까요?",
        dialog_id = 60064,
        finish_event = "",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [94] = {
        content = "꽥~꽥~인류,반드시 떠나!",
        dialog_id = 60064,
        finish_event = "",
        last_action = {{["content"] = "사람들을 다치게 하지마", ["event"] = "F10025"}, {["content"] = "지나치다", ["event"] = "포기"}},
        next = "0",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [95] = {
        content = "오늘 바쁘다 보니 야옹이가 사라진 것을 알았습니다.찾아줄 수 있나요?",
        dialog_id = 60071,
        finish_event = "TARGET40009",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [96] = {
        content = "드디어 찾았다,함께 집에 가자!",
        dialog_id = 60071,
        finish_event = "STARTTRACE,COMMIT,_DESC2",
        last_action = {},
        next = "2",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 1,
        ui_mode = 2,
        voice = 0,
    },

    [97] = {
        content = "우리 아가,보고 싶었어!고맙습니다.",
        dialog_id = 60071,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [98] = {
        content = "풍죽림 전체를 찾았지만 화화를 발견하지 못했는데,어디로 간 거죠?",
        dialog_id = 60072,
        finish_event = "TARGET40010",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 2,
        subid = 1,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

    [99] = {
        content = "드디어 찾았다,함께 집에 가자!",
        dialog_id = 60072,
        finish_event = "STARTTRACE,COMMIT,_DESC2",
        last_action = {},
        next = "2",
        pre_id_list = "0",
        status = 1,
        subid = 2,
        type = 1,
        ui_mode = 2,
        voice = 0,
    },

    [100] = {
        content = "어디 간거지,바깥세상은 위험한데!",
        dialog_id = 60072,
        finish_event = "DONE",
        last_action = {},
        next = "0",
        pre_id_list = "0",
        status = 5,
        subid = 3,
        type = 2,
        ui_mode = 2,
        voice = 0,
    },

}
