-- 测试客户端转换脚本
require("client.convert._common")

print("=== 测试客户端数据转换 ===")

-- 测试SaveToFile函数
local test_data = "TEST_DATA = {\n    [1] = {id = 1, name = 'test'},\n    [2] = {id = 2, name = 'test2'}\n}"

print("测试SaveToFile函数...")
SaveToFile("test", test_data)

-- 测试一个简单的模块
print("\n测试global模块...")
local success, global_module = pcall(function()
    return require("client.convert.global")
end)

if success and global_module.main then
    print("执行global模块...")
    local r1, r2 = pcall(function()
        global_module.main()
    end)
    if not r1 then
        print("ERROR:", r2)
    else
        print("SUCCESS: global模块执行成功")
    end
else
    print("ERROR: 无法加载global模块")
end

print("\n=== 测试完成 ===")
