module(..., package.seeall)
function main()
	print("DEBUG: global模块开始执行")

	local success, d1 = pcall(function()
		return require("global")
	end)

	if not success then
		print("ERROR: 无法加载global数据:", d1)
		return
	end

	print("DEBUG: global数据加载成功，数据项数量:", d1 and #d1 or "未知")

	local s = table.dump(d1, "GLOBAL")
	print("DEBUG: global数据转换完成，字符串长度:", string.len(s))

	SaveToFile("global", s)
	print("DEBUG: global模块执行完成")
end
