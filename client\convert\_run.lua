--执行所有客户端导表
OTHER_PATH = ...
require("client.convert._common")

print("=== 开始客户端数据转换 ===")
print("OTHER_PATH:", OTHER_PATH)
print("当前工作目录:", os.getenv("PWD") or "未知")

-- 检查是否强制更新
local FORCE_UPDATE = os.getenv("FORCE_UPDATE") == "1"
print("强制更新模式:", FORCE_UPDATE)

-- 如果启用强制更新，修改SaveToFile函数
if FORCE_UPDATE then
    local original_SaveToFile = SaveToFile
    SaveToFile = function(filename, s)
        return original_SaveToFile(filename, s, true)
    end
    print("已启用强制更新模式")
end

local list ={
	"global",
	"globalcontrol",
	"item",
	"map",
	"chat",
	"patrol",
	"scene",
	"upgrade",
	"audio",
	"task",
	"reward",
	"roletype",
	"randomname",
	"npcshop",
	"pay",
	"school",
	"schedule",
	"autoteam",
	"team",
	"charge",
	"skill",
	"buff",
	"maskword",
	"worldboss",
	"treasure",
	"loginreward",
	"magic",
	"npc",
	"model",
	"partner",
	"partnerequip",
	"house",
	"arena",
	"equalarena",
	"rank",
	"org",
	"tollgate",
	"monster",
	"endlesspve",
	"help",
	"title",
	"equipfuben",
	"pefuben",
	"teach",
	"openui",
	"playconfig",
	"trapmine",
	"friend",
	"pata",
	"huodong_block",
	"achieve",
	"mapbook",
	"guide",
	"terrawar",
	"foretell",
	"yjfuben",
	"travel",
	"forge",
	"lilian",
	"spine",
	"minglei",
	"sociality",
	"fieldboss",
	"welfare",
	"onlinegift",
	"convoy",
	"chapterfuben",
	"teampvp",
	"sceneexam",
	"msattack",
	"roleskin",
	"hunt",
	"clubarena",
	"marry",
	"servergrade",
}

print("总共需要处理的模块数量:", #list)

for i, name in ipairs(list) do
	print(string.format("=== [%d/%d] 开始处理模块: %s ===", i, #list, name))

	local success, m = pcall(function()
		return require("client.convert."..name)
	end)

	if not success then
		print("ERROR: 无法加载模块", name, "错误:", m)
		goto continue
	end

	print("模块加载成功:", name, "main函数存在:", m.main ~= nil)

	if m.main then
		local r1, r2 = pcall(function ()
			print("开始执行", name, "的main函数...")
			m.main()
			print("完成执行", name, "的main函数")
		end)
		if not r1 then
			print("ERROR: 执行", name, "模块失败:", r2)
		else
			print("SUCCESS: 模块", name, "执行成功")
		end
	else
		print("WARNING: 模块", name, "没有main函数")
	end

	::continue::
end

print("=== 客户端数据转换完成 ===")
